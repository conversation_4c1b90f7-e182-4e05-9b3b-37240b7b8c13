#include "stdafx.h"
#include "svr_part_material.h"

#include "svr_json_helper.h"

namespace svr_data
{
	bool SvrPartMaterial::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		IF_ERROR_RETURN(GetJsonValue(jv["editPartId"], EditPartId));
		IF_ERROR_RETURN(GetJsonValue(jv["editPartName"], EditPartName));
		IF_ERROR_RETURN(GetJsonValue(jv["materialId"], MaterialId));
		IF_ERROR_RETURN(GetJsonValue(jv["flag"], Flag));

		return true;
	}

}
