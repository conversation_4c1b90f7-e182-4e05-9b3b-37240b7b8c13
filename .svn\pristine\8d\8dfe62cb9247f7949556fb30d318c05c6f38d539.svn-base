﻿#include "stdafx.h"
#include "HttpAsyncManage.h"
#include "../parse_json/BaseFactory.h"
#include <sstream>  
#include <fstream>
#include "decoration_vr_interface.h"
#include "download_assist.h"

#ifdef RENDER_SKETCHER_HTML
#include "../decoration_vr_callback.h"
using namespace decoration_vr_interface;
#endif

#include "Util.h"

namespace svr_data
{
	HttpAysncManage::HttpAysncManage()
	{

	}
	HttpAysncManage::HttpAysncManage(const rse::string& addrIP, const rse::string& svcName)
	{
		m_sAddrIP = addrIP;
		m_sSvcName = svcName;
	}
	HttpAysncManage::~HttpAysncManage()
	{

	}

	// 先组装成Json后，进行序列化，再转utf8_string
	//主要对照 DataVisitorContext ，是对它的组装

	rse::string HttpAysncManage::DataSerializeToUtf8String(int64 nLibraryId, int32_t nLangId, int64 nUserId, int64 npartId)
	{
		Json::Value dvc;
		dvc["LibraryId"] = nLibraryId;
		dvc["LangId"] = nLangId;
		dvc["UserId"] = nUserId;
		Json::Value obj;
		obj["partId"] = npartId;
		obj["dvc"] = dvc;
		return ToString(obj);
	}

	rse::string HttpAysncManage::DataSerializeToUtf8String(int64 nLibraryId, int32_t nLangId, int64 nUserId, Json::Value& obj)
	{
		Json::Value dvc;
		dvc["LibraryId"] = nLibraryId;
		dvc["LangId"] = nLangId;
		dvc["UserId"] = nUserId;

		obj["dvc"] = dvc;
		return ToString(obj);
	}

	//使用异步方式，发送组装好的Json 
	//与 “无参” 构造函数 配合使用
	//参数说明： sAddrIP = “http://localhost:28753”（ip地址）
	//参数说明： sSvcName = /SDSExhibitDataService.svc/ （所在服务目录）
	//参数说明： sResouceName = LoadCar（资源名称）
	//参数说明： sSerialize = DataSerializeToUtf8String 返回的序列化字符串
	bool HttpAysncManage::SendRequest(Json::Value &root, rse::string sAddrIP, rse::string sSvcName, rse::string sResouceName, rse::string sSerialize)
	{
		auto download_url = sAddrIP + sSvcName + sResouceName;
		m_sResouceName = sResouceName;
		return RequestData(root, download_url, sSerialize);
	}
	//使用异步方式，发送组装好的Json 
	//与 “有参” 构造函数 配合使用
	//参数说明： sResouceName = LoadCar（资源名称）
	//参数说明： sSerialize = DataSerializeToUtf8String 返回的序列化字符串
	bool HttpAysncManage::SendRequest(Json::Value &root, rse::string sResouceName, rse::string sSerialize)
	{
		auto download_url = decoration_vr_interface::Util::CombinePath(
			decoration_vr_interface::Util::CombinePath(m_sAddrIP, m_sSvcName),
			sResouceName, 
			TSTR("/"));
		m_sResouceName = sResouceName;
		return RequestData(root, download_url, sSerialize);

		//try
		//{

		//	//m_sResouceName = sResouceName;
		//	//utility::string_t wsAddr = conversions::utf8_to_utf16(m_sAddrIP);
		//	//rse::string sChildAddr = m_sSvcName + sResouceName;
		//	//utility::string_t wsLinkAddr = conversions::utf8_to_utf16(sChildAddr);
		//	//http::uri u_AddrIP = http::uri(wsAddr);
		//	//http_client client(u_AddrIP);
		//	//auto urlString = conversions::utf16_to_utf8(uri_builder(u_AddrIP).append_path(wsLinkAddr).to_string());
		//	//auto content = sSerialize;
		//	//auto contentType = utf8string("application/json");

		//	////ErrorMsgAndLog* pEma  = new ErrorMsgAndLog();
		//	////rse::string tmp =  pEma->SetErrorInfo(Err_NetLink_Failed);
		//	////std::wcout << tmp.c_str();

		//	//return client.request(methods::POST, urlString, content, contentType).then([](http_response response)->pplx::task<json::value>
		//	//{
		//	//	if (response.status_code() == status_codes::OK)
		//	//	{
		//	//		return response.extract_json();
		//	//	}
		//	//	return pplx::task_from_result(json::value());
		//	//}).then([](pplx::task<json::value> previousTask)
		//	//{

		//	//	return previousTask;
		//	//	//HttpAysncManage::GetClassData(jv.as_object(), sResouceName);
		//	//});
		//	return pplx::task_from_result(json::value());
		//}
		//catch (const std::exception&)
		//{
		//	//日志错误输出
		//	SetErrorMsg(Err_NetLink_Failed);
		//	RecordLog("SendRequest2", g_sRtnErrMsg);
		//}
	}

	bool HttpAysncManage::RequestData(Json::Value &root, const rse::string &download_url, rse::string &sSerialize)
	{
		auto call_back = GetVrCallback();
		auto data_buf = call_back->DownloadData(download_url.c_str(), sSerialize.c_str());
		auto d = rse::string(data_buf);
		
		call_back->FreeBuf(data_buf);

		Json::CharReaderBuilder reader_builder;
		reader_builder["collectComments"] = false;
		auto reader = reader_builder.newCharReader();

		JSONCPP_STRING err;
		auto ret = reader->parse(d.data(), d.data() + d.size(), &root, &err);
		rs_delete(reader);

		return ret;
	}

	decoration_vr_interface::IDecorationVrCallBack* HttpAysncManage::GetVrCallback()
	{
		return static_cast<decoration_vr_interface::DecorationVrInterface*>(
			decoration_vr_interface::GetDecorationVrInterface())->GetVrCallBack();
	}

	//使用 非 异步方式，发送组装好的Json
	//与 “有参” 构造函数 配合使用
	//json::value HttpAysncManage::SendRequestA(rse::string sResouceName, rse::string sSerialize)
	//{
	//	try
	//	{
	//		//m_sResouceName = sResouceName;
	//		//utility::string_t wsAddr = conversions::utf8_to_utf16(m_sAddrIP);
	//		//rse::string sChildAddr = m_sSvcName + sResouceName;
	//		//utility::string_t wsLinkAddr = conversions::utf8_to_utf16(sChildAddr);
	//		//http::uri u_AddrIP = http::uri(wsAddr);
	//		//http_client client(u_AddrIP);
	//		//auto urlString = conversions::utf16_to_utf8(uri_builder(u_AddrIP).append_path(wsLinkAddr).to_string());
	//		//auto content = sSerialize;
	//		//auto contentType = "application/json";

	//		//http_response response = client.request(methods::POST, urlString, content, contentType).get();
	//		//if (response.status_code() == status_codes::OK)
	//		//{
	//		//	json::value jv = response.extract_json().get();
	//		//	auto content = jv.serialize();
	//		//	//std::wcout << content << std::endl;
	//		//	return jv;
	//		//}
	//		return json::value();
	//	}
	//	catch (const std::exception ex)
	//	{
	//		//std::cout << ex.what() << std::endl;
	//		//日志错误输出
	//		SetErrorMsg(Err_NetLink_Failed);
	//		RecordLog("SendRequestA", g_sRtnErrMsg);
	//	}
	//}

	IDataBasePtr HttpAysncManage::GetClassData(Json::Value& jv)
	{
		try
		{
			auto is_null = jv.isNull();
			if (is_null)
			{
				return nullptr;
			}

			IFactory businessFactory;
			auto ct = businessFactory.CreateParseJsonObj(m_sResouceName);
			//auto node_name = m_sResouceName + "Result";
			auto node_name = "data";
			//{
			//	std::ofstream out("D:\\a.json");
			//	if (out)
			//	{
			//		Json::StreamWriterBuilder builder;
			//		builder["commentStyle"] = "None";
			//		builder["indentation"] = "   ";  // or whatever you like
			//		std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
			//		writer->write(jv, &out);
			//		out.close();
			//	}
			//}
			if (ct->Parse(jv[node_name]))
			{
				return ct;
			}
		}
		catch (const std::exception&)
		{

		}
		return nullptr;
	}

	//文件下载,返回数据流
	//参数说明： sFilePath = E:\VS2015_c++\Rest_Api_QDB
	//参数说明： sFileName = nodepad
	//参数说明： sFileType = .exe
	//bool HttpAysncManage::DownloadFileStream(rse::string sFilePath, rse::string sFileName, rse::string sFileType)
	//{
	//	return DownloadFileStream(sFilePath, sFileName + sFileType);
	//	//rse::string rtnData = "";
	//	//try
	//	//{
	//	//	/*rse::string sAddr = sFilePath.append("\\") + sFileName.append(".") + sFileType;
	//	//	utility::string_t wsAddr = conversions::utf8_to_utf16(sAddr);
	//	//	http::uri u_AddrIP = http::uri(wsAddr);
	//	//	http_client client(u_AddrIP);
	//	//	http_response response = client.request(methods::GET).get();
	//	//	if (response.status_code() == status_codes::OK)
	//	//	{
	//	//		concurrency::streams::stringstreambuf buffer;
	//	//		response.body().read_to_end(buffer).get();
	//	//		return buffer.collection();
	//	//	}*/
	//	//	
	//	//}
	//	//catch (const std::exception&)
	//	//{
	//	//	//日志错误输出
	//	//	SetErrorMsg(Err_DownloadFileStream_Failed);
	//	//	RecordLog("DownloadFileStream3", g_sRtnErrMsg);
	//	//}
	//	//return rtnData;
	//}

	//文件下载,返回数据流
	//参数说明： sFilePath = E:\VS2015_c++\Rest_Api_QDB
	//参数说明： sFile = nodepad.exe
	//bool HttpAysncManage::DownloadFileStream(rse::string sFilePath, rse::string sFile)
	//{

	//	//rse::string rtnData = "";
	//	//try
	//	//{
	//	//	/*rse::string sAddr = sFilePath.append("\\") + sFile;
	//	//	utility::string_t wsAddr = conversions::utf8_to_utf16(sAddr);
	//	//	http::uri u_AddrIP = http::uri(wsAddr);
	//	//	http_client client(u_AddrIP);
	//	//	http_response response = client.request(methods::GET).get();
	//	//	if (response.status_code() == status_codes::OK)
	//	//	{
	//	//		concurrency::streams::stringstreambuf buffer;
	//	//		response.body().read_to_end(buffer).get();
	//	//		return buffer.collection();
	//	//	}*/
	//	//}
	//	//catch (const std::exception&)
	//	//{
	//	//	//日志错误输出
	//	//	SetErrorMsg(Err_DownloadFileStream_Failed);
	//	//	RecordLog("DownloadFileStream2", g_sRtnErrMsg);
	//	//}
	//	//return rtnData;
	//}

	//文件下载,返回数据流
	//参数说明： sFilePath = E:\VS2015_c++\Rest_Api_QDB\nodepad.exe
	bool HttpAysncManage::DownloadFileStream(const rse::string &download_url, const rse::string &local_abs_path, const rse::string &file_digital)
	{
		auto cb = GetVrCallback();
		auto need_download = decoration_vr_interface::GlobalInfoDataCommon::Instance()->GetDownLoadAssist()->AddDownloadInfo(download_url, local_abs_path, file_digital);
		if(need_download)
		{
#ifdef RENDER_SKETCHER_HTML
			return static_cast<DecorationVrCallBack*>(cb)->DownloadFile(download_url.c_str(), local_abs_path.c_str(), file_digital.c_str());
#else
			return cb->DownloadFile(download_url.c_str(), local_abs_path.c_str()); // , file_digital.c_str());
#endif
		}
		else
		{
			//don't need to download, it has been in downloading file list.
			return true;
		}

	}

	////把数据写入文件
	////参数说明： sFilePath = E:\VS2015_c++\Rest_Api_QDB\nodepad.exe
	////参数说明： sData = DownloadFileStream 返回的数据流
	//bool HttpAysncManage::WriteToFile(rse::string sFilePath, rse::string sData)
	//{
	//	try
	//	{
	//		std::ofstream file;
	//		file.open(sFilePath, std::ios::out | std::ios::binary);
	//		if (!file.good())
	//		{
	//			return false;
	//		}
	//		file.write(sData.c_str(), sData.size());
	//		file.close();
	//	}
	//	catch (std::exception&)
	//	{
	//		//日志错误输出
	//		SetErrorMsg(Err_WriteToFile_Failed);
	//		RecordLog("WriteToFile", g_sRtnErrMsg);
	//		return false;
	//	}

	//	return true;
	//}

	////把数据写入文件
	////参数说明： sFilePath = E:\VS2015_c++\Rest_Api_QDB
	////参数说明： sFile = nodepad.exe
	////参数说明： sData = DownloadFileStream 返回的数据流
	//bool HttpAysncManage::WriteToFile(rse::string sFilePath, rse::string sFile, rse::string sData)
	//{
	//	try
	//	{
	//		rse::string filePath = sFilePath.append("\\") + sFile;
	//		std::ofstream file;
	//		file.open(filePath, std::ios::out | std::ios::binary);
	//		if (!file.good())
	//		{
	//			return false;
	//		}
	//		file.write(sData.c_str(), sData.size());
	//		file.close();
	//	}
	//	catch (std::exception&)
	//	{
	//		//日志错误输出
	//		SetErrorMsg(Err_WriteToFile_Failed);
	//		RecordLog("WriteToFile", g_sRtnErrMsg);
	//		return false;
	//	}

	//	return true;
	//}

	rse::string HttpAysncManage::ToString(Json::Value &val)
	{
		Json::StreamWriterBuilder wbuilder;
		wbuilder["indentation"] = "\t";
		rse::string document = Json::writeString(wbuilder, val);
		return document;
	}

}