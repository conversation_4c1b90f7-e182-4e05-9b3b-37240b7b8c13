#ifndef _HALL_ROOM_PART_FACTORY_H_
#define _HALL_ROOM_PART_FACTORY_H_

#include "IElevatorPartFactory.h"

#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{
	class HallRoomPartFactory :public IElevatorPartFactory
	{
	public:
		HallRoomPartFactory(int part_type);
		virtual ~HallRoomPartFactory();

		void SetPartType(int ty);
		virtual int GetPartType() override;

		virtual IElevatorPartPtr CreatePart(FactoryArgs* param) override;

	protected:
		virtual void SetPartInfo(IElevatorPart* part, svr_data::SvrCommonPart* svr_part);
		virtual void SetMaterial(IElevatorPart* part, const  rse::vector<svr_data::SvrPartMaterial>& mats);
	protected:
		IElevatorPartPtr GetPartFromJsonConfig(FactoryArgs* args);
		IElevatorPartPtr GetPartFromJsonObject(FactoryArgs* args);

	protected:
		int part_type_;
	};
}

#endif//_HALL_ROOM_PART_FACTORY_H_

