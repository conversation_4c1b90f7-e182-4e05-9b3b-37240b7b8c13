#include "stdafx.h"
#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	DGBaseVisitor::DGBaseVisitor(IDGSceneEx* scene, const tchar* arr_name)
		: arr_name_(arr_name)
	{
//		scene->SetIsLoaded(true);

		visitor_ = scene->GetSceneDataReceiver(arr_name);
	}

	DGBaseVisitor::~DGBaseVisitor()
	{
	}

	void DGBaseVisitor::init(IDGSceneEx* scene, const tchar* arr_name)
	{
		arr_name_ = arr_name;
		if (scene)
		{
			visitor_ = scene->GetSceneDataReceiver(arr_name);
		}
	}

	int DGBaseVisitor::AddRow()
	{
		return visitor_->AddRow();
	}

	int DGBaseVisitor::GetRowCount()
	{
		return visitor_->GetRowCount();
	}

	void DGBaseVisitor::DeleteRow(int row)
	{
		visitor_->DeleteRow(row);
	}

	void DGBaseVisitor::Clear()
	{
		visitor_->Clear();
	}


	void DGBaseVisitor::PrintData(const tchar* file_name)
	{
		if (visitor_)
		{
			visitor_->PrintData(file_name);
		}
	}

}