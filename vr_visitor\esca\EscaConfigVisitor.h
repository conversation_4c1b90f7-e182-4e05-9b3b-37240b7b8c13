#ifndef _VR_EscaConfigVisitor_H_
#define _VR_EscaConfigVisitor_H_

#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class VrEscalatorConfigVisitor : public BaseVisitor
	{
	public:
		VrEscalatorConfigVisitor();
		virtual ~VrEscalatorConfigVisitor();

		DEFINE_CREATE_FUN(VrEscalatorConfigVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	};
}

#endif //endif
