﻿//
//  Sel_Accessory.h
//  VrVisitor
//
//  Created by vrprg on 10:25:05.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Accessory : public DGBaseVisitor
{
public:
	Sel_Accessory(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name){}

	bool GetSelectable(int row);
	void SetSelectable(int row, bool enable);
	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	float GetWidth(int row);
	void SetWidth(int row, float val);

	float GetHeight(int row);
	void SetHeight(int row, float val);

	float GetDoorWidth(int row);
	void SetDoorWidth(int row, float val);
};

class Sel_Hung_Part : public DGBaseVisitor
{
public:
	Sel_Hung_Part(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name){}

	bool GetSelectable(int row);
	void SetSelectable(int row, bool enable);
	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	float GetOffsetX(int row);
	void SetOffsetX(int row, float val);

	float GetOffsetY(int row);
	void SetOffsetY(int row, float val);

	int GetLocation(int row);
	void SetLocation(int row, int val);

	int GetPartType(int row);
	void SetPartType(int row, int val);

	int GetExtra1(int row);
	void SetExtra1(int row, int val);
};
}