#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Mirror;

class VrMirrorVisitor : public BaseVisitor
{
public:
	VrMirrorVisitor();
	~VrMirrorVisitor();

	DEFINE_CREATE_FUN(VrMirrorVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_Mirror> model_;
};
}
