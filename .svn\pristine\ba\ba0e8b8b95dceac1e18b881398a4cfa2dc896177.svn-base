#ifndef _VR_HALL_WALL_VISITOR_H_
#define _VR_HALL_WALL_VISITOR_H_

#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrHallWallVisitor :public BaseVisitor
	{
	public:
		VrHallWallVisitor();
		virtual ~VrHallWallVisitor();

		DEFINE_CREATE_FUN(VrHallWallVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	private:
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif//_VR_HALL_WALL_VISITOR_H_

