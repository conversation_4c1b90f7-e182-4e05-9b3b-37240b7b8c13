#pragma once

#include "DGBaseVisitor.h"
namespace decoration_vr_interface
{
	class Sel_HallDoor_Imported : public DGBaseVisitor
	{
	public:
		Sel_HallDoor_Imported(IDGSceneEx* scene, const tchar* arr_name);
			
		bool Get_Select(int row);
		void Set_Select(int row, bool val);

		rse::string Get_Path(int row);
		void Set_Path(int row, const tchar* val);

		//int Get_Mark(int row);
		//void Set_Mark(int row, int val);
	};
}

