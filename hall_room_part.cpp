#include "stdafx.h"
#include "hall_room_part.h"
#include "GlobalInfoDataCommon.h"

#include "file_pool.h"

namespace decoration_vr_interface
{
	HallRoomPart::HallRoomPart()
		:part_type_(-1), part_id_(-1), part_name_(TSTR("")), is_reflect_(false)
	{
	}


	HallRoomPart::~HallRoomPart()
	{
	}

	int HallRoomPart::GetPartType()
	{
		return part_type_;
	}

	int64 HallRoomPart::GetPartId()
	{
		return part_id_;
	}

	int64 HallRoomPart::GetHallDoorCount()
	{
        return hall_door_count_;
	}

	tstring HallRoomPart::GetPartName()
	{
		return part_name_;
	}

	void HallRoomPart::SetPartName(const tstring &new_name)
	{
		if (part_name_ != new_name)
		{
			part_name_ = new_name;
		}
	}

	tstring HallRoomPart::LoadModel()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(GetPartType(), GetPartId(), size_id);
	}

	bool HallRoomPart::IsValidPart()
	{
		auto gd = GlobalInfoDataCommon::Instance();
		bool is_material_part = gd->GetPartTypeManager()->IsMaterialPart(part_type_);

		if (!is_material_part && part_id_ <= 0)
		{
			gd->LogErrorLn("CommonPart isn't valid part: type=%d, part id < 0", part_type_);
			return false;
		}

		for (auto it = editpart_materials_.begin(), ie = editpart_materials_.end(); it != ie; ++it)
		{
			if (it->second <= 0)
			{
				gd->LogErrorLn("CommonPart's material isn't valid part: type=%d, part id=%ld, material id for edit part id=%d less 0",
					part_type_, part_id_, it->first);
				return false;
			}
		}

		return true;
	}

	int64 HallRoomPart::GetPartMaterial()
	{
		return GetEditPartMaterial(1);
	}

	void HallRoomPart::SetPartMaterial(int64 mat)
	{
		SetEditPartMaterial(1, mat);
	}

	int64 HallRoomPart::GetEditPartMaterial(int id)
	{
		auto it = editpart_materials_.find(id);
		if (it != editpart_materials_.end())
		{
			return it->second;
		}

		return -1;
	}

	bool HallRoomPart::SetEditPartMaterial(int id, int64 mat)
	{
		auto it = editpart_materials_.find(id);
		if (it != editpart_materials_.end())
		{
			it->second = mat;
			return true;
		}
		return false;
	}

	bool HallRoomPart::GetIsHasReflect()
	{
		return is_reflect_;
	}

	void HallRoomPart::SetIsHasReflect(const bool &is_reflect)
	{
		is_reflect_ = is_reflect;
	}
}
