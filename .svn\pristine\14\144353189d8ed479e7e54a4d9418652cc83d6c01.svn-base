#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_HILcd;

class VrHallIndicatorDisplayVisitor : public BaseVisitor
{
public:
	VrHallIndicatorDisplayVisitor();
	~VrHallIndicatorDisplayVisitor();

	DEFINE_CREATE_FUN(VrHallIndicatorDisplayVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_HILcd> model_;
};
}
