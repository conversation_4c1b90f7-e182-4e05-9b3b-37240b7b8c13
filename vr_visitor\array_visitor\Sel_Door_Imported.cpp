#include "stdafx.h"
#include "Sel_Door_Imported.h"

namespace decoration_vr_interface
{
	Sel_Door_Imported::Sel_Door_Imported(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name)
	{

	}

	bool Sel_Door_Imported::Get_Select(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Door_Imported::Set_Select(int row, bool val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	rse::string Sel_Door_Imported::Get_Path(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Door_Imported::Set_Path(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	//int Sel_Door_Imported::Get_Mark(int row)
	//{
	//	return visitor_->GetElementInt(row, 2);
	//}

	//void Sel_Door_Imported::Set_Mark(int row, int val)
	//{
	//	visitor_->SetElementValue(row, 2, val);
	//}

}
