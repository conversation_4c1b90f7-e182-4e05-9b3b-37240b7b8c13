#include "stdafx.h"
#include <set>
#include "car_wall_part.h"

#include "file_pool.h"

namespace decoration_vr_interface
{

	CarWallPart::CarWallPart()
		:part_type_(-1), part_id_(-1), part_name_(TSTR("")), width_(0), height_(0), is_reflect_(false)
	{

	}

	CarWallPart::~CarWallPart()
	{

	}

	int CarWallPart::GetPartType()
	{
		return part_type_;
	}

	int64 CarWallPart::GetPartId()
	{
		return part_id_;
	}

	tstring CarWallPart::GetPartName()
	{
		return part_name_;
	}

	void CarWallPart::SetPartName(const tstring &new_name)
	{
		if (part_name_ != new_name)
		{
			part_name_ = new_name;
		}
	}

	tstring CarWallPart::LoadModel()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(GetPartType(), GetPartId(), size_id);
	}

	bool CarWallPart::IsValidPart()
	{
		auto gd = GlobalInfoDataCommon::Instance();

		if (part_id_ <= 0)
		{
			gd->LogErrorLn("CarWall isn't valid part: type=%d, part id < 0", part_type_);
			return false;
		}

		if (width_ <= 0 || height_ <= 0)
		{
			gd->LogErrorLn("CarWall isn't valid part: type=%d, part id=%ld, size of wall is less 0",
				part_type_, part_id_);
			return false;
		}

		if (elems_.empty())
		{
			gd->LogErrorLn("CarWall isn't valid part: type=%d, part id=%ld, there aren't elements in wall at all",
				part_type_, part_id_);
			return false;
		}

		return true;
	}

	int CarWallPart::GetWidth()
	{
		return width_;
	}

	int CarWallPart::GetHeight()
	{
		return height_;
	}

	int64 CarWallPart::GetWallMaterial()
	{
		rse::set<int64> mats;
		for (auto it = elems_.begin(), ie = elems_.end(); it != ie; ++it)
		{
			auto ptr = it->get();
			if (ptr->marker_ <= WALLELEM_MARK_STANDARD_MAX || ptr->marker_ == WALLELEM_MARK_REARWALL_MIRRORMATERIAL)
			{
				mats.insert(ptr->material_id_);
			}
		}

		if (mats.size() == 1)
			return *(mats.begin());
		return -1;
	}

	void CarWallPart::SetWallMaterial(int64 mat)
	{
		for (auto it = elems_.begin(), ie = elems_.end(); it != ie; ++it)
		{
			auto ptr = it->get();
			if (ptr->marker_ <= WALLELEM_MARK_STANDARD_MAX || ptr->marker_ == WALLELEM_MARK_REARWALL_MIRRORMATERIAL)
			{
				ptr->material_id_ = mat;
			}
		}
	}

	int CarWallPart::GetWallElemCount()
	{
		int c = 0;
		for (auto it = elems_.begin(), ie = elems_.end(); it != ie; ++it)
		{
			auto ptr = it->get();
			if (ptr->marker_ <= WALLELEM_MARK_STANDARD_MAX || ptr->marker_ == WALLELEM_MARK_REARWALL_MIRRORMATERIAL)
			{
				++c;
			}
		}

		return c;
	}

	bool CarWallPart::GetIsHasReflect()
	{
		return is_reflect_;
	}

	void CarWallPart::SetIsHasReflect(const bool &is_reflect)
	{
		is_reflect_ = is_reflect;
	}

}
