//
//  Sel_CopLcd.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_CopLcd : public DGBaseVisitor
{
public:
	Sel_CopLcd(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	bool Getm_bool_Select(int row);
	void Setm_bool_Select(int row, bool val);

	rse::string Getm_str_Path(int row);
	void Setm_str_Path(int row, const tchar* val);

	int Getm_int_Mark(int row);
	void Setm_int_Mark(int row, int val);

};
}