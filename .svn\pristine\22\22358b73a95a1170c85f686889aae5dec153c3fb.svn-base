#include "stdafx.h"
#include "Sel_Car_Shell.h"

namespace decoration_vr_interface
{
	Sel_Car_Shell::Sel_Car_Shell(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name)
	{

	}

	bool Sel_Car_Shell::Get_Select(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Car_Shell::Set_Select(int row, bool val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	rse::string Sel_Car_Shell::Get_Path(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Car_Shell::Set_Path(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	//int Sel_Car_Shell::Get_Mark(int row)
	//{
	//	return visitor_->GetElementInt(row, 2);
	//}

	//void Sel_Car_Shell::Set_Mark(int row, int val)
	//{
	//	visitor_->SetElementValue(row, 2, val);
	//}

	Sel_Hall_Door_Window::Sel_Hall_Door_Window(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name)
	{

	}

	bool Sel_Hall_Door_Window::GetIsHave(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Hall_Door_Window::SetIsHave(int row, bool val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	rse::string Sel_Hall_Door_Window::GetPath(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Hall_Door_Window::SetPath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	int Sel_Hall_Door_Window::GetGlassDoorType(int row)
	{
		return visitor_->GetElementInt(row, 2);
	}

	void Sel_Hall_Door_Window::SetGlassDoorType(int row, int val)
	{
		visitor_->SetElementValue(row, 2, val);
	}
}
