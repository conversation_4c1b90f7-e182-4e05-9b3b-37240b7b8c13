#ifndef _VR_EscaHandrailLightingVisitor_H_
#define _VR_EscaHandrailLightingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Handrail_Lighting.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaHandrailLightingVisitor : public BaseVisitor
	{
	public:
		VrEscaHandrailLightingVisitor();
		virtual ~VrEscaHandrailLightingVisitor();

		DEFINE_CREATE_FUN(VrEscaHandrailLightingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Handrail_Lighting> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif