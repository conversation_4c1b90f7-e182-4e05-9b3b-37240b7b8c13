#include "stdafx.h"
#include "VrCarIndicatorVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_CarIndicator.h"
#include "material_channel.h"
#include "electric_part.h"

namespace decoration_vr_interface
{

	VrCarIndicatorVisitor::VrCarIndicatorVisitor()
	{
        part_type_ = PartTypeCarIndicator;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		InitializeAvailablePartTypes(types);

	} 

	VrCarIndicatorVisitor::~VrCarIndicatorVisitor()
	{
	}


	void VrCarIndicatorVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_CarIndicator>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), part_type_info->material_array_.c_str());
		}
	}

	bool VrCarIndicatorVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (model_->GetRowCount() < 1)
		{
			model_->AddRow();
		}

		auto indicator_part = static_cast<ElectricPart*>(vr_change->config_->GetPart(part_type_));
		rse::vector<MaterialInfo> material_infos;

		if (indicator_part)
		{
			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
			auto model_file = indicator_part->LoadModel();
			auto path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(indicator_part->GetPartType(), indicator_part->GetPartId(), model_file, path);

			model_->SetIsHave(0, true);
			model_->SetLocation(0, indicator_part->GetSetupOrientation());
			model_->SetPath(0, path.c_str());

			auto material = indicator_part->GetPartMaterial();
			if (material != 0)
			{
				MaterialInfo info(material, 1, 0);
				material_infos.push_back(info);
			}
		}
		else
		{
			model_->SetIsHave(0, false);
		}

		auto res = VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), part_type_, material_infos, indicator_part->GetIsHasReflect());
		return res >= 0;
	}

	void VrCarIndicatorVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);

		material_->PrintData(file_name);
	}

}
    