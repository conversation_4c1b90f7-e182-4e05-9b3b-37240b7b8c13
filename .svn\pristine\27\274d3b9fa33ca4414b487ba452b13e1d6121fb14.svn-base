#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_Step_Parameter : public DGBaseVisitor
		{
		public:
			Global_Step_Parameter(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			float GetRung_Size_X1(int row);
			void SetRung_Size_X1(int row, float val);

			float GetRung_Size_X2(int row);
			void SetRung_Size_X2(int row, float val);

			float GetRung_Size_Y(int row);
			void SetRung_Size_Y(int row, float val);

		};
	}

}