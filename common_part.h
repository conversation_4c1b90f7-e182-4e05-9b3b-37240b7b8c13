﻿#ifndef _COMMON_PART_H_
#define _COMMON_PART_H_

#include "IElevatorPart.h"

namespace decoration_vr_interface
{
	class CommonPart :public IElevatorPart
	{
		friend class CommonPartFactory;
	public:
		CommonPart();
		virtual ~CommonPart();

		virtual int GetPartType() override;
		virtual int64 GetPartId() override;

		virtual tstring GetPartName() override;
		virtual void SetPartName(const tstring &new_name) override;

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		int64 GetPartMaterial();
		void SetPartMaterial(int64 mat);

		int64 GetEditPartMaterial(int editpart_id);
		bool SetEditPartMaterial(int editpart_id, int64 mat);

		rse::vector<int>* GetEditPartsPtr() { return &editparts_; }

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

		int GetSetupPos() { return setup_pos_; }
		void SetSetupPos(int val) { setup_pos_ = val; }

		float GetPosX() { return pos_x_; }
		void SetPosX(float x) { pos_x_ = x; }
		float GetPosY() { return pos_y_; }
		void SetPosY(float y) { pos_y_ = y; }

		void ReplaceEditMaterials(CommonPart* src);
	protected:
		int part_type_;

		int64 part_id_;

		tstring part_name_;

		int setup_pos_;
		float pos_x_;
		float pos_y_;

		rse::vector<int> editparts_;
		rse::map<int, int64> editpart_materials_;

		bool is_reflect_;
	};

	typedef std::shared_ptr<CommonPart> CommonPartPtr;
}

#endif//_COMMON_PART_H_