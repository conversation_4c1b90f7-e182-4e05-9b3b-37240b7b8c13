#include "stdafx.h"
#include "Global_Camera_EffectParameter.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Global_Camera_EffectParameter::GetNumber(int row)
		{
			return visitor_->GetElementInt(row, 0);
		}

		void Global_Camera_EffectParameter::SetNumber(int row, int val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Global_Camera_EffectParameter::GetAspectRatio_X(int row)
		{
			return visitor_->GetElementInt(row, 3);
		}

		void Global_Camera_EffectParameter::SetAspectRatio_X(int row, int val)
		{
			visitor_->SetElementValue(row, 3, val);
		}

		int Global_Camera_EffectParameter::GetAspectRatio_Y(int row)
		{
			return visitor_->GetElementInt(row, 4);
		}

		void Global_Camera_EffectParameter::SetAspectRatio_Y(int row, int val)
		{
			visitor_->SetElementValue(row, 4, val);
		}
	}

}