#include "stdafx.h"
#include "svr_material_special_rule.h"

#include "svr_json_helper.h"

namespace svr_data
{

	bool SvrMaterialSpecialRule::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		IF_ERROR_RETURN(GetJsonValue(jv["channelId"], ChannelId));
		IF_ERROR_RETURN(GetJsonValue(jv["ruleType"], RuleType));
		IF_ERROR_RETURN(GetJsonValue(jv["reservedInt1"], ReservedInt1));
		IF_ERROR_RETURN(GetJsonValue(jv["reservedInt2"], ReservedInt2));
		IF_ERROR_RETURN(GetJsonValue(jv["reservedInt3"], ReservedInt3));
		IF_ERROR_RETURN(GetJsonValue(jv["reservedInt4"], ReservedInt4));

		return true;
	}

}
