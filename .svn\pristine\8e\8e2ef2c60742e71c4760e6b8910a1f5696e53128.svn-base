#ifndef _VR_EscaSkirtBrushVisitor_H_
#define _VR_EscaSkirtBrushVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Skirt_Brush.h"

namespace decoration_vr_interface
{
	class VrEscaSkirtBrushVisitor : public BaseVisitor
	{
	public:
		VrEscaSkirtBrushVisitor();
		virtual ~VrEscaSkirtBrushVisitor();

		DEFINE_CREATE_FUN(VrEscaSkirtBrushVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Skirt_Brush> model_;
	};
}
#endif
