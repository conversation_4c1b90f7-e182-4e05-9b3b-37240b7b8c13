//
//  Sel_Lop.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Lop : public DGBaseVisitor
{
public:
	Sel_Lop(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	int GetIsParallel(int row);
	void SetIsParallel(int row, int val);

	rse::string GetPath_NoStop(int row);
	void SetPath_NoStop(int row, const tchar* val);

	rse::string GetPath_Stop(int row);
	void SetPath_Stop(int row, const tchar* val);

	rse::string GetANPath(int row);
	void SetANPath(int row, const tchar* val);

	int GetPos(int row);
	void SetPos(int row, int val);

	float GetPos_X(int row);
	void SetPos_X(int row, float val);

	float GetPos_Y(int row);
	void SetPos_Y(int row, float val);

};

class MaterialLopButton : public DGBaseVisitor
{
public:
	MaterialLopButton(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}

	rse::string GetPanelTexture(int row);
	void SetPanelTexture(int row, const tchar* val);
};
}