#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_CarIndicator;
class MaterialChannel;

class VrCarIndicatorVisitor : public BaseVisitor
{
public:
	VrCarIndicatorVisitor();
	~VrCarIndicatorVisitor();

	DEFINE_CREATE_FUN(VrCarIndicatorVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_CarIndicator> model_;
	std::shared_ptr<MaterialChannel> material_;
};
}
