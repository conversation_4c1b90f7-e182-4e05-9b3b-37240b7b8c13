#ifndef _SVR_MATERIAL_SPECIAL_RULE_H_
#define _SVR_MATERIAL_SPECIAL_RULE_H_

#include "svr_base_data.h"

namespace svr_data
{

	class SvrMaterialSpecialRule : public ISvrBaseData
	{
	public:
		virtual bool Parse(const Json::Value& jobj) override;

	public:
		int32_t ChannelId;
		int32_t RuleType;
		int32_t ReservedInt1;
		int32_t ReservedInt2;
		int32_t ReservedInt3;
		int32_t ReservedInt4;
	};

}//namespace svr_data

#endif//_MATERIAL_SPECIAL_RULE_H_

