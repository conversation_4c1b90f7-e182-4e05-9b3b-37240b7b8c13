#ifndef _SVR_CAR_WALL_H_
#define _SVR_CAR_WALL_H_

#include <vector>

#include "svr_base_data.h"
#include "svr_part_basic_info.h"
#include "svr_wall_element.h"
#include "svr_wall_size_info.h"
#include "svr_material.h"

namespace svr_data
{
	class SvrWallConstructData : public ISvrBaseData
	{
	public:
		virtual bool Parse(const Json::Value& jobj) override;

	public:
		int32_t ElevatorSizeId;
		rse::vector<SvrWallElement> Elems;

	private:
		bool ParseWallElems(const Json::Value& jv);
	};

	class SvrWallData : public ISvrBaseData
	{
	public:
		virtual bool Parse(const Json::Value& jobj) override;

	public:
		SvrPartBasicInfo basic_info_;
		rse::vector<SvrWallSizeInfo> WallSizeDataList;
		rse::vector<SvrWallConstructData> Data;

	private:
		bool ParseWallWallSizeDataList(const Json::Value& jv);
		bool ParseWallDatas(const Json::Value& jv);
	};

	class SvrExhibitWall : public ISvrBaseData
	{
	public:
		bool Parse(const Json::Value& jobj) override;

	public:
		SvrWallData WallInfo;
		rse::vector<SvrMaterialInfo> MaterialInfos;

	private:
		bool ParseMaterialInfos(const Json::Value& jobj);
	};
}
#endif
