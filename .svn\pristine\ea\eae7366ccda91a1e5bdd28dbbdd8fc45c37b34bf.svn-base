#ifndef _SVR_WALL_ELEMENT_H_
#define _SVR_WALL_ELEMENT_H_

#include "svr_base_data.h"

namespace svr_data
{

	class SvrWallElement : public ISvrBaseData
	{
	public:
		virtual bool Parse(const Json::Value& jobj) override;

	public:
		int32_t Id;
		float PosX;
		float PosY;
		float SizeX;
		float SizeY;
		float PosZ;
		float LeftSideThick;
		float RightSideThick;
		float TopSideThick;
		float BottomSideThick;
		int32_t EdgeMark;
		float CornerRadiusX;
		float CornerRadiusY;
		float CornerRadiusZ;
		int32_t CornerType;
		float Gap;
		int32_t Marker;
		int32_t LightMapMark;
		int64_t MaterialId;
	};

}//namespace svr_data

#endif//_WALL_ELEMENT_H_

