#pragma once
#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{	
	class IPartOperator
	{
	public:
		enum 
		{
			ByNone = -1,
			ByDefault,
			ByGoods
		};

		IPartOperator()
			:flag_(-1)
		{

		}

		virtual bool SetPart(int part_type, int64 part_id) = 0;
		virtual int GetNotifyPartType(int part_type) = 0;
		virtual int64 GetCurrentPartId(int part_type) = 0;
		virtual const char* GetCurrentPartName(int part_type) = 0;

		virtual bool SetPartByGoods(int part_type, int64 goods_id);
		virtual bool SetPartByContent(int part_type, const rse::string& c) = 0;
	protected:
		int flag_;
	};

	typedef std::shared_ptr<IPartOperator> IPartOperatorPtr;

	class BasePartOperator : public IPartOperator
	{
	public:
		BasePartOperator();
		~BasePartOperator();

		virtual int GetNotifyPartType(int part_type) override;
		virtual int64 GetCurrentPartId(int part_type) override;
		virtual const char* GetCurrentPartName(int part_type) override;
	protected:
		IElevatorPartPtr CreatePart(int part_type, int64 part_id);
		IElevatorPartPtr CreatePart(int part_type, const rse::string& c);
		IElevatorPartPtr CreatePartInner(int part_type, svr_data::IDataBasePtr db_part);
		void AddMaterialFilesToFilePools(rse::vector<svr_data::SvrMaterialInfo> &mat_infos);
		
		void BaseSetElevatorPart(int part_type, IElevatorPartPtr part);
		bool ReplaceDoorMaterialIfNeed(int t1, IElevatorPart* part);

		svr_data::IDataBasePtr GetRawData(int part_type, int64 part_id);
		svr_data::IDataBasePtr GetRawData(int part_type, const rse::string& c);
	};

}
