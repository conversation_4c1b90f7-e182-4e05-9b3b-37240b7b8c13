#include "stdafx.h"
#include "EscaAcessOverFactory.h"
#include "esca_elevator_part.h"

namespace decoration_vr_interface
{
	void EscaAcessOverFactory::SetPartInfo(IElevatorPart* part, svr_data::SvrCommonPart* svr_part)
	{
		EscalatorPartFactory::SetPartInfo(part, svr_part);

		/*part->up_floor_number_ = extra_data_map[TSTR("UpFloorNumber")];
		part->down_floor_number_ = extra_data_map[TSTR("DownFloorNumber")];*/
	}

}