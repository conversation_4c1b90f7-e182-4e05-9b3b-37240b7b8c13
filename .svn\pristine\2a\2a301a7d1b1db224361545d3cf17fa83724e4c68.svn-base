#include "stdafx.h"
#include "esca_elevator_part_factory.h"
#include "esca_elevator_part.h"

namespace decoration_vr_interface
{
	EscalatorPartFactory::EscalatorPartFactory(int part_type)
		:part_type_(part_type)
	{

	}

	EscalatorPartFactory::~EscalatorPartFactory()
	{
	}

	void EscalatorPartFactory::SetPartType(int ty)
	{
		part_type_ = ty;
	}

	int EscalatorPartFactory::GetPartType()
	{
		return part_type_;
	}

	decoration_vr_interface::IElevatorPartPtr EscalatorPartFactory::CreatePart(FactoryArgs* args)
	{
		switch (args->createType)
		{
		case PCT_CREATE_FROM_JSON_CONFIG:
			return GetPartFromJsonConfig(args);

		case PCT_CREATE_FROM_JSON:
			return GetPartFromJsonObject(args);

		default:
			return nullptr;
		}
	}

	decoration_vr_interface::IElevatorPartPtr EscalatorPartFactory::GetPartFromJsonConfig(FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJsonConfig*>(args);
		if (json_arg != nullptr)
		{
			//auto raw_config = static_cast<svr_data::SvrExhibitCarConfig*>(json_arg->raw_config_obj_);
			//if (raw_config != nullptr)
			{
				//const auto& children = raw_config->ChildParts;
				//auto it = std::find_if(children.begin(), children.end(), [this](const svr_data::SvrCommonPart& child) {
				//	return child.basic_info_.PartType == this->GetPartType();
				//});
				//if (it != children.end())
				{
					auto child_part = reinterpret_cast<svr_data::SvrCommonPart*>(json_arg->raw_part_obj_);
					auto common_part = RSE_MAKE_SHARED<EscalatorPart>();
					SetPartInfo(common_part.get(), child_part);
					SetMaterial(common_part.get(), child_part->material_infos_);
					SetPartPosition(common_part.get(), json_arg);
					return common_part;
				}
			}
		}

		return nullptr;
	}

	decoration_vr_interface::IElevatorPartPtr EscalatorPartFactory::GetPartFromJsonObject(FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJson*>(args);
		if (json_arg != nullptr && json_arg->raw_obj_ != nullptr)
		{
			auto raw_part = static_cast<svr_data::SvrExhibitCommonPart*>(json_arg->raw_obj_);
			if (raw_part != nullptr)
			{
				auto common_part = RSE_MAKE_SHARED<EscalatorPart>();
				SetPartInfo(common_part.get(), &(raw_part->part_info_));
				SetMaterial(common_part.get(), raw_part->part_info_.material_infos_);

				return common_part;
			}
		}

		return nullptr;
	}

	void EscalatorPartFactory::SetPartPosition(IElevatorPart* part, FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJsonConfig*>(args);
		auto config = json_arg->config_;
		auto raw_config = static_cast<svr_data::SvrExhibitCarConfig*>(json_arg->raw_config_obj_);

		//const auto& datas = raw_config->SetupPositionDatas;
		//auto it = std::find_if(datas.begin(), datas.end(), [part](const svr_data::SvrExhibitIntLongKeyValData& data) {
		//	return data.IntKey == part->GetPartType();
		//});
		//if (it != datas.end())
		{
			switch (part->GetPartType())
			{
			case PartTypeHandrail:
			{
				auto var = RSE_MAKE_SHARED<Variant>();
				var->i_ = raw_config->SetupPositionDatas[json_arg->order_id_];
				config->SetExtendProperty(EPK_HandrailSetupPos, var);
			}
			break;
			//case PartTypeMirror:
			//	//config->SetExtendProperty(EPK_HandrailSetupPos, var);
			//	break;
			default:
				break;
			}
		}
	}

	void EscalatorPartFactory::SetPartInfo(IElevatorPart* part, svr_data::SvrCommonPart* svr_part)
	{
		auto common_part = static_cast<EscalatorPart*>(part);
		common_part->part_type_ = svr_part->basic_info_.PartType;
		common_part->part_id_ = svr_part->basic_info_.Id;
	}

	void EscalatorPartFactory::SetMaterial(IElevatorPart* part, const rse::vector<svr_data::SvrPartMaterial>& edit_mats)
	{
		auto common_part = static_cast<EscalatorPart*>(part);

		for (auto it = edit_mats.begin(), ie = edit_mats.end(); it != ie; ++it)
		{
			common_part->editparts_.push_back(it->EditPartId);
			common_part->editpart_materials_[it->EditPartId] = it->MaterialId;
		}
	}
}
