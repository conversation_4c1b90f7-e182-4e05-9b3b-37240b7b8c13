#include "stdafx.h"
#include "VrEmidsVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Emids.h"
#include "Sel_CopLcd.h"
#include "material_channel.h"
#include "electric_part.h"

namespace decoration_vr_interface
{

	VrEMIDSVisitor::VrEMIDSVisitor()
	{
        part_type_ = PartTypeEMIDS;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		InitializeAvailablePartTypes(types);

	} 

	VrEMIDSVisitor::~VrEMIDSVisitor()
	{
	}


	void VrEMIDSVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Emids>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), part_type_info->material_array_.c_str());
		}

		if (!lcd_)
		{
			tstring emids_lcd_name = TSTR("Sel_EMIDSLcd");
			lcd_ = RSE_MAKE_SHARED<Sel_CopLcd>(vr_glob->get_dg_scene(), emids_lcd_name.c_str());
		}
	}

	bool VrEMIDSVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		model_->Clear();
		lcd_->Clear();

		auto emids = static_cast<ElectricPart*>(vr_change->config_->GetPart(part_type_));
		rse::vector<MaterialInfo> material_infos;

		if (emids)
		{
			model_->AddRow();
			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
			auto model_file = emids->LoadModel();
			auto path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(emids->GetPartType(), emids->GetPartId(), model_file, path);

			model_->SetPos(0, emids->GetSetupOrientation());
			model_->SetPath(0, path.c_str());
			model_->SetPos_X(0, emids->GetPosX());
			model_->SetPos_Y(0, emids->GetPosY());

			auto material = emids->GetPartMaterial();
			if (material != 0)
			{
				MaterialInfo info(material, 1, 0);
				material_infos.push_back(info);
			}

			tstring lcd_path = emids->GetLcdPath();

			int row = lcd_->AddRow();

			if (lcd_path != TSTR(""))
			{
				lcd_->Setm_bool_Select(row, true);
				auto path = Util::CombinePath(dir, lcd_path);
				AddFileToModelCache(emids->GetLcdType(), emids->GetLcd(), lcd_path, path);
				lcd_->Setm_str_Path(row, path.c_str());
				lcd_->Setm_int_Mark(row, 0);
			}
			else
			{
				lcd_->Setm_bool_Select(row, false);
			}

			auto res = VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), part_type_, material_infos, emids->GetIsHasReflect());
			return res >= 0;

		}

		return true;
	}

	void VrEMIDSVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
		lcd_->PrintData(file_name);
		material_->PrintData(file_name);
	}

}
    