﻿#include "stdafx.h"
#include "VrHallShaftVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Shaft.h"
#include "material_channel.h"
#include "common_part.h"

namespace decoration_vr_interface
{

	VrHallShaftVisitor::VrHallShaftVisitor()
	{
		part_type_ = PartTypeHallShaft;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrHallShaftVisitor::~VrHallShaftVisitor()
	{
	}

	void VrHallShaftVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Shaft>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}
		else
		{
			model_->init(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), part_type_info->material_array_.c_str());
		}
		else
		{
			material_->init(vr_glob->get_dg_scene(), part_type_info->material_array_.c_str());
		}

#if defined(XJ)
		if (!floor_material_)
		{
			floor_material_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), "Material_Shaft_Floor");
		}
		else
		{
			floor_material_->init(vr_glob->get_dg_scene(), "Material_Shaft_Floor");
		}
#endif
	}

	bool VrHallShaftVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (model_->GetRowCount() < 1)
		{
			model_->AddRow();
		}
		model_->Set_Select(0, false);

		auto shaft = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type_));
		if (!shaft)
		{
			return true;
		}

		auto vr_glob = VrGlobalInfo::Instance();
		bool is_reflect = shaft->GetIsHasReflect();


		int64 part_id = -1;
		part_id = shaft->GetPartId();
		bool selected = part_id > 0;
		model_->Set_Select(0, selected);
		if (selected)
		{
			auto model_file = shaft->LoadModel();
			auto dir = vr_glob->get_config_info()->get_model_dir();
			tstring model_path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(shaft->GetPartType(), part_id, model_file, model_path);
			model_->Set_Path(0, model_path.c_str());
			if (model_file.empty())
			{
				LOGI("VrHallShaftVisitor model file empty");
				GlobalInfoDataCommon::Instance()->ReportInfoToHost("VrHallShaftVisitor model file empty");
				return false;
			}

			model_->Set_Width(0, GlobalInfoDataCommon::Instance()->GetShaftWidth());
			model_->Set_Depth(0, GlobalInfoDataCommon::Instance()->GetShaftDepth());
		}

		rse::vector<MaterialInfo> floor_material_infos;
		rse::vector<MaterialInfo> material_infos;
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		auto edit_parts = shaft->GetEditPartsPtr();
		for (int i = 0, c = edit_parts->size(); i < c; ++i)
		{
			int id = edit_parts->at(i);
			if (id >= 100)
			{
				floor_material_infos.push_back(MaterialInfo(shaft->GetEditPartMaterial(id), id-100, type_info->vr_type_));
			}
			else
			{
				material_infos.push_back(MaterialInfo(shaft->GetEditPartMaterial(id), id, type_info->vr_type_));
			}
		}

		auto res = VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), part_type_, material_infos, is_reflect);
#if defined(XJ)
		VrGlobalInfo::Instance()->WriteMaterialChannelInfo(floor_material_.get(), part_type_, floor_material_infos, is_reflect);
#endif
		return res >= 0;
	}

	void VrHallShaftVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
		material_->PrintData(file_name);
#if defined(XJ)
		floor_material_->PrintData(file_name);
#endif
	}
}