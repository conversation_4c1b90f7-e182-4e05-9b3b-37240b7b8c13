//
//  Sel_Lop.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Lop.h"


namespace decoration_vr_interface
{

int Sel_Lop::GetIsParallel(int row)
{
    return visitor_->GetElementInt(row, 0);
}

void Sel_Lop::SetIsParallel(int row, int val)
{
    visitor_->SetElementValue(row, 0, val);
}

rse::string Sel_Lop::GetPath_NoStop(int row)
{
    const char* val = visitor_->GetElementString(row, 1);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Lop::SetPath_NoStop(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 1, val);
}

rse::string Sel_Lop::GetPath_Stop(int row)
{
    const char* val = visitor_->GetElementString(row, 2);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Lop::SetPath_Stop(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 2, val);
}

rse::string Sel_Lop::GetANPath(int row)
{
    const char* val = visitor_->GetElementString(row, 3);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Lop::SetANPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 3, val);
}

int Sel_Lop::GetPos(int row)
{
    return visitor_->GetElementInt(row, 4);
}

void Sel_Lop::SetPos(int row, int val)
{
    visitor_->SetElementValue(row, 4, val);
}

float Sel_Lop::GetPos_X(int row)
{
	if (visitor_->GetColumnCount() > 5)
	{
		return visitor_->GetElementFloat(row, 5);
	}

#ifdef RENDER_SKETCHER_HTML
	return 0;
#else
	return FLT_MAX;
#endif
	
}

void Sel_Lop::SetPos_X(int row, float val)
{
	if (visitor_->GetColumnCount() > 5)
	{
		visitor_->SetElementValue(row, 5, val);
	}
}

float Sel_Lop::GetPos_Y(int row)
{
	if (visitor_->GetColumnCount() > 6)
	{
		return visitor_->GetElementFloat(row, 6);
	}
#ifdef RENDER_SKETCHER_HTML
	return 0;
#else
	return FLT_MAX;
#endif
}

void Sel_Lop::SetPos_Y(int row, float val)
{
	if (visitor_->GetColumnCount() > 6)
	{
		visitor_->SetElementValue(row, 6, val);
	}
}

rse::string MaterialLopButton::GetPanelTexture(int row)
{
	const char* val = visitor_->GetElementString(row, 0);
	rse::string ret = val;
	fnGetLigMgr()->ReleaseLibBuf(val);
	return ret;
}

void MaterialLopButton::SetPanelTexture(int row, const tchar* val)
{
	visitor_->SetElementValue(row, 0, val);
}

}