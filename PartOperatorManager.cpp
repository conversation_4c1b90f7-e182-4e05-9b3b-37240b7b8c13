#include "stdafx.h"
#include "PartOperatorManager.h"
#include "ElectricOperator.h"
#include "ConfigOperator.h"
#include "ElevatorPartOperator.h"
#include "FrontWallTypeOperator.h"

namespace decoration_vr_interface
{
	PartOperatorManager::PartOperatorManager()
	{
		config_operator_ = RSE_MAKE_SHARED<ConfigOperator>();
		electric_operator_ = RSE_MAKE_SHARED<ElectricOperator>();
		elevator_part_operator_ = RSE_MAKE_SHARED<ElevatorPartOperator>();
		front_wall_operator_ = RSE_MAKE_SHARED<FrontWallTypeOperator>();
	}


	PartOperatorManager::~PartOperatorManager()
	{
	}

	IPartOperator* PartOperatorManager::GetPartOperator(int part_type)
	{
		if (part_type == PartTypeCarConfig || part_type == PartTypeHallConfig || part_type == PartTypeEscalatorConfig)
		{
			return config_operator_.get();
		}
		else if (part_type == PartTypeFrontWallConstructorType)
		{
			return front_wall_operator_.get();
		}
		else if (GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type) == MPT_ELECTRIC)
		{
			return electric_operator_.get();
		}
		else
		{
			return elevator_part_operator_.get();
		}
	}

}