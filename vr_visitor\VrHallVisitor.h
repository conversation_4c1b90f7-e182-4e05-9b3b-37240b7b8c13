#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Hall;
class VrHallVisitor : public BaseVisitor
{
public:
	VrHallVisitor();
	~VrHallVisitor();

	DEFINE_CREATE_FUN(VrHallVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_Hall> model_;
};
}
