#ifndef _HALL_ROOM_PART_H_
#define _HALL_ROOM_PART_H_

#include "IElevatorPart.h"

namespace decoration_vr_interface
{
	class HallRoomPart :public IElevatorPart
	{
		friend class HallRoomPartFactory;
	public:
		HallRoomPart();
		virtual ~HallRoomPart();

		virtual int GetPartType() override;
		virtual int64 GetPartId() override;

		int64 GetHallDoorCount();

		virtual tstring GetPartName() override;
		virtual void SetPartName(const tstring &new_name) override;

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		int64 GetPartMaterial();
		void SetPartMaterial(int64 mat);

		int64 GetEditPartMaterial(int editpart_id);
		bool SetEditPartMaterial(int editpart_id, int64 mat);

		rse::vector<int>* GetEditPartsPtr() { return &editparts_; }

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

	protected:
		int part_type_;

		int64 part_id_;
		int64 hall_door_count_;
		tstring part_name_;

		rse::vector<int> editparts_;
		rse::map<int, int64> editpart_materials_;

		bool is_reflect_;
	};

	typedef std::shared_ptr<HallRoomPart> HallRoomPartPtr;
}

#endif//_HALL_ROOM_PART_H_