#include "stdafx.h"
#include "Sel_Esca_Side_Cladding.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		rse::string Sel_Esca_Side_Cladding::Getstr_Path(int row)
		{
			const char* val = visitor_->GetElementString(row, 0);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Side_Cladding::Setstr_Path(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		rse::string Sel_Esca_Side_Cladding::Getstr_piecePath(int row)
		{
			const char* val = visitor_->GetElementString(row, 1);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Side_Cladding::Setstr_piecePath(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

	}

}