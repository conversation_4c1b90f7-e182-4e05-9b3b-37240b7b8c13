#pragma once
#include "IElevatorPart.h"
namespace decoration_vr_interface
{
	class CarTop : public IElevatorPart
	{
		friend class CarTopFactory;
	public:
		CarTop();
		~CarTop();

		virtual int GetPartType() override;
		virtual int64 GetPartId() override;

		virtual tstring GetPartName() override;
		virtual void SetPartName(const tstring &new_name) override;

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		int GetHeight() const { return height_; }
		int GetThickness() const { return thickness_; }
		int GetFrontHeight() const { return front_height_; }
		int GetLeftHeight() const { return left_height_; }
		int GetRightHeight() const { return right_height_; }
		int GetBackHeight() const { return back_height_; }

		int64 GetEditPartMaterial(int editpart_id);
		bool SetEditPartMaterial(int editpart_id, int64 mat);

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

		rse::vector<int>* GetEditPartsPtr() { return &editparts_; }
	protected:
		int64 part_id_;

		int height_;
		int thickness_;
		int front_height_;
		int left_height_;
		int right_height_;
		int back_height_;
		bool is_reflect_;

		rse::vector<int> editparts_;
		rse::map<int, int64> editpart_materials_;
	};

}