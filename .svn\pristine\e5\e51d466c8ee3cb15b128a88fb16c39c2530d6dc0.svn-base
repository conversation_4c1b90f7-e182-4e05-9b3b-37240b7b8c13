
#include "stdafx.h"
#include "ElevatorSize.h"

namespace decoration_vr_interface
{

	ElevatorSize ElevatorSize::GetElevatorSizeFromDb(int id)
	{
		ElevatorSize elev_size;
		elev_size.id_ = id;
		elev_size.width_ = 160;
		elev_size.depth_ = 150;
		elev_size.height_ = 0;
		elev_size.door_width_ = 90;
		elev_size.door_height_ = 210;
		/*auto elevator_size_set = db_visitor::DBVisitorMgr::Instance()->GetVisitor()->LoadElevatorCarSize(id);

		elevator_size_set.Reset();
		if (!elevator_size_set.IsEof())
		{
			elev_size.id_ = id;
			elev_size.data_name_ = Util::StringToTString(elevator_size_set.GetStringField("UC_DATA_NAME").c_str());
			elev_size.width_ = elevator_size_set.GetIntField("UC_WIDTH");
			elev_size.depth_ = elevator_size_set.GetIntField("UC_DEPTH");
			elev_size.height_ = kCarHeightDefaultValue;
			elev_size.door_height_ = elevator_size_set.GetIntField("UC_DOOR_HEIGHT");
			elev_size.door_width_ = kCarDoorWidthDefaultValue;
		}*/

		return std::move(elev_size);
	}

	ElevatorSize ElevatorSize::GetElevatorSizeFromDb(int width, int depth)
	{
		ElevatorSize elev_size;
		elev_size.width_ = width;
		elev_size.depth_ = depth;
		elev_size.height_ = 0;
		/*auto elevator_size_set = db_visitor::DBVisitorMgr::Instance()->GetVisitor()->LoadElevatorCarSize(width, depth);

		elevator_size_set.Reset();
		if (!elevator_size_set.IsEof())
		{
			elev_size.id_ = elevator_size_set.GetIntField("UC_ID");
			elev_size.data_name_ = Util::StringToTString(elevator_size_set.GetStringField("UC_DATA_NAME").c_str());
			elev_size.width_ = elevator_size_set.GetIntField("UC_WIDTH");
			elev_size.depth_ = elevator_size_set.GetIntField("UC_DEPTH");
			elev_size.height_ = kCarHeightDefaultValue;
			elev_size.door_height_ = elevator_size_set.GetIntField("UC_DOOR_HEIGHT");
			elev_size.door_width_ = kCarDoorWidthDefaultValue;
		}*/

		return std::move(elev_size);
	}

}