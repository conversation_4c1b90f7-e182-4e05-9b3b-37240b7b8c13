#pragma once
#include "IDbVisitor.h"

namespace decoration_vr_interface
{
	class DbVisitor : public IDbVisitor
	{
	public:
		DbVisitor();
		~DbVisitor();

		static DbVisitor* Instance();

		virtual svr_data::IDataBasePtr LoadMaterial(int64 mat_id) override;
		virtual svr_data::IDataBasePtr LoadPart(int part_type, int64 part_id) override;

		virtual svr_data::IDataBasePtr LoadMaterialByGoodsId(int64 part_id) override;
		virtual svr_data::IDataBasePtr LoadPartByGoodsId(int part_type, int64 part_id) override;

		virtual svr_data::IDataBasePtr LoadPartByString(int part_type, const rse::string& c) override;

		virtual svr_data::IDataBasePtr LoadBootomMaterialByGoodsId(int64 goods_id, int width, int depth, int doorWidth, bool isThroughDoor) override;

	private:
		tstring GetServiceFuncName(int part_type);
		tstring GetServiceGoodsFuncName(int part_type);

		svr_data::IDataBasePtr GetClassData(rse::string command, Json::Value& jobj);

		svr_data::IDataBasePtr GetExhibitWallFromExhibitCarConfig(int part_type, svr_data::IDataBasePtr ret);
	};

}
