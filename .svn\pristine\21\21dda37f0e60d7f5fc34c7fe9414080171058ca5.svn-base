#include "stdafx.h"
#include "config_factory.h"

#include "config_part.h"
#include "common_part.h"
#include "electric_part.h"

#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{
	ConfigFactory::ConfigFactory()
	{
	}

	ConfigFactory::~ConfigFactory()
	{
	}

	decoration_vr_interface::IElevatorPartPtr ConfigFactory::CreatePart(FactoryArgs* args)
	{
		switch (args->createType)
		{
		case PCT_CREATE_FROM_JSON:
			return GetConfigFromJsonObject(args);

		default:
			return nullptr;
			break;
		}
	}

	decoration_vr_interface::IElevatorPartPtr ConfigFactory::GetConfigFromJsonObject(FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJson*>(args);
		if (json_arg != nullptr && json_arg->raw_obj_ != nullptr)
		{
			auto raw_config = static_cast<svr_data::SvrExhibitConfig*>(json_arg->raw_obj_);
			if (raw_config != nullptr)
			{
				auto config = RSE_MAKE_SHARED<Config>();
				config->part_type_ = raw_config->part_info_.basic_info_.PartType;
				config->id_ = raw_config->part_info_.basic_info_.Id;

				auto part_type_has_reflect = raw_config->partType_has_reflect_;
				if (config->part_type_ == PartTypeCarConfig)
				{
					GlobalInfoDataCommon::Instance()->partType_reflect_ = part_type_has_reflect;
				}

				//for (auto it = raw_config->ChildParts.begin(), ie = raw_config->ChildParts.end(); it != ie; ++it)
				for (int32_t order = 0, child_count = raw_config->child_parts_.size(); order < child_count; ++order)
				{
					auto& child_part = raw_config->child_parts_[order];
					auto fac = GlobalInfoDataCommon::Instance()->GetElevatorPartFactoryManager()->GetPartFactory(child_part.basic_info_.PartType);
					if (fac != nullptr)
					{
						FactoryArgsOfLoadingFromJsonConfig arg;
						arg.config_ = config.get();
						arg.raw_config_obj_ = raw_config;
						arg.raw_part_obj_ = &child_part;
						arg.order_id_ = order;
						auto part = fac->CreatePart(&arg);
						if (part != nullptr)
						{
							int count = part_type_has_reflect.size();
							bool is_reflect = false;

							for (int i = 0; i < count; i++)
							{
								if (part_type_has_reflect[i] == child_part.basic_info_.PartType)
								{
									is_reflect = true;
								}
							}

							part->SetIsHasReflect(is_reflect);
							config->SetPart(part->GetPartType(), part);
						}
					}			
				}

				return config;
			}
		}

		return nullptr;
	}

	decoration_vr_interface::IElevatorPartPtr CarConfigFactory::CreatePart(FactoryArgs* args)
	{
		auto config = ConfigFactory::CreatePart(args);
		auto c = static_cast<IConfig*>(config.get());
		auto front_wall = static_cast<CommonPart*>(c->GetPart(PartTypeFrontWall));
		auto ft_type = front_wall->GetPartId();
		if (ft_type > 0)
		{
			auto cop = static_cast<ElectricPart*>(c->GetPart(PartTypeCop));
			cop->SetPanelType(ft_type);
		}

		auto car_config = static_cast<IConfig*>(config.get());

		//walls 
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJson*>(args);
		if (json_arg != nullptr && json_arg->raw_obj_ != nullptr)
		{
			auto raw_config = static_cast<svr_data::SvrExhibitCarConfig*>(json_arg->raw_obj_);
			if (raw_config != nullptr)
			{
				if (raw_config->FrontWallType > 0) 
				{
					auto cop = static_cast<ElectricPart*>(c->GetPart(PartTypeCop));
					cop->SetPanelType(raw_config->FrontWallType);
				}
				//2019.12.11
				int door_type = raw_config->part_info_.basic_info_.IntVal1;
				if (door_type <= 0)
				{
					door_type = kCarDoorTypeDefaultValue;
				}
				auto var = RSE_MAKE_SHARED<Variant>();
				var->i_ = door_type;
				car_config->SetExtendProperty(EPK_CarDoorType, var);

				auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;

				auto& children = raw_config->Walls;
				for (int32_t i = 0; i < children.size(); ++i)
				{
					auto &wall = children[i];
					auto fac = GlobalInfoDataCommon::Instance()->GetElevatorPartFactoryManager()->GetPartFactory(wall.basic_info_.PartType);
					if (fac != nullptr)
					{
						FactoryArgsOfLoadingFromJsonConfig arg;
						arg.config_ = car_config;
						arg.raw_config_obj_ = raw_config;
						arg.raw_part_obj_ = &wall;
						arg.order_id_ = 0;
						auto part = fac->CreatePart(&arg);
						if (part != nullptr)
						{
							car_config->SetPart(part->GetPartType(), part);
						}
					}						
				}
			}
		}

		return config;
	}

}