#include "stdafx.h"
#include "VrConfigInfo.h"
#include "Util.h"

tstring VrConfigInfo::get_texture_dir()
{
	auto path = decoration_vr_interface::Util::CombinePath(resouseFilePath, textures_dir_);
	return path;
}

tstring VrConfigInfo::get_model_dir()
{
	auto path = decoration_vr_interface::Util::CombinePath(resouseFilePath, model_dir_);
	return path;
}

tstring VrConfigInfo::get_shader_dir()
{
	auto path = decoration_vr_interface::Util::CombinePath(resouseFilePath, shader_dir_);
	return path;
}
