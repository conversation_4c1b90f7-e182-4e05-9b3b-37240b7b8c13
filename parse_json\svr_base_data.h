#ifndef _SVR_BASE_DATA_H_
#define _SVR_BASE_DATA_H_

#include <stddef.h>
#include <stdint.h>

#include <memory>
#include <string>

#define IF_ERROR_RETURN(a) do\
{\
	if(!(a))\
		return false;\
}while (0)

namespace Json
{
	class Value;
}

namespace svr_data
{
	class ISvrBaseData
	{
	public:
		virtual bool Parse(const Json::Value& jobj) = 0;
	};

	typedef std::shared_ptr<ISvrBaseData> IDataBasePtr;
}

#endif//_BASE_DATA_H_

