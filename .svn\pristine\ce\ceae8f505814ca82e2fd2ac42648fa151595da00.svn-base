#include "stdafx.h"
#include "Sel_Esca_Scene.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		bool Sel_Esca_Scene::Getbool_Select(int row)
		{
			return visitor_->GetElementBool(row, 0);
		}

		void Sel_Esca_Scene::Setbool_Select(int row, bool val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		rse::string Sel_Esca_Scene::Getstr_Path(int row)
		{
			const char* val = visitor_->GetElementString(row, 1);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Scene::Setstr_Path(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

	}

}