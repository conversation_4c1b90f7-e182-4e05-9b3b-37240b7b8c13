#include "stdafx.h"
#include "skirting_part.h"
#include "car_wall_part.h"
#include "ConfigAnalyzer.h"

namespace decoration_vr_interface
{

	SkirtingPart::SkirtingPart()
		:part_type_(PartTypeSkirting), part_id_(-1), part_name_("Skirting"), is_reflect_(false)
	{
		skirting_mat_.insert(std::make_pair(PartTypeLeftWall, -1));
		skirting_mat_.insert(std::make_pair(PartTypeBackWall, -1));
		skirting_mat_.insert(std::make_pair(PartTypeRightWall, -1));
	}

	SkirtingPart::~SkirtingPart()
	{

	}

	int SkirtingPart::GetPartType()
	{
		return part_type_;
	}

	int64 SkirtingPart::GetPartId()
	{
		return part_id_;
	}

	tstring SkirtingPart::GetPartName()
	{
		return part_name_;
	}

	void SkirtingPart::SetPartName(const tstring &new_name)
	{
		part_name_ = new_name;
	}

	tstring SkirtingPart::LoadModel()
	{
		tstring s;
		return s;
	}

	bool SkirtingPart::IsValidPart()
	{
		if (part_id_ <= 0)
		{
			return false;
		}

		for (auto it = skirting_mat_.begin(), ie = skirting_mat_.end(); it != ie; ++it)
		{
			if (it->second <= 0)
			{
				return false;
			}
		}

		return true;
	}

	void SkirtingPart::SetSkirtingMaterial(int64 mat)
	{
		for (auto it = skirting_mat_.begin(), ie = skirting_mat_.end(); it != ie; ++it)
		{
			it->second = mat;
		}
	}

	void SkirtingPart::SetSkirtingMaterial(int type, int64 mat)
	{
		skirting_mat_[type] = mat;
	}

	int64 SkirtingPart::GetSkirtingMaterial(int type)
	{
		auto it = skirting_mat_.find(type);
		if (it != skirting_mat_.end())
		{
			return it->second;
		}
		return -1;
	}

	void SkirtingPart::CollectSkirtingMaterial()
	{
		//CarWallPart* left = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(PartTypeLeftWall));
		//auto elem = left->GetWallElems(WALLELEM_MARK_SKIRTING);
		//if (elem)
		//	SetSkirtingMaterial(PartTypeLeftWall, elem->material_id_);

		//CarWallPart* back = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(PartTypeBackWall));
		//elem = back->GetWallElems(WALLELEM_MARK_SKIRTING);
		//if (elem)
		//	SetSkirtingMaterial(PartTypeBackWall, elem->material_id_);

		//CarWallPart* right = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(PartTypeRightWall));
		//elem = right->GetWallElems(WALLELEM_MARK_SKIRTING);
		//if (elem)
		//	SetSkirtingMaterial(PartTypeRightWall, elem->material_id_);
	}

	bool SkirtingPart::GetIsHasReflect()
	{
		return is_reflect_;
	}

	void SkirtingPart::SetIsHasReflect(const bool &is_reflect)
	{
		is_reflect_ = is_reflect;
	}

}
