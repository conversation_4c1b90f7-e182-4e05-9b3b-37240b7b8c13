#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_Control_Flag : public DGBaseVisitor
		{
		public:
			Global_Control_Flag(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			int GetDoorState(int row);
			void SetDoorState(int row, int val);

			int GetDoorCommand(int row);
			void SetDoorCommand(int row, int val);

			int GetCameraState(int row);
			void SetCameraState(int row, int val);

			int GetShowHideFlag(int row);
			void SetShowHideFlag(int row, int val);

			bool GetReachedFloorNotify(int row);
			void SetReachedFloorNotify(int row, bool val);

		};
	}

}