//
//  Sel_Cop.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Cop.h"


namespace decoration_vr_interface
{

rse::string Sel_Cop::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 0);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Cop::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 0, val);
}

rse::string Sel_Cop::GetAN_Path(int row)
{
    const char* val = visitor_->GetElementString(row, 1);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Cop::SetAN_Path(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 1, val);
}

int Sel_Cop::GetPos(int row)
{
    return visitor_->GetElementInt(row, 2);
}

void Sel_Cop::SetPos(int row, int val)
{
    visitor_->SetElementValue(row, 2, val);
}

float Sel_Cop::GetPos_X(int row)
{
    return visitor_->GetElementFloat(row, 3);
}

void Sel_Cop::SetPos_X(int row, float val)
{
    visitor_->SetElementValue(row, 3, val);
}

float Sel_Cop::GetPos_Y(int row)
{
    return visitor_->GetElementFloat(row, 4);
}

void Sel_Cop::SetPos_Y(int row, float val)
{
    visitor_->SetElementValue(row, 4, val);
}

int Sel_Cop::GetCopForm(int row) 
{ 
	return visitor_->GetElementInt(row, 6);
}
void Sel_Cop::SetCopForm(int row, int val)
{ 
	visitor_->SetElementValue(row, 6, val); 
}

int Sel_Cop::GetCopType(int row) 
{ 
	return visitor_->GetElementInt(row, 7); 
}
void Sel_Cop::SetCopType(int row, int val) 
{
	visitor_->SetElementValue(row, 7, val); 
}

rse::string MaterialCopButton::GetPanelTexture(int row)
{
	const char* val = visitor_->GetElementString(row, 0);
	rse::string ret = val;
	fnGetLigMgr()->ReleaseLibBuf(val);
	return ret;
}

void MaterialCopButton::SetPanelTexture(int row, const tchar* val)
{
	visitor_->SetElementValue(row, 0, val);
}
}