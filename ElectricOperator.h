#pragma once
#include "BasePartOperator.h"
namespace decoration_vr_interface
{
	class ElectricOperator : public BasePartOperator
	{
	public:
		ElectricOperator();
		~ElectricOperator();

		virtual bool SetPart(int part_type, int64 part_id) override;
		virtual bool SetPartByContent(int part_type, const rse::string& c) override;
		virtual int64 GetCurrentPartId(int part_type) override;
		virtual const char* GetCurrentPartName(int part_type) override;
	protected:
		bool IsButtonType(int part_type);
		bool IsIndicatorType(int part_type);
	};

}