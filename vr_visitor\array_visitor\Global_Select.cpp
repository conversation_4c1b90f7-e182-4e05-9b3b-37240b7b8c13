#include "stdafx.h"
#include "Global_Select.h"


namespace decoration_vr_interface
{

int Global_Select::GetCurrentElevtor(int row)
{
    return visitor_->GetElementInt(row, 0);
}

void Global_Select::SetCurrentElevtor(int row, int val)
{
    visitor_->SetElementValue(row, 0, val);
}

int Global_Select::GetCurrentCarId(int row)
{
    return visitor_->GetElementInt(row, 1);
}

void Global_Select::SetCurrentCarId(int row, int val)
{
    visitor_->SetElementValue(row, 1, val);
}

bool Global_Select::GetIsHaveAuxCop(int row)
{
    return visitor_->GetElementBool(row, 2);
}

void Global_Select::SetIsHaveAuxCop(int row, bool val)
{
    visitor_->SetElementValue(row, 2, val);
}

bool Global_Select::GetIsHaveHcCop(int row)
{
    return visitor_->GetElementBool(row, 3);
}

void Global_Select::SetIsHaveHcCop(int row, bool val)
{
    visitor_->SetElementValue(row, 3, val);
}

bool Global_Select::GetIsHaveAuxHcCop(int row)
{
    return visitor_->GetElementBool(row, 4);
}

void Global_Select::SetIsHaveAuxHcCop(int row, bool val)
{
    visitor_->SetElementValue(row, 4, val);
}

bool Global_Select::GetIsHaveCarIndicator(int row)
{
    return visitor_->GetElementBool(row, 5);
}

void Global_Select::SetIsHaveCarIndicator(int row, bool val)
{
    visitor_->SetElementValue(row, 5, val);
}

bool Global_Select::GetIsHaveWallTrim(int row)
{
    return visitor_->GetElementBool(row, 6);
}

void Global_Select::SetIsHaveWallTrim(int row, bool val)
{
    visitor_->SetElementValue(row, 6, val);
}

bool Global_Select::GetIsHaveCarDoorTrim(int row)
{
    return visitor_->GetElementBool(row, 7);
}

void Global_Select::SetIsHaveCarDoorTrim(int row, bool val)
{
    visitor_->SetElementValue(row, 7, val);
}

bool Global_Select::GetIsHaveThiefCamera(int row)
{
    return visitor_->GetElementBool(row, 8);
}

void Global_Select::SetIsHaveThiefCamera(int row, bool val)
{
    visitor_->SetElementValue(row, 8, val);
}

bool Global_Select::GetIsHaveProtectedWall(int row)
{
    return visitor_->GetElementBool(row, 9);
}

void Global_Select::SetIsHaveProtectedWall(int row, bool val)
{
    visitor_->SetElementValue(row, 9, val);
}

bool Global_Select::GetIsHaveHallIndicator(int row)
{
    return visitor_->GetElementBool(row, 10);
}

void Global_Select::SetIsHaveHallIndicator(int row, bool val)
{
    visitor_->SetElementValue(row, 10, val);
}

bool Global_Select::GetIsHaveLantern(int row)
{
    return visitor_->GetElementBool(row, 11);
}

void Global_Select::SetIsHaveLantern(int row, bool val)
{
    visitor_->SetElementValue(row, 11, val);
}

bool Global_Select::GetIsHaveHallDoorTrim(int row)
{
    return visitor_->GetElementBool(row, 12);
}

void Global_Select::SetIsHaveHallDoorTrim(int row, bool val)
{
    visitor_->SetElementValue(row, 12, val);
}

int Global_Select::GetDoorType(int row)
{
    return visitor_->GetElementInt(row, 13);
}

void Global_Select::SetDoorType(int row, int val)
{
    visitor_->SetElementValue(row, 13, val);
}

int Global_Select::GetStopSwitcherFloorCount(int row)
{
    return visitor_->GetElementInt(row, 14);
}

void Global_Select::SetStopSwitcherFloorCount(int row, int val)
{
    visitor_->SetElementValue(row, 14, val);
}

int Global_Select::GetElevatorWeight(int row)
{
    return visitor_->GetElementInt(row, 15);
}

void Global_Select::SetElevatorWeight(int row, int val)
{
    visitor_->SetElementValue(row, 15, val);
}

int Global_Select::GetFrontWallType(int row)
{
    return visitor_->GetElementInt(row, 16);
}

void Global_Select::SetFrontWallType(int row, int val)
{
    visitor_->SetElementValue(row, 16, val);
}

int Global_Select::GetCopButtonCount(int row)
{
    return visitor_->GetElementInt(row, 17);
}

void Global_Select::SetCopButtonCount(int row, int val)
{
    visitor_->SetElementValue(row, 17, val);
}

bool Global_Select::GetIsAuxCopSameAsMain(int row)
{
    return visitor_->GetElementBool(row, 18);
}

void Global_Select::SetIsAuxCopSameAsMain(int row, bool val)
{
    visitor_->SetElementValue(row, 18, val);
}

bool Global_Select::GetIsAuxHcCopSameAsMain(int row)
{
    return visitor_->GetElementBool(row, 19);
}

void Global_Select::SetIsAuxHcCopSameAsMain(int row, bool val)
{
    visitor_->SetElementValue(row, 19, val);
}

int Global_Select::GetCarType(int row)
{
    return visitor_->GetElementInt(row, 20);
}

void Global_Select::SetCarType(int row, int val)
{
    visitor_->SetElementValue(row, 20, val);
}

int Global_Select::GetHandrailType(int row)
{
    return visitor_->GetElementInt(row, 21);
}

void Global_Select::SetHandrailType(int row, int val)
{
    visitor_->SetElementValue(row, 21, val);
}

int Global_Select::GetcarType(int row)
{
    return visitor_->GetElementInt(row, 22);
}

void Global_Select::SetcarType(int row, int val)
{
    visitor_->SetElementValue(row, 22, val);
}

int Global_Select::GetThroughBackWallType(int row)
{
	return visitor_->GetElementInt(row, 24);
}

void Global_Select::SetThroughBackWallType(int row, int val)
{
	visitor_->SetElementValue(row, 24, val);
}

int Global_Select::GetThroughBackDoorType(int row)
{
	return visitor_->GetElementInt(row, 25);
}

void Global_Select::SetThroughBackDoorType(int row, int val)
{
	visitor_->SetElementValue(row, 25, val);
}

}