#include "stdafx.h"
#include "Sel_RGB_Share.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Sel_RGB_Share::Getbackground_r(int row)
		{
			return visitor_->GetElementInt(row, 0);
		}

		void Sel_RGB_Share::Setbackground_r(int row, int val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Sel_RGB_Share::Getbackground_g(int row)
		{
			return visitor_->GetElementInt(row, 1);
		}

		void Sel_RGB_Share::Setbackground_g(int row, int val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		int Sel_RGB_Share::Getbackground_b(int row)
		{
			return visitor_->GetElementInt(row, 2);
		}

		void Sel_RGB_Share::Setbackground_b(int row, int val)
		{
			visitor_->SetElementValue(row, 2, val);
		}
	}


}