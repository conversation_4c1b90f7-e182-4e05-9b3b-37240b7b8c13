﻿#include "stdafx.h"
#include "VrCopDisplayVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_CopLcd.h"
#include "material_channel.h"
#include "electric_part.h"

namespace decoration_vr_interface
{

	VrCopDisplayVisitor::VrCopDisplayVisitor()
	{
        part_type_ = PartTypeCopDisplay;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		//types.push_back(PartTypeAuxCopDisplay);
		InitializeAvailablePartTypes(types);

	} 

	VrCopDisplayVisitor::~VrCopDisplayVisitor()
	{
	}


	void VrCopDisplayVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_CopLcd>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

	}

	bool VrCopDisplayVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		model_->Clear();

		auto config = vr_change->config_;
		auto cop_part = static_cast<ElectricPart*>(config->GetPart(PartTypeCop));
		if(!cop_part) return false;

		rse::vector<int> type_list;
		rse::vector<int64> id_list;
		rse::vector<tstring> lcd_list;

		int arr[4] = { PartTypeCop, PartTypeAuxCop, PartTypeHDCop, PartTypeAuxHDCop };
		for (int i = 0; i < sizeof(arr) / sizeof(arr[0]); ++i)
		{
			auto cop = static_cast<ElectricPart*>(config->GetPart(arr[i]));
			if (cop)
			{
				lcd_list.push_back(cop->GetLcdPath());
				type_list.push_back(cop->GetLcdType());
				id_list.push_back(cop->GetLcd());
			}
		}

		//if (GlobalInfoDataCommon::Instance()->IsOppositeDoor())
		//{
		//	for (int i = 0; i < sizeof(arr) / sizeof(arr[0]); ++i)
		//	{
		//		auto cop = static_cast<ElectricPart*>(config->GetPart(arr[i]));
		//		if (cop)
		//		{
		//			lcd_list.push_back(cop->GetLcdPath());
		//			type_list.push_back(cop->GetLcdType());
		//			id_list.push_back(cop->GetLcd());
		//		}
		//	}
		//}

		auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		for (int lcd_i = 0; lcd_i < lcd_list.size(); ++lcd_i)
		{
			tstring lcd_path = lcd_list[lcd_i];
			auto row = model_->AddRow();
			model_->Setm_bool_Select(row, true);
			auto path = Util::CombinePath(dir, lcd_path);
			AddFileToModelCache(type_list[lcd_i], id_list[lcd_i], lcd_path, path);
			model_->Setm_str_Path(row, path.c_str());
			model_->Setm_int_Mark(row, 0);
		}

		return true;
	}

	void VrCopDisplayVisitor::PrintData(const tchar* file_name)
	{

		model_->PrintData(file_name);

	}

}
    