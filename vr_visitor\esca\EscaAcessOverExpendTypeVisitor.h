#ifndef _VR_EscaAcessOverExpendTypeVisitor_H_
#define _VR_EscaAcessOverExpendTypeVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Access_Cover_ExpendType.h"

namespace decoration_vr_interface
{
	class VrEscaAcessOverExpendTypeVisitor : public BaseVisitor
	{
	public:
		VrEscaAcessOverExpendTypeVisitor();
		virtual ~VrEscaAcessOverExpendTypeVisitor();

		DEFINE_CREATE_FUN(VrEscaAcessOverExpendTypeVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<esca::Sel_Esca_Access_Cover_ExpendType> model_;
	};
}

#endif
