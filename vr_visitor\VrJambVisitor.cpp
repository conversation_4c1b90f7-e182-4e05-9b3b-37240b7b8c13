#include "stdafx.h"
#include "VrJambVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Jamb.h"
#include "material_channel.h"
#include "common_part.h"

namespace decoration_vr_interface
{

	VrJambVisitor::VrJambVisitor()
	{
        part_type_ = PartTypeJamb;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		InitializeAvailablePartTypes(types);

	} 

	VrJambVisitor::~VrJambVisitor()
	{
	}


	void VrJambVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Jamb>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), part_type_info->material_array_.c_str());
		}

		if (!material_header_)
		{
			material_header_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), TSTR("Material_Hall_Door_Header"));
		}
	}

	bool VrJambVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		model_->Clear();

		rse::vector<MaterialInfo> mat_info_list;
		rse::vector<MaterialInfo> math_info_list;
		auto elevator_list_ptr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->get_elevator_config_list_ptr();
		bool is_reflect = false;

		for (int elevator_i = 0; elevator_i < elevator_list_ptr->size(); ++elevator_i)
		{
			auto elevator = (*elevator_list_ptr)[elevator_i];
			IConfigPtr hall_config = elevator->GetHallConfig(0);
			auto part = static_cast<CommonPart*>(hall_config->GetPart(part_type_));
			is_reflect = part->GetIsHasReflect();
			if(part == nullptr) continue;

			auto row = model_->AddRow();
			model_->SetType(row, part->GetPartId());
			MaterialInfo info(part->GetEditPartMaterial(1), elevator_i * 100 + 1, part_type_);
			mat_info_list.push_back(std::move(info));

			if (part->GetEditPartMaterial(2) > 0) 
			{
				MaterialInfo infoh(part->GetEditPartMaterial(2), elevator_i * 100 + 1, 10010);
				math_info_list.push_back(std::move(infoh));
			}
		}

		auto res = VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), part_type_, mat_info_list, is_reflect);
		VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_header_.get(), 10010, math_info_list, is_reflect);
		return res >= 0;
	}

	void VrJambVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
		material_->PrintData(file_name);
	}

}
    