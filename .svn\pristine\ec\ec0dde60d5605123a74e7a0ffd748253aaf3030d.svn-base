#pragma once

#include "BasePartOperator.h"
namespace decoration_vr_interface
{
	class ConfigOperator : public BasePartOperator
	{
	public:
		ConfigOperator();
		~ConfigOperator();

		virtual bool SetPart(int part_type, int64 part_id) override;

		virtual bool SetPartByContent(int part_type, const rse::string& c) override;

	private:
		bool SetPart(IElevatorPartPtr part);
		bool IsHallChildPart(int cur_part_type);
	};

}