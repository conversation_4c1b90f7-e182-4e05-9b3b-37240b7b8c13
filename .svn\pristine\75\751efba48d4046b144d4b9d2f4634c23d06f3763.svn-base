#pragma once

#include "IElevatorPartFactory.h"

namespace decoration_vr_interface
{
	class ConfigFactory : public IElevatorPartFactory
	{
	public:
		ConfigFactory();
		~ConfigFactory();

		virtual int GetPartType() = 0;

		virtual IElevatorPartPtr CreatePart(FactoryArgs* param) override;

	protected:
		IElevatorPartPtr GetConfigFromJsonObject(FactoryArgs* args);

	};

	class CarConfigFactory : public ConfigFactory
	{
	public:
		virtual int GetPartType() override { return PartTypeCarConfig; }

		virtual IElevatorPartPtr CreatePart(FactoryArgs* param) override;
	};

	class HallConfigFactory : public ConfigFactory
	{
	public:
		virtual int GetPartType() override { return PartTypeHallConfig; }
	};

	class EscalatorConfigFactory : public ConfigFactory
	{
	public:
		virtual int GetPartType() override { return PartTypeEscalatorConfig; }
	};
}