﻿//
//  Global_Setting_Parameter.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Global_Setting_Parameter.h"


namespace decoration_vr_interface
{

bool Global_Setting_Parameter::Getplay_Type(int row)
{
    return visitor_->GetElementBool(row, 0);
}

void Global_Setting_Parameter::Setplay_Type(int row, bool val)
{
    visitor_->SetElementValue(row, 0, val);
}

int Global_Setting_Parameter::GettextureManager(int row)
{
    return visitor_->GetElementInt(row, 1);
}

void Global_Setting_Parameter::SettextureManager(int row, int val)
{
    visitor_->SetElementValue(row, 1, val);
}

int Global_Setting_Parameter::Getscreen_X(int row)
{
    return visitor_->GetElementInt(row, 2);
}

void Global_Setting_Parameter::Setscreen_X(int row, int val)
{
    visitor_->SetElementValue(row, 2, val);
}

int Global_Setting_Parameter::Getscreen_Y(int row)
{
    return visitor_->GetElementInt(row, 3);
}

void Global_Setting_Parameter::Setscreen_Y(int row, int val)
{
    visitor_->SetElementValue(row, 3, val);
}

int Global_Setting_Parameter::Getcam_Pos(int row)
{
    return visitor_->GetElementInt(row, 4);
}

void Global_Setting_Parameter::Setcam_Pos(int row, int val)
{
    visitor_->SetElementValue(row, 4, val);
}

int Global_Setting_Parameter::Getcam_Speed(int row)
{
    return visitor_->GetElementInt(row, 5);
}

void Global_Setting_Parameter::Setcam_Speed(int row, int val)
{
    visitor_->SetElementValue(row, 5, val);
}

float Global_Setting_Parameter::Getcam_High(int row)
{
    return visitor_->GetElementFloat(row, 6);
}

void Global_Setting_Parameter::Setcam_High(int row, float val)
{
    visitor_->SetElementValue(row, 6, val);
}

float Global_Setting_Parameter::Getcam_SavePosition_X(int row)
{
    return visitor_->GetElementFloat(row, 7);
}

void Global_Setting_Parameter::Setcam_SavePosition_X(int row, float val)
{
    visitor_->SetElementValue(row, 7, val);
}

float Global_Setting_Parameter::Getcam_SavePosition_Y(int row)
{
    return visitor_->GetElementFloat(row, 8);
}

void Global_Setting_Parameter::Setcam_SavePosition_Y(int row, float val)
{
    visitor_->SetElementValue(row, 8, val);
}

bool Global_Setting_Parameter::GetkeyControl(int row)
{
    return visitor_->GetElementBool(row, 10);
}

void Global_Setting_Parameter::SetkeyControl(int row, bool val)
{
    visitor_->SetElementValue(row, 10, val);
}

rse::string Global_Setting_Parameter::GetmodelPath(int row)
{
    const char* val = visitor_->GetElementString(row, 11);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Global_Setting_Parameter::SetmodelPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 11, val);
}

rse::string Global_Setting_Parameter::GettexturePath(int row)
{
    const char* val = visitor_->GetElementString(row, 12);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Global_Setting_Parameter::SettexturePath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 12, val);
}

int Global_Setting_Parameter::GetrecordFramerate(int row)
{
    return visitor_->GetElementInt(row, 13);
}

void Global_Setting_Parameter::SetrecordFramerate(int row, int val)
{
    visitor_->SetElementValue(row, 13, val);
}

int Global_Setting_Parameter::GetrecodCamPosition(int row)
{
    return visitor_->GetElementInt(row, 14);
}

void Global_Setting_Parameter::SetrecodCamPosition(int row, int val)
{
    visitor_->SetElementValue(row, 14, val);
}

bool Global_Setting_Parameter::GetDisplayMode(int row)
{
    return visitor_->GetElementBool(row, 15);
}

void Global_Setting_Parameter::SetDisplayMode(int row, bool val)
{
    visitor_->SetElementValue(row, 15, val);
}

bool Global_Setting_Parameter::GetPause(int row)
{
    return visitor_->GetElementBool(row, 16);
}

void Global_Setting_Parameter::SetPause(int row, bool val)
{
    visitor_->SetElementValue(row, 16, val);
}

bool Global_Setting_Parameter::GetReachedFloorNotify(int row)
{
    return visitor_->GetElementBool(row, 17);
}

void Global_Setting_Parameter::SetReachedFloorNotify(int row, bool val)
{
    visitor_->SetElementValue(row, 17, val);
}

int Global_Setting_Parameter::GetSetCurFloor(int row)
{
    return visitor_->GetElementInt(row, 18);
}

void Global_Setting_Parameter::SetSetCurFloor(int row, int val)
{
    visitor_->SetElementValue(row, 18, val);
}

float Global_Setting_Parameter::Getdoorgap(int row)
{
    return visitor_->GetElementFloat(row, 19);
}

void Global_Setting_Parameter::Setdoorgap(int row, float val)
{
    visitor_->SetElementValue(row, 19, val);
}

int Global_Setting_Parameter::Getis_network(int row)
{
    return visitor_->GetElementInt(row, 20);
}

void Global_Setting_Parameter::Setis_network(int row, int val)
{
    visitor_->SetElementValue(row, 20, val);
}

int Global_Setting_Parameter::GetBuild_Level(int row)
{
    return visitor_->GetElementInt(row, 21);
}

void Global_Setting_Parameter::SetBuild_Level(int row, int val)
{
    visitor_->SetElementValue(row, 21, val);
}

const tchar* Global_Setting_Parameter::GetPanronamaPath(int row)
{
	return visitor_->GetElementString(row, 29); 
}
void Global_Setting_Parameter::SetPanronamaPath(int row, const tchar* val)
{ 
	visitor_->SetElementValue(row, 29, val); 
}

int Global_Setting_Parameter::GetProjectType(int row)
{
	return visitor_->GetElementInt(row, 30);
}

void Global_Setting_Parameter::SetProjectType(int row, int val)
{
	visitor_->SetElementValue(row, 30, val);
}

float Global_Setting_Parameter::GetCameraFov(int row)
{
    if (visitor_->GetColumnCount() > 32)
    {
        return visitor_->GetElementFloat(row, 32);
    }
    return -10.0f;
}

void Global_Setting_Parameter::SetCameraFov(int row, float val)
{
    if (val > 0 && visitor_->GetColumnCount() > 32)
    {
        visitor_->SetElementValue(row, 32, val);
    }
}

}