#include "stdafx.h"
#include "svr_material.h"

#include "svr_json_helper.h"

namespace svr_data
{
	bool SvrMaterialInfo::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		if (!basic_info_.Parse(jv["basicInfo"]))
		{
			return false;
		}

		if (!ParseMaterialChannels(jv["channels"]))
		{
			return false;
		}

		if (!ParseMaterialSpecialRules(jv["rules"]))
		{
			return false;
		}

		if (!ParseFileDigitals(jv["fileDigitals"]))
		{
			return false;
		}

		return true;
	}

	bool SvrMaterialInfo::ParseMaterialChannels(const Json::Value& jv)
	{
		auto size = jv.size();
		channels_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!channels_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrMaterialInfo::ParseMaterialSpecialRules(const Json::Value& jv)
	{
		auto size = jv.size();
		rules_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!rules_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrMaterialInfo::ParseFileDigitals(const Json::Value& jv)
	{
		auto size = jv.size();
		file_digitals_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!file_digitals_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}
}