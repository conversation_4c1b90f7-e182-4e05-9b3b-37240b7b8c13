//
//  Sel_Mirror.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Mirror.h"


namespace decoration_vr_interface
{

bool Sel_Mirror::GetBool(int row)
{
    return visitor_->GetElementBool(row, 0);
}

void Sel_Mirror::SetBool(int row, bool val)
{
    visitor_->SetElementValue(row, 0, val);
}

rse::string Sel_Mirror::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 1);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Mirror::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 1, val);
}

float Sel_Mirror::GetSize_X(int row)
{
    return visitor_->GetElementFloat(row, 2);
}

void Sel_Mirror::SetSize_X(int row, float val)
{
    visitor_->SetElementValue(row, 2, val);
}

float Sel_Mirror::GetSize_Y(int row)
{
    return visitor_->GetElementFloat(row, 3);
}

void Sel_Mirror::SetSize_Y(int row, float val)
{
    visitor_->SetElementValue(row, 3, val);
}

float Sel_Mirror::GetPos_X(int row)
{
    return visitor_->GetElementFloat(row, 4);
}

void Sel_Mirror::SetPos_X(int row, float val)
{
    visitor_->SetElementValue(row, 4, val);
}

float Sel_Mirror::GetPos_Y(int row)
{
    return visitor_->GetElementFloat(row, 5);
}

void Sel_Mirror::SetPos_Y(int row, float val)
{
    visitor_->SetElementValue(row, 5, val);
}

int Sel_Mirror::GetLocation(int row)
{
    return visitor_->GetElementInt(row, 6);
}

void Sel_Mirror::SetLocation(int row, int val)
{
    visitor_->SetElementValue(row, 6, val);
}

}