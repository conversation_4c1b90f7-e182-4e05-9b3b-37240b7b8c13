﻿#include "stdafx.h"
#include "ConstValueMap.h"

namespace decoration_vr_interface
{

	int ConstValueMap::ToGeneralVrCopSetupPos(int posForDb)
	{
		switch (posForDb)
		{
		case kCopWallLeft:
			return kCspCopLeft;
		case kFrontWallRight:
			return kCspFrontRight;
		case kFrontWall:
			return kCspFrontCenter;
		case kCopWall:
			return kCspCopCenter;
		case kLeftWall:
			return kCspLeft;
		case kRightWall:
			return kCspRight;
		case kBackWall:
			return kCspBack;
		case kBackWallLeft:
			return kCspBackLeft;
		case kBackWallCenter:
			return kCspBackCenter;
		default:
			return posForDb;
			//DECORATION_ASSERT(false);
		}
	}

}