//
//  Sel_Mirror.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Mirror : public DGBaseVisitor
{
public:
	Sel_Mirror(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	bool GetBool(int row);
	void SetBool(int row, bool val);

	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	float GetSize_X(int row);
	void SetSize_X(int row, float val);

	float GetSize_Y(int row);
	void SetSize_Y(int row, float val);

	float GetPos_X(int row);
	void SetPos_X(int row, float val);

	float GetPos_Y(int row);
	void SetPos_Y(int row, float val);

	int GetLocation(int row);
	void SetLocation(int row, int val);

};
}