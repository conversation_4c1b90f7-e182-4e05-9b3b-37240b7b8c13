#include "stdafx.h"
#include "ConfigOperator.h"

namespace decoration_vr_interface
{
	ConfigOperator::ConfigOperator()
	{
	}

	ConfigOperator::~ConfigOperator()
	{
	}

	bool ConfigOperator::SetPart(int part_type, int64 part_id)
	{
		auto part = CreatePart(part_type, part_id);
		return SetPart(part);
	}

	bool ConfigOperator::SetPartByContent(int part_type, const rse::string& c)
	{
		auto gd = GlobalInfoDataCommon::Instance();
#ifdef RENDER_SKETCHER_ANDROID
		LOGI("PartType=%d, Begin load content from net...", part_type);
#endif
		auto part = CreatePart(part_type, c);

#ifdef RENDER_SKETCHER_ANDROID
		LOGI("PartType=%d, End load content from net", part_type);
#endif

		auto ret = SetPart(part);
		if (ret)
		{
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("Successfully create part, PartType=%d", part_type);
#endif
			gd->ReportInfoToHost( "Successfully create part, PartType=%d", part_type);
		}
		else
		{
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("Failed to create part, PartType=%d", part_type);
#endif
			gd->ReportInfoToHost( "Failed to create part, PartType=%d", part_type);
		}

		return ret;
	}

	bool ConfigOperator::SetPart(IElevatorPartPtr part)
	{
		if (!part)
		{
			return false;
		}

		auto part_type = part->GetPartType();
		auto config = std::static_pointer_cast<IConfig>(part);

		auto cur_elva_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig();
		if (part_type == PartTypeCarConfig)
		{
			cur_elva_config->SetCarConfig(config);
		}
		else if (part_type == PartTypeHallConfig)
		{
			cur_elva_config->SetHallConfig(0, config);
		}
		else if (part_type == PartTypeEscalatorConfig)
		{
			cur_elva_config->SetEscalatorConfig(config);
		}
		else
		{
			auto gd = GlobalInfoDataCommon::Instance();
			int part_type = gd->cur_part_type_;

			if (IsHallChildPart(part_type))
			{
				auto cur_hall_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentHallConfig();
				cur_hall_config->SetPart(part_type, part);
			}
			else
			{
				auto cur_car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentCarConfig();

				auto parttype_reflect = GlobalInfoDataCommon::Instance()->partType_reflect_;
				int count = parttype_reflect.size();
				bool is_reflect = false;

				for (int i = 0; i < count; i++)
				{
					if (parttype_reflect[i] == part_type)
					{
						is_reflect = true;
					}
				}
				part->SetIsHasReflect(is_reflect);

				cur_car_config->SetPart(part_type, part);
			}
		}

		return true;
	}

	bool ConfigOperator::IsHallChildPart(int cur_part_type)
	{
		bool result = false;

		switch (cur_part_type)
		{
			case PartTypeJamb:
			case PartTypeHallDoor:
			case PartTypeHallDoorTrim:
			case PartTypeLop:
			case PartTypeLopButton:
			case PartTypeLopDisplay:
			case PartTypeHIBPlate:
			case PartTypeLantern:
			case PartTypeLanternDisplay:
			case PartTypeHallIndicator:
			case PartTypeHallIndicatorDisplay:
			case PartTypeHallIndicatorPlate:
			case PartTypeHallWall:
			case PartTypeHallBottom:
			case PartTypeHallFireBox:
			{
				result = true;
			}
			break;
		}

		return result;
	}
}