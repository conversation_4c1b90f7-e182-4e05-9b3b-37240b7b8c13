#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Sel_Esca_Balustrade : public DGBaseVisitor
		{
		public:
			Sel_Esca_Balustrade(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			rse::string Getstr_Path(int row);
			void Setstr_Path(int row, const tchar* val);

			rse::string Getstr_piecePath(int row);
			void Setstr_piecePath(int row, const tchar* val);

		};
	}

}