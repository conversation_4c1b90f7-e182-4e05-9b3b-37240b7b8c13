#ifndef _SVR_CONFIG_H_
#define _SVR_CONFIG_H_

#include "svr_base_data.h"
#include "svr_part_basic_info.h"
#include "svr_part_material.h"
#include "svr_material_channel.h"
#include "svr_material_special_rule.h"
#include "svr_file_digital_info.h"
#include "svr_part_model_info.h"

#include "svr_material.h"
#include "svr_common_part.h"
#include "svr_car_wall.h"

namespace svr_data
{

	class SvrExhibitConfig : public SvrExhibitCommonPart
	{
	public:
		bool Parse(const Json::Value& jobj) override;

	public:
		rse::vector<int64_t> SetupPositionDatas;

	private:
		bool ParseSetupPositionDatas(const Json::Value& jobj);
	};

	class SvrExhibitCarConfig : public SvrExhibitConfig
	{
	public:
		SvrExhibitCarConfig();
		bool Parse(const Json::Value& jobj) override;

	public:
		rse::vector<SvrWallData> Walls;
		rse::vector<int32_t> FitElevatorSizes;
		int32_t FrontWallType;
		float DoorWidth;
		float DoorHeight;
		int FloorCount;
	private:
		bool ParseWalls(const Json::Value& jobj);

		bool ParsFitElevatorSizes(const Json::Value& jobj);

	};
}
#endif
