#pragma once
#include "esca_elevator_part.h"
#include "IElevatorPartFactory.h"

#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{
	//typedef CommonPartFactory EscalatorPartFactory;
	class EscalatorPartFactory :public IElevatorPartFactory
	{
	public:
		EscalatorPartFactory(int part_type);
		virtual ~EscalatorPartFactory();

		void SetPartType(int ty);
		virtual int GetPartType() override;

		virtual IElevatorPartPtr CreatePart(FactoryArgs* param) override;

	protected:
		virtual void SetPartInfo(IElevatorPart* part, svr_data::SvrCommonPart* svr_part);
		virtual void SetMaterial(IElevatorPart* part, const  rse::vector<svr_data::SvrPartMaterial>& mats);
	protected:
		IElevatorPartPtr GetPartFromJsonConfig(FactoryArgs* args);
		IElevatorPartPtr GetPartFromJsonObject(FactoryArgs* args);

		void SetPartPosition(IElevatorPart* part, FactoryArgs* args);

	protected:
		int part_type_;
	};
}