#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_HandRail;

class VrHandrailVisitor : public BaseVisitor
{
public:
	VrHandrailVisitor();
	~VrHandrailVisitor();

	DEFINE_CREATE_FUN(VrHandrailVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	void ScriptHandrailAndCop(int row, VrChangeArg* vrChange);
	int GetHandrailPos(IConfig* config);
	int ScripteSub_GetWallHandrailType(VrChangeArg* vrChange, int partType);
	bool IsNCopSideWallType(int sideWallHandrailType);
	std::shared_ptr<Sel_HandRail> model_;
};
}
