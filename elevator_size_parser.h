#pragma once
#ifndef _ELEVATOR_SIZE_PARSER_H_
#define _ELEVATOR_SIZE_PARSER_H_

namespace decoration_vr_interface
{
	struct ElevatorSpecification;
	class ElevatorSpecificationParser
	{
	public:
		ElevatorSpecificationParser();
		~ElevatorSpecificationParser();

		static bool GetElevatorSpecification(ElevatorSpecification& es, const rse::map<tstring, tstring>& m_);
		static bool GetElevatorSpecificationsWithHeight(ElevatorSpecification& es, const rse::map<tstring, tstring>& m_);
		static void GetHandrailPosMark(int &left, int &right, int &back, const rse::map<tstring, tstring>& m_);
		static bool GetEscalatorElevSpecification(ElevatorSpecification& es, const rse::map<tstring, tstring>& m_);
	};


}

#endif

