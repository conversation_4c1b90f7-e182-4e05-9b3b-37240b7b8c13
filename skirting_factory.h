#ifndef _SKIRTING_FACTORY_H_
#define _SKIRTING_FACTORY_H_

#include "IElevatorPartFactory.h"

namespace decoration_vr_interface
{
	class SkirtingPartFactory :public IElevatorPartFactory
	{
	public:
		SkirtingPartFactory(int part_type);
		virtual ~SkirtingPartFactory();

		void SetPartType(int ty);
		virtual int GetPartType() override;

		virtual IElevatorPartPtr CreatePart(FactoryArgs* param) override;

	private:
		int part_type_;
	};
}

#endif//_SKIRTING_FACTORY_H_

