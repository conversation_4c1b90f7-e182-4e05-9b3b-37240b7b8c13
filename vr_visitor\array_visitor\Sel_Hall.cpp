//
//  Sel_Hall.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Hall.h"


namespace decoration_vr_interface
{

rse::string Sel_Hall::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 0);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Hall::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 0, val);
}

}