#include "stdafx.h"
#include "i_decoration_vr_bind.h"

#if defined(RENDER_SKETCHER_HTML)

class IDecorationVrCallBack_wrapper : public wrapper<IHTMLDecorationVrCallBack>
{
public:

	EMSCRIPTEN_WRAPPER(IDecorationVrCallBack_wrapper);

	void LoadCompleteCallBack(int a, int b, const std::string& c)
	{
		return call<void>("LoadCompleteCallBack", a, b, c);
	}

	std::string DownloadData(const std::string& download_url, const std::string& post_data)
	{
		return call<std::string>("DownloadData", download_url, post_data);
	}

	bool DownloadFile(const std::string& download_url, const std::string& file_abs_path)
	{
		return call<bool>("DownloadFile", download_url, file_abs_path);
	}

	bool DownloadFileEx(const rse::string& download_url, const rse::string& file_abs_path, const rse::string& file_digital)
	{
		return call<bool>("DownloadFileEx", download_url, file_abs_path, file_digital);
	}

	void OnProgress(int category, int progress, const std::string& info)
	{
		return call<void>("OnProgress", category, progress, info);
	}

	bool GetReceiveProgress()
	{
		return call<bool>("GetReceiveProgress");
	}

	void SetReceiveProgress(bool val)
	{
		return call<void>("SetReceiveProgress", val);
	}

	void SetDownloadFileCount(int file_count)
	{
		return call<void>("SetDownloadFileCount", file_count);
	}

	int GetFileFromLruCache(const std::string& file_abs_path, const std::string& digital)
	{
		return call<int>("GetFileFromLruCache", file_abs_path, digital);
	}
};

EMSCRIPTEN_BINDINGS(vr_bind)
{
	class_<IHTMLDecorationVrCallBack>("IHTMLDecorationVrCallBack")
		.function("LoadCompleteCallBack", &IHTMLDecorationVrCallBack::LoadCompleteCallBack, pure_virtual())
		.function("DownloadData", &IHTMLDecorationVrCallBack::DownloadData, pure_virtual())
		.function("DownloadFile", &IHTMLDecorationVrCallBack::DownloadFile, pure_virtual())
		.function("DownloadFileEx", &IHTMLDecorationVrCallBack::DownloadFileEx, pure_virtual())
		.function("OnProgress", &IHTMLDecorationVrCallBack::OnProgress, pure_virtual())
		.function("GetReceiveProgress", &IHTMLDecorationVrCallBack::GetReceiveProgress, pure_virtual())
		.function("SetReceiveProgress", &IHTMLDecorationVrCallBack::SetReceiveProgress, pure_virtual())
		.function("SetDownloadFileCount", &IHTMLDecorationVrCallBack::SetDownloadFileCount, pure_virtual())
		.function("GetFileFromLruCache", &IHTMLDecorationVrCallBack::GetFileFromLruCache, pure_virtual())
		.allow_subclass<IDecorationVrCallBack_wrapper>("IDecorationVrCallBack_wrapper")
		;

	class_<HTMLDecorationVrInterface>("HTMLDecorationVrInterface")
		.constructor<>()
		.function("InitializeElevator", &HTMLDecorationVrInterface::InitializeElevator, allow_raw_pointers())
		.function("SetDir", &HTMLDecorationVrInterface::SetDir)
		.function("Finalize", &HTMLDecorationVrInterface::Finalize)
		.function("DownloadFileCompleted", &HTMLDecorationVrInterface::DownloadFileCompleted)
		.function("SetPart", &HTMLDecorationVrInterface::SetPart)
		.function("SetMaterial", &HTMLDecorationVrInterface::SetMaterial)
		.function("SetPartByGoods", &HTMLDecorationVrInterface::SetPartByGoods)
		.function("SetMaterialByGoods", &HTMLDecorationVrInterface::SetMaterialByGoods)
		.function("SetHandrailPos", &HTMLDecorationVrInterface::SetHandrailPos)
		.function("SetContentString", &HTMLDecorationVrInterface::SetContentString)
		.function("ResizeRender", &HTMLDecorationVrInterface::ResizeRender)
		.function("Resize", &HTMLDecorationVrInterface::Resize)
		.function("SendVrMessage", &HTMLDecorationVrInterface::SendVrMessage)
		.function("DoVrProcess", &HTMLDecorationVrInterface::DoVrProcess)
		.function("SetDownloadServerURL", &HTMLDecorationVrInterface::SetDownloadServerURL)
		//.function("GestureDetector", &HTMLDecorationVrInterface::GestureDetector)
		.function("SetHTMLCallBack", &HTMLDecorationVrInterface::SetHTMLCallBack, allow_raw_pointers())
		.function("SelectType", &HTMLDecorationVrInterface::SelectType)
		.function("SendVrMessageWithParas", &HTMLDecorationVrInterface::SendVrMessageWithParas)
		.function("DataArrayAddRow", &HTMLDecorationVrInterface::DataArrayAddRow)
		.function("DataArrayGetRowCount", &HTMLDecorationVrInterface::DataArrayGetRowCount)
		.function("DataArrayGetColumnCount", &HTMLDecorationVrInterface::DataArrayGetColumnCount)
		.function("DataArrayDeleteRow", &HTMLDecorationVrInterface::DataArrayDeleteRow)
		.function("DataArrayClear", &HTMLDecorationVrInterface::DataArrayClear)
		.function("DataArrayGetElementInt", &HTMLDecorationVrInterface::DataArrayGetElementInt)
		.function("DataArraySetElementInt", &HTMLDecorationVrInterface::DataArraySetElementInt)
		.function("DataArrayGetElementBool", &HTMLDecorationVrInterface::DataArrayGetElementBool)
		.function("DataArraySetElementBool", &HTMLDecorationVrInterface::DataArraySetElementBool)
		.function("DataArrayGetElementFloat", &HTMLDecorationVrInterface::DataArrayGetElementFloat)
		.function("DataArraySetElementFloat", &HTMLDecorationVrInterface::DataArraySetElementFloat)
		.function("DataArrayGetElementString", &HTMLDecorationVrInterface::DataArrayGetElementString)
		.function("DataArraySetElementString", &HTMLDecorationVrInterface::DataArraySetElementString)
		.function("DataArrayGetElementCompound", &HTMLDecorationVrInterface::DataArrayGetElementCompound)
		.function("DataArraySetElementCompound", &HTMLDecorationVrInterface::DataArraySetElementCompound)
		.function("RenderSmallImage", &HTMLDecorationVrInterface::RenderSmallImage)
		;

	class_<ElevatorSpecification>("ElevatorSpecification")
		.constructor<>()
		.property("elevator_size_id_", &ElevatorSpecification::elevator_size_id_)
		.property("width_", &ElevatorSpecification::width_)
		.property("depth_", &ElevatorSpecification::depth_)
		.property("height_", &ElevatorSpecification::height_)
		.property("door_width_", &ElevatorSpecification::door_width_)
		.property("door_height_", &ElevatorSpecification::door_height_)
		.property("elevator_count_", &ElevatorSpecification::elevator_count_)
		.property("current_serial_", &ElevatorSpecification::current_serial_)
		.property("default_door_open_type_", &ElevatorSpecification::default_door_open_type_)
		.property("default_floor_count_", &ElevatorSpecification::default_floor_count_)
		;
}

#endif