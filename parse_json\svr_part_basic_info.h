#ifndef _SVR_PART_BASIC_INFO_H_
#define _SVR_PART_BASIC_INFO_H_

#include "svr_base_data.h"

namespace svr_data
{
	class SvrPartBasicInfo : public ISvrBaseData
	{
	public:
		SvrPartBasicInfo() : PartType(-1), Id(-1), IntVal1(0), IntVal2(0), IntVal3(0), IntVal4(0), IntVal5(0), IntVal6(0), IntVal7(0), IntVal8(0)
			, FloatVal1(0), FloatVal2(0), FloatVal3(0), FloatVal4(0), Name(TSTR("")), StringVal1(TSTR("")), StringVal2(TSTR(""))
			, StringVal3(TSTR("")), StringVal4(TSTR("")), LangStringVal1(TSTR("")), LangStringVal2(TSTR("")), LangStringVal3(TSTR(""))
			, LangStringVal4(TSTR("")), LangStringVal5(TSTR("")), Note(TSTR("")), PicPath(TSTR("")) {}

		virtual bool Parse(const Json::Value& jobj) override;

	public:
		int32_t PartType;
		int64_t Id;
		int64_t IntVal1;
		int64_t IntVal2;
		int64_t IntVal3;
		int64_t IntVal4;
		int64_t IntVal5;
		int64_t IntVal6;
		int64_t IntVal7;
		int64_t IntVal8;
		float FloatVal1;
		float FloatVal2;
		float FloatVal3;
		float FloatVal4;
		rse::string StringVal1;
		rse::string StringVal2;
		rse::string StringVal3;
		rse::string StringVal4;
		rse::string StringVal5;
		rse::string Name;
		rse::string LangStringVal1;
		rse::string LangStringVal2;
		rse::string LangStringVal3;
		rse::string LangStringVal4;
		rse::string LangStringVal5;
		rse::string Note;
		rse::string PicPath;
		int64_t EditUserForRead;
		rse::string EditDateForRead;
		int32_t EditStateForRead;
		int64_t OwnerLibraryForRead;
		int64_t ReferenceUserForRead;
	};

}//namespace svr_data

#endif//_PART_BASIC_INFO_H_

