#include "stdafx.h"
#include "html_decoration_vr_interface.h"
#include "VrGlobalInfo.h"
#include "decoration_vr_interface_lib.h"
#include <stdio.h>

namespace decoration_vr_interface
{

	HTMLDecorationVrInterface::HTMLDecorationVrInterface()
	{
	}

	void HTMLDecorationVrInterface::SelectType(int part_type)
	{
		MsgParaValue para("PartType", MPVT_INT32, part_type);
		GetDecorationVrInterface()->SendVrMessage("line_frame_selected", &para, 1, 1);
	}

	void HTMLDecorationVrInterface::SetPart(int part_type, const std::string& long_id, bool notify)
	{
		int64 part_id = atoll(long_id.c_str());
		GetDecorationVrInterface()->SetPart(part_type, part_id, notify);
	}

	void HTMLDecorationVrInterface::SetMaterial(int part_type, const std::string& long_id, bool notify)
	{
		int64 part_id = atoll(long_id.c_str());
		GetDecorationVrInterface()->SetMaterial(part_type, part_id, notify);
	}

	void HTMLDecorationVrInterface::SetPartByGoods(int part_type, const std::string& long_id, bool notify)
	{
		int64 goods_id = atoll(long_id.c_str());
		GetDecorationVrInterface()->SetPartByGoods(part_type, goods_id, notify);
	}

	void HTMLDecorationVrInterface::SetMaterialByGoods(int part_type, const std::string& long_id, bool notify)
	{
		int64 goods_id = atoll(long_id.c_str());
		GetDecorationVrInterface()->SetMaterialByGoods(part_type, goods_id, notify);
	}

	void HTMLDecorationVrInterface::SetHandrailPos(int pos, bool notify)
	{
		GetDecorationVrInterface()->SetHandrailPos(pos, notify);
	}

	void HTMLDecorationVrInterface::InitializeElevator(const ElevatorSpecification* elev_spec, int flag, int win_width, int win_height, const std::string& app_dir)
	{
		auto gd = GlobalInfoDataCommon::Instance();
		GetDecorationVrInterface()->InitEngine();
		
		GetDecorationVrInterface()->SetResourcesPath(app_dir.c_str());
		SetDir(app_dir, app_dir);
		GetDecorationVrInterface()->CreateRender(nullptr, win_width, win_height, 1);

		GetDecorationVrInterface()->LoadScene();
	
		GetDecorationVrInterface()->InitElevator(elev_spec);
	
		GetDecorationVrInterface()->SetDecorationVrCallBack(&call_back_);

	}

	void HTMLDecorationVrInterface::Finalize()
	{
		GetDecorationVrInterface()->FinalizeEngine();
		DeleteDecorationVrInterface();
#if defined(RENDER_SKETCHER_HTML)
		DeleteHTMLDecorationVrInterface();
#endif
	}

	void HTMLDecorationVrInterface::SetDir(const std::string& app_dir, const std::string& home_dir)
	{
		GetDecorationVrInterface()->SetDir(app_dir.c_str(), home_dir.c_str());
	}

	void HTMLDecorationVrInterface::SetLoginInfo(int lang_id, const std::string& lib_str, const std::string& user_str)
	{
		int64 lib_id = atoll(lib_str.c_str());
		int64 usr_id = atoll(user_str.c_str());
		GetDecorationVrInterface()->SetLoginInfo(lib_id, lang_id, usr_id);
	}

	void HTMLDecorationVrInterface::SetDownloadServerURL(const std::string& url, const std::string& down_file_root)
	{
		GetDecorationVrInterface()->SetDownloadServerURL(url.c_str(), down_file_root.c_str());
	}

	void HTMLDecorationVrInterface::SetContentString(const std::string& content)
	{
		GetDecorationVrInterface()->SetContentString(content.c_str());
		return;
	}

	void HTMLDecorationVrInterface::Resize(float width, float height)
	{
		GetDecorationVrInterface()->Resize(width, height);
	}

	void HTMLDecorationVrInterface::ResizeRender(int w, int h)
	{
		GetDecorationVrInterface()->ResizeRender(w, h);
	}

	void HTMLDecorationVrInterface::DoVrProcess()
	{
		GetDecorationVrInterface()->DoVrProcess();
	}

	void HTMLDecorationVrInterface::SendVrMessage(const std::string& msg, int delay_frame)
	{
		GetDecorationVrInterface()->SendVrMessage(msg.c_str(), delay_frame);
	}

	void HTMLDecorationVrInterface::SetHTMLCallBack(IHTMLDecorationVrCallBack* callback)
	{
		call_back_.SetHTMLCallBack(callback);
	}

	bool HTMLDecorationVrInterface::DownloadFileCompleted(const std::string& file)
	{
		return GetDecorationVrInterface()->DownloadFileCompleted(file.c_str());
	}

	void HTMLDecorationVrInterface::GestureDetector(int gesture_type, int gesture_state, float x, float y)
	{
		//GetDecorationVrInterface()->GestureDetector(gesture_type, gesture_state, x, y);
	}

	bool HTMLDecorationVrInterface::SendVrMessageWithParas(const std::string &msg, const std::string &param_json_values, int delay_frame)
	{
		return GetDecorationVrInterface()->SendVrMessageWithParas(msg.c_str(), param_json_values.c_str(), param_json_values.size(), delay_frame);
	}

	void HTMLDecorationVrInterface::DataArrayAddRow(const std::string &arr_name)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->AddRow();
	}

	int HTMLDecorationVrInterface::DataArrayGetRowCount(const std::string &arr_name)
	{
		auto arr = GetDataArrayByName(arr_name);
		return arr->GetRowCount();
	}

	int HTMLDecorationVrInterface::DataArrayGetColumnCount(const std::string &arr_name)
	{
		auto arr = GetDataArrayByName(arr_name);
		return arr->GetColumnCount();
	}

	void HTMLDecorationVrInterface::DataArrayDeleteRow(const std::string &arr_name, int row)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->DeleteRow(row);
	}

	void HTMLDecorationVrInterface::DataArrayClear(const std::string &arr_name)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->Clear();
	}

	int HTMLDecorationVrInterface::DataArrayGetElementInt(const std::string &arr_name, int row, int col)
	{
		auto arr = GetDataArrayByName(arr_name);
		return arr->GetElementInt(row, col);
	}

	void HTMLDecorationVrInterface::DataArraySetElementInt(const std::string &arr_name, int row, int col, int val)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->SetElementValue(row, col, val);
	}

	bool HTMLDecorationVrInterface::DataArrayGetElementBool(const std::string &arr_name, int row, int col)
	{
		auto arr = GetDataArrayByName(arr_name);
		return arr->GetElementBool(row, col);
	}

	void HTMLDecorationVrInterface::DataArraySetElementBool(const std::string &arr_name, int row, int col, bool val)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->SetElementValue(row, col, val);
	}

	float HTMLDecorationVrInterface::DataArrayGetElementFloat(const std::string &arr_name, int row, int col)
	{
		auto arr = GetDataArrayByName(arr_name);
		return arr->GetElementFloat(row, col);
	}

	void HTMLDecorationVrInterface::DataArraySetElementFloat(const std::string &arr_name, int row, int col, float val)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->SetElementValue(row, col, val);
	}

	std::string HTMLDecorationVrInterface::DataArrayGetElementString(const std::string &arr_name, int row, int col)
	{
		auto arr = GetDataArrayByName(arr_name);
		const char* pval = arr->GetElementString(row, col);
		std::string ret = pval;
		VrGlobalInfo::Instance()->get_dg_lib_mgr()->ReleaseLibBuf(pval);
		return ret;
	}

	void HTMLDecorationVrInterface::DataArraySetElementString(const std::string &arr_name, int row, int col, const std::string &val)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->SetElementValue(row, col, val.c_str());
	}

	std::string HTMLDecorationVrInterface::DataArrayGetElementCompound(const std::string &arr_name, int row, int col, int nCompoundTypeId)
	{
		auto arr = GetDataArrayByName(arr_name);
		const char* pval = arr->GetElementCompound(row, col, nCompoundTypeId);
		std::string ret = pval;
		VrGlobalInfo::Instance()->get_dg_lib_mgr()->ReleaseLibBuf(pval);
		return ret;
	}

	void HTMLDecorationVrInterface::DataArraySetElementCompound(const std::string &arr_name, int row, int col, int nCompoundTypeId, const std::string &val)
	{
		auto arr = GetDataArrayByName(arr_name);
		arr->SetElementCompound(row, col, nCompoundTypeId, val.c_str());
	}

	IDGArrayVisitor* HTMLDecorationVrInterface::GetDataArrayByName(const std::string &arr_name)
	{
		auto gd = GlobalInfoDataCommon::Instance();
		gd->ReportInfoToHost("GetDataArrayByName: %s",arr_name.c_str());

		auto iter = cached_array_.find(arr_name);
		if (iter != cached_array_.end())
		{
			return iter->second;
		}
		else
		{
			IDGArrayVisitor* v = VrGlobalInfo::Instance()->get_dg_scene()->GetSceneDataReceiver(arr_name.c_str());
			cached_array_.insert(std::make_pair(arr_name, v));
			return v;
		}
	}

	void HTMLDecorationVrInterface::RenderSmallImage(const std::string& file_path, int width, int height)
	{
		GetDecorationVrInterface()->RenderSmallImage(file_path.c_str(), width, height);
	}

}