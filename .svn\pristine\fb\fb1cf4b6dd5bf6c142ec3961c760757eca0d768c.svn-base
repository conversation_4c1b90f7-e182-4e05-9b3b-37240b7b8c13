#ifndef _SHA_H_
#define _SHA_H_

#include <stddef.h>
#include <stdint.h>

namespace util
{
	struct sha256_ctx
	{
		uint8_t  digest_len;
		uint64_t count;
		uint8_t  buffer[64];
		uint32_t state[8];
	};

	int  sha256_init(sha256_ctx *ctx);

	void sha256_update(sha256_ctx* ctx, const uint8_t* data, unsigned int len);

	void sha256_final(sha256_ctx* ctx, uint8_t *digest);

}//namespace util

#endif//_SHA_H_
