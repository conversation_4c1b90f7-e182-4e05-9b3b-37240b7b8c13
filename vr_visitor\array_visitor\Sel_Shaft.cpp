#include "stdafx.h"
#include "Sel_Shaft.h"

namespace decoration_vr_interface
{
	Sel_Shaft::Sel_Shaft(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name)
	{

	}

	bool Sel_Shaft::Get_Select(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Shaft::Set_Select(int row, bool val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	rse::string Sel_Shaft::Get_Path(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Shaft::Set_Path(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	void Sel_Shaft::Set_Width(int row, float val)
	{
		visitor_->SetElementValue(row, 3, val);
	}

	void Sel_Shaft::Set_Depth(int row, float val)
	{
		visitor_->SetElementValue(row, 4, val);
	}

	//int Sel_Shaft::Get_Mark(int row)
	//{
	//	return visitor_->GetElementInt(row, 2);
	//}

	//void Sel_Shaft::Set_Mark(int row, int val)
	//{
	//	visitor_->SetElementValue(row, 2, val);
	//}

}
