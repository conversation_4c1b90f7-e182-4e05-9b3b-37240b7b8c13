#include "stdafx.h"
#include "Global_Esca_Setting_Parameter.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		bool Global_Esca_Setting_Parameter::Getplay_Type(int row)
		{
			return visitor_->GetElementBool(row, 0);
		}

		void Global_Esca_Setting_Parameter::Setplay_Type(int row, bool val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Global_Esca_Setting_Parameter::GettextureManager(int row)
		{
			return visitor_->GetElementInt(row, 1);
		}

		void Global_Esca_Setting_Parameter::SettextureManager(int row, int val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		int Global_Esca_Setting_Parameter::Getscreen_X(int row)
		{
			return visitor_->GetElementInt(row, 2);
		}

		void Global_Esca_Setting_Parameter::Setscreen_X(int row, int val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

		int Global_Esca_Setting_Parameter::Getscreen_Y(int row)
		{
			return visitor_->GetElementInt(row, 3);
		}

		void Global_Esca_Setting_Parameter::Setscreen_Y(int row, int val)
		{
			visitor_->SetElementValue(row, 3, val);
		}

		int Global_Esca_Setting_Parameter::Getcam_Pos(int row)
		{
			return visitor_->GetElementInt(row, 4);
		}

		void Global_Esca_Setting_Parameter::Setcam_Pos(int row, int val)
		{
			visitor_->SetElementValue(row, 4, val);
		}

		int Global_Esca_Setting_Parameter::Getcam_Speed(int row)
		{
			return visitor_->GetElementInt(row, 5);
		}

		void Global_Esca_Setting_Parameter::Setcam_Speed(int row, int val)
		{
			visitor_->SetElementValue(row, 5, val);
		}

		float Global_Esca_Setting_Parameter::Getcam_High(int row)
		{
			return visitor_->GetElementFloat(row, 6);
		}

		void Global_Esca_Setting_Parameter::Setcam_High(int row, float val)
		{
			visitor_->SetElementValue(row, 6, val);
		}

		float Global_Esca_Setting_Parameter::Getcam_SavePosition_X(int row)
		{
			return visitor_->GetElementFloat(row, 7);
		}

		void Global_Esca_Setting_Parameter::Setcam_SavePosition_X(int row, float val)
		{
			visitor_->SetElementValue(row, 7, val);
		}

		float Global_Esca_Setting_Parameter::Getcam_SavePosition_Y(int row)
		{
			return visitor_->GetElementFloat(row, 8);
		}

		void Global_Esca_Setting_Parameter::Setcam_SavePosition_Y(int row, float val)
		{
			visitor_->SetElementValue(row, 8, val);
		}

		bool Global_Esca_Setting_Parameter::GetkeyControl(int row)
		{
			return visitor_->GetElementBool(row, 10);
		}

		void Global_Esca_Setting_Parameter::SetkeyControl(int row, bool val)
		{
			visitor_->SetElementValue(row, 10, val);
		}

		rse::string Global_Esca_Setting_Parameter::GetmodelPath(int row)
		{
			const char* val = visitor_->GetElementString(row, 11);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_Esca_Setting_Parameter::SetmodelPath(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 11, val);
		}

		rse::string Global_Esca_Setting_Parameter::GettexturePath(int row)
		{
			const char* val = visitor_->GetElementString(row, 12);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_Esca_Setting_Parameter::SettexturePath(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 12, val);
		}

		int Global_Esca_Setting_Parameter::GetrecordFramerate(int row)
		{
			return visitor_->GetElementInt(row, 13);
		}

		void Global_Esca_Setting_Parameter::SetrecordFramerate(int row, int val)
		{
			visitor_->SetElementValue(row, 13, val);
		}

		int Global_Esca_Setting_Parameter::GetrecodCamPosition(int row)
		{
			return visitor_->GetElementInt(row, 14);
		}

		void Global_Esca_Setting_Parameter::SetrecodCamPosition(int row, int val)
		{
			visitor_->SetElementValue(row, 14, val);
		}

		bool Global_Esca_Setting_Parameter::GetDisplayMode(int row)
		{
			return visitor_->GetElementBool(row, 15);
		}

		void Global_Esca_Setting_Parameter::SetDisplayMode(int row, bool val)
		{
			visitor_->SetElementValue(row, 15, val);
		}

		bool Global_Esca_Setting_Parameter::GetPause(int row)
		{
			return visitor_->GetElementBool(row, 16);
		}

		void Global_Esca_Setting_Parameter::SetPause(int row, bool val)
		{
			visitor_->SetElementValue(row, 16, val);
		}

		bool Global_Esca_Setting_Parameter::GetReachedFloorNotify(int row)
		{
			return visitor_->GetElementBool(row, 17);
		}

		void Global_Esca_Setting_Parameter::SetReachedFloorNotify(int row, bool val)
		{
			visitor_->SetElementValue(row, 17, val);
		}

		int Global_Esca_Setting_Parameter::GetSetCurFloor(int row)
		{
			return visitor_->GetElementInt(row, 18);
		}

		void Global_Esca_Setting_Parameter::SetSetCurFloor(int row, int val)
		{
			visitor_->SetElementValue(row, 18, val);
		}

		float Global_Esca_Setting_Parameter::Getdoorgap(int row)
		{
			return visitor_->GetElementFloat(row, 19);
		}

		void Global_Esca_Setting_Parameter::Setdoorgap(int row, float val)
		{
			visitor_->SetElementValue(row, 19, val);
		}

		bool Global_Esca_Setting_Parameter::GetInterface(int row)
		{
			return visitor_->GetElementBool(row, 20);
		}

		void Global_Esca_Setting_Parameter::SetInterface(int row, bool val)
		{
			visitor_->SetElementValue(row, 20, val);
		}

		bool Global_Esca_Setting_Parameter::GetBalustradeShader(int row)
		{
			return visitor_->GetElementBool(row, 21);
		}

		void Global_Esca_Setting_Parameter::SetBalustradeShader(int row, bool val)
		{
			visitor_->SetElementValue(row, 21, val);
		}

		float Global_Esca_Setting_Parameter::GetDeckExtend(int row)
		{
			return visitor_->GetElementFloat(row, 22);
		}

		void Global_Esca_Setting_Parameter::SetDeckExtend(int row, float val)
		{
			visitor_->SetElementValue(row, 22, val);
		}

		int Global_Esca_Setting_Parameter::GetStepWidth(int row)
		{
			return visitor_->GetElementInt(row, 23);
		}

		void Global_Esca_Setting_Parameter::SetStepWidth(int row, int val)
		{
			visitor_->SetElementValue(row, 23, val);
		}

		int Global_Esca_Setting_Parameter::GetVertHeight(int row)
		{
			return visitor_->GetElementInt(row, 24);
		}

		void Global_Esca_Setting_Parameter::SetVertHeight(int row, int val)
		{
			visitor_->SetElementValue(row, 24, val);
		}

		int Global_Esca_Setting_Parameter::GetFixedViewType(int row)
		{
			return visitor_->GetElementInt(row, 25);
		}

		void Global_Esca_Setting_Parameter::SetFixedViewType(int row, int val)
		{
			visitor_->SetElementValue(row, 25, val);
		}

	}

}