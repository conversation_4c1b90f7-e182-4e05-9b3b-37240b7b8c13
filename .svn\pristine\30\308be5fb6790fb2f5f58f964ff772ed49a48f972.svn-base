#include "stdafx.h"
#include "Global_TestNote.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Global_TestNote::GetNumber(int row)
		{
			return visitor_->GetElementInt(row, 0);
		}

		void Global_TestNote::SetNumber(int row, int val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		rse::string Global_TestNote::GetName(int row)
		{
			const char* val = visitor_->GetElementString(row, 1);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_TestNote::SetName(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

	}


}