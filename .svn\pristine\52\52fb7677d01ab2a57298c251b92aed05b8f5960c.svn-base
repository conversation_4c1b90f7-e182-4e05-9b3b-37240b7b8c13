﻿//
//  Global_Setting_Car.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Global_Setting_Car : public DGBaseVisitor
{
public:
	Global_Setting_Car(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}

	float GetCar_Pos_X(int row);
	void SetCar_Pos_X(int row, float val);

	float GetCar_Pos_Y(int row);
	void SetCar_Pos_Y(int row, float val);

	float GetCar_Pos_Z(int row);
	void SetCar_Pos_Z(int row, float val);

	float GetCar_Size_X(int row);
	void SetCar_Size_X(int row, float val);

	float GetCar_Size_Y(int row);
	void SetCar_Size_Y(int row, float val);

	float GetCar_Size_Z(int row);
	void SetCar_Size_Z(int row, float val);

	float GetDoor_Size_X(int row);
	void SetDoor_Size_X(int row, float val);

	float GetDoor_Size_Y(int row);
	void SetDoor_Size_Y(int row, float val);

	float GetDoor_Size_Z(int row);
	void SetDoor_Size_Z(int row, float val);

	float GetCeiling_Ply(int row);
	void SetCeiling_Ply(int row, float val);

	float GetDoor_Offset_X(int row);
	void SetDoor_Offset_X(int row, float val);

	float GetDoorThick(int row);
	void SetDoorThick(int row, float val);

	float GetDoorBiasDistance(int row);
	void SetDoorBiasDistance(int row, float val);
};
}