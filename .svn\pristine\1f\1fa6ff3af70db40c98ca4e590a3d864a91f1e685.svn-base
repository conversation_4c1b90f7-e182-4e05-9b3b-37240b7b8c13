//
//  Global_Setting_Hall.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Global_Setting_Hall.h"


namespace decoration_vr_interface
{

int Global_Setting_Hall::Getcar_Number(int row)
{
    return visitor_->GetElementInt(row, 0);
}

void Global_Setting_Hall::Setcar_Number(int row, int val)
{
    visitor_->SetElementValue(row, 0, val);
}

float Global_Setting_Hall::Getcar_Size_X(int row)
{
    return visitor_->GetElementFloat(row, 1);
}

void Global_Setting_Hall::Setcar_Size_X(int row, float val)
{
    visitor_->SetElementValue(row, 1, val);
}

float Global_Setting_Hall::Getcar_Size_Z(int row)
{
    return visitor_->GetElementFloat(row, 2);
}

void Global_Setting_Hall::Setcar_Size_Z(int row, float val)
{
    visitor_->SetElementValue(row, 2, val);
}

int Global_Setting_Hall::Getdoor_Type(int row)
{
    return visitor_->GetElementInt(row, 3);
}

void Global_Setting_Hall::Setdoor_Type(int row, int val)
{
    visitor_->SetElementValue(row, 3, val);
}

float Global_Setting_Hall::Getdistance(int row)
{
    return visitor_->GetElementFloat(row, 4);
}

void Global_Setting_Hall::Setdistance(int row, float val)
{
    visitor_->SetElementValue(row, 4, val);
}

float Global_Setting_Hall::Gethalldoor_SIze_X(int row)
{
    return visitor_->GetElementFloat(row, 5);
}

void Global_Setting_Hall::Sethalldoor_SIze_X(int row, float val)
{
    visitor_->SetElementValue(row, 5, val);
}

float Global_Setting_Hall::Gethalldoor_SIze_Y(int row)
{
    return visitor_->GetElementFloat(row, 6);
}

void Global_Setting_Hall::Sethalldoor_SIze_Y(int row, float val)
{
    visitor_->SetElementValue(row, 6, val);
}

}