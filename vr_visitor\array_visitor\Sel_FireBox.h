//
//  Sel_HL.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_FireBox : public DGBaseVisitor
	{
	public:
		Sel_FireBox(IDGSceneEx* scene, const tchar* arr_name)
			:DGBaseVisitor(scene, arr_name) {}
		bool GetIsHave(int row);
		void SetIsHave(int row, bool val);

		rse::string GetPath(int row);
		void SetPath(int row, const tchar* val);

		int GetLocation(int row);
		void SetLocation(int row, int val);

	};
}