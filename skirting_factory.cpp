#include "stdafx.h"
#include "skirting_factory.h"
#include "skirting_part.h"

namespace decoration_vr_interface
{
	
	SkirtingPartFactory::SkirtingPartFactory(int part_type)
		:part_type_(part_type)
	{

	}

	SkirtingPartFactory::~SkirtingPartFactory()
	{

	}

	void SkirtingPartFactory::SetPartType(int ty)
	{
		part_type_ = ty;
	}

	int SkirtingPartFactory::GetPartType()
	{
		return part_type_;
	}

	decoration_vr_interface::IElevatorPartPtr SkirtingPartFactory::CreatePart(FactoryArgs* param)
	{
		SkirtingPartPtr part = RSE_MAKE_SHARED<SkirtingPart>();
		if (param != nullptr)
		{
			switch (param->createType)
			{
			case PCT_CREATE_FROM_DB:
			{
				FactoryArgsOfLoadingFromDb* args = static_cast<FactoryArgsOfLoadingFromDb*>(param);
				part->part_type_ = part_type_;
				part->part_id_ = args->part_id_;
				part->CollectSkirtingMaterial();
			}
			break;

			case PCT_CREATE_FROM_CONFIG_LOADING:
			{
				FactoryArgsOfConfigLoading* args = static_cast<FactoryArgsOfConfigLoading*>(param);

				auto iter = std::find_if(args->items_.begin(), args->items_.end(), [this](const ConfigDataItemPtr& item) {
					return item->part_type_ == this->part_type_;
				});
				if (iter != args->items_.end())
				{
					part->part_type_ = part_type_;
					part->part_id_ = iter->get()->part_id_;
					part->SetSkirtingMaterial(iter->get()->part_mat_id_);
				}
				break;
			}
			}
		}
		else
		{
			part->CollectSkirtingMaterial();
		}

		return part;
	}

}

