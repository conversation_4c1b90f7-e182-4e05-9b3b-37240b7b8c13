#ifndef _VR_EscaSideCladdingLightingVisitor_H_
#define _VR_EscaSideCladdingLightingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Side_Cladding_Lighting.h"

namespace decoration_vr_interface
{
	class VrEscaSideCladdingLightingVisitor : public BaseVisitor
	{
	public:
		VrEscaSideCladdingLightingVisitor();
		virtual ~VrEscaSideCladdingLightingVisitor();

		DEFINE_CREATE_FUN(VrEscaSideCladdingLightingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Side_Cladding_Lighting> model_;
	};
}

#endif
