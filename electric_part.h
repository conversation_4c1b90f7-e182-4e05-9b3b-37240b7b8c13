#ifndef _ELECTRIC_PART_H_
#define _ELECTRIC_PART_H_

#include "IElevatorPart.h"

namespace decoration_vr_interface
{
	class ElectricPart : public IElevatorPart
	{
		friend class ElectricPartFactory;
	public:
		ElectricPart();
		virtual ~ElectricPart();

		virtual int GetPartType() override;
		virtual int64 GetPartId() override;

		virtual tstring GetPartName() override;
		virtual void SetPartName(const tstring &new_name) override;

		tstring GetButtonPath();
		void SetButton(int64 button_id) { button_ = button_id; }
		int64 GetButton() { return button_; }
		int GetButtonType() { return button_type_; }

		tstring GetLcdPath();
		void SetLcd(int64 lcd_id) { lcd_ = lcd_id; }
		int64 GetLcd() { return lcd_; }
		int GetLcdType() { return lcd_type_; }

		int GetSetupOrientation() { return setup_orientation_; }
		void SetSetupOrientation(int val) { setup_orientation_ = val; }

		float GetPosX() { return pos_x_; }
		void SetPosX(float x) {  pos_x_ = x; }
		float GetPosY() { return pos_y_; }
		void SetPosY(float y) {  pos_y_ = y; }

		int GetPanelType() { return panel_type_; }
		void SetPanelType(int val) { panel_type_ = val; }

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		int64 GetPartMaterial();
		void SetPartMaterial(int64 mat);

		int64 GetEditPartMaterial(int editpart_id);
		bool SetEditPartMaterial(int editpart_id, int64 mat);

		rse::vector<int>* GetEditPartsPtr() { return &editparts_; }

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

	protected:
		int part_type_;

		int64 part_id_;

		tstring part_name_;

		rse::vector<int> editparts_;
		rse::map<int, int64> editpart_materials_;

		int lcd_type_;
		int64 lcd_;

		int button_type_;
		int64 button_;

		int panel_type_;

		int setup_orientation_;

		float pos_x_;
		float pos_y_;

		bool is_reflect_;
	};

	typedef std::shared_ptr<ElectricPart> ElectricPartPtr;
}

#endif//_ELECTRIC_PART_H_

