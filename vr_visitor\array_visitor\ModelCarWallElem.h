//
//  ModelCarWallElem.h
//  VrVisitor
//
//  Created by vrprg on 17:21:13.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class ModelCarWallElem : public DGBaseVisitor
{
public:
	ModelCarWallElem(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	float GetPos_X(int row);
	void SetPos_X(int row, float val);

	float GetPos_Y(int row);
	void SetPos_Y(int row, float val);

	float GetSize_X(int row);
	void SetSize_X(int row, float val);

	float GetSize_Y(int row);
	void SetSize_Y(int row, float val);

	float GetPos_Z(int row);
	void SetPos_Z(int row, float val);

	float GetSize_Z_L(int row);
	void SetSize_Z_L(int row, float val);

	float GetSize_Z_R(int row);
	void SetSize_Z_R(int row, float val);

	float GetSize_Z_T(int row);
	void SetSize_Z_T(int row, float val);

	float GetSize_Z_B(int row);
	void SetSize_Z_B(int row, float val);

	int GetEdge(int row);
	void SetEdge(int row, int val);

	float GetCorner_Radius_X(int row);
	void SetCorner_Radius_X(int row, float val);

	float GetCorner_Radius_Y(int row);
	void SetCorner_Radius_Y(int row, float val);

	float GetCorner_Radius_Z(int row);
	void SetCorner_Radius_Z(int row, float val);

	int GetCorner_Type(int row);
	void SetCorner_Type(int row, int val);

	float GetGap(int row);
	void SetGap(int row, float val);

	int GetMarker(int row);
	void SetMarker(int row, int val);

	int GetLM_Number(int row);
	void SetLM_Number(int row, int val);

};
}