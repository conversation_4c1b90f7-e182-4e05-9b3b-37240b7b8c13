//
//  Global_Select.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Global_Loading_Scence : public DGBaseVisitor
{
public:
	Global_Loading_Scence(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}

	bool GetSelected(int row);
	void SetSelected(int row, bool val);

	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	int GetScenePartType(int row);
	void SetScenePartType(int row, int val);

	rse::string GetScenePath(int row);
	void SetScenePath(int row, const tchar* val);
};
}