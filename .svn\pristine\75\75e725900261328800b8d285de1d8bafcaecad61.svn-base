﻿#pragma once

#include "i_decoration_vr_interface.h"
#include "i_decoration_vr_interface_extend.h"
#include "vr_controller.h"
#include "arg_cache.h"

namespace decoration_vr_interface
{

	class DecorationVrInterface :public IDecorationVrInterface, public IDecorationVrInterfaceExtend
	{
	public:
		DecorationVrInterface();
		~DecorationVrInterface();

		virtual bool IsInitialize() override;

		virtual void InitializeElevator(void* hwnd, int elev_size, int width, int depth, int door_width, int door_height, int flag) override;
		virtual void InitializeElevator(void* hwnd, int flag) override;
		virtual void InitializeElevator(void* hwnd, const ElevatorSpecification* elev_spec, int flag) override;
		virtual void SetDir(const char* app_dir, const char* home_dir) override;
		virtual void InitializeElevator(void* hwnd, void* am, const ElevatorSpecification* elev_spec, int flag) override;
		virtual void InitializeEscalator(void* hwnd, int width, int height, int flag) override;
		virtual void Finalize() override;

		virtual bool InitEngine() override;
		virtual void SetResourcesPath(const char* path) override;
		virtual bool CreateRender(void* hwnd, int w, int h, int device_type) override;
		virtual void LoadScene() override;
		virtual void InitElevator(const ElevatorSpecification* elev_spec) override;
		virtual void UnloadScene() override;
		virtual void FinalizeEngine() override;
		virtual bool DownloadFileCompleted(const char* file) override;
		
		virtual void ChangeElevatorSpecification(const ElevatorSpecification* elev_spec) override;
		virtual void ChangeEscalatorSpecification(int size_id, int width, int height) override;


		virtual void SetPart(int part_type, int64 part_id, bool notify) override;
		virtual void SetMaterial(int part_type, int64 material_id, bool notify) override;
		virtual void Resize(float width, float height) override;
		virtual void NotifyVr() override;

		virtual void SetRemoveSkirting(bool val) override;
		virtual bool IsRemoveSkirting() override;

		virtual void SetMirrorSetupInfo(int orient, float x, float y, bool notify) override;

		virtual void ClearOptionalPart(int type, bool notify) override;

        virtual void GoToCar() override;
        virtual void GoToHall() override;
        
		virtual void DoVrProcess() override;
        virtual int RenderFrameC() override;
        virtual bool InitializeForRenderFrameC() override;
        virtual void SendVrMessage(const char* msg, int delay_frame) override;
		virtual void SendVrMessage(const char* msg, MsgParaValue *arr, int arr_length, int delay_frame) override;

        virtual bool DoChange(int part_type, bool need_notify) override;

		//virtual void SetSkipDoChange(bool val) override;
		//virtual bool IsSkipDoChange() override;

		virtual void AutoSetFCopSetupPos(bool val) override;
		virtual bool IsAutoSetFCopSetupPos() override;

		virtual void SetHandrailPos(int pos, bool notify) override;
		virtual void SetIsHasAuxCop(bool has, bool notify)/* override*/;
		virtual void SetIsHasHdCop(bool has, bool notify)/* override*/;
		virtual void SetIsHasAuxHdCop(bool has, bool notify)/* override*/;
		virtual void SetCopButtonCount(int btn_count, bool notify) override;
		virtual bool SetSubitemMaterial(int part_type, int part_mark, int64 material_id, bool notify) override;

		virtual void SetElectricPartOrientation(int part_type, int orient, bool notify) override;
		virtual void SetElectricPartPos(int part_type, float x, float y, bool notify) override;
		virtual void SetDoorOpenType(int door_type, int elevator_id, bool notify) override;

		//virtual bool UpdateConstructByElemCount(int part_type, int unit_count) override;

		//set handrail pos mark
		virtual void SetHandrailPosMark(int ori, int mark) override;
		//////////////////////////////////////////////////////////////////////////

		virtual void SetSceneFilePath(const char* scene_path) override;
		//virtual void SetResousePath(const char* res_path) override;
		//virtual void SetDataBasePath(const char* db_path) override;
        
		virtual void SetDecorationVrCallBack(IDecorationVrCallBack* callback) override;

		virtual void GestureDetector(int gesture_type, int gesture_state, float x, float y) override;

		virtual void OnTouch(int finger_id, int x, int y) override;

		//virtual int GetStereoMode() override;
		//virtual void SetStereoMode(int val) override;

		//virtual float GetEyeDistance() override;
		//virtual void SetEyeDistance(float val) override;

		//virtual float GetFocusLength() override;
		//virtual void SetFocusLength(float val) override;

		//virtual void SetDefaultCameraAspectRatio(float ratio) override;
		//virtual void ChangeCameraFace(int pos) override;
		//virtual bool CreateSlaveRenderWindow(void* parentWnd) override;
		//virtual bool CreateSlaveRenderWindow() override;
		//virtual bool DestroySlaveRenderWindow() override;
		
		virtual void SetSnapPos(int pos) override;


		virtual void ResizeRender(int w, int h) override;

        //virtual bool EnterMojing() override;
        //virtual void ExitMojing() override;

		virtual void RenderLargeImage(const char* path, float width, float height) override;
		virtual void RenderPanoramaImages(const char*  path) override;
		virtual void RenderPanoramaImages(const char* path, bool is_plat) override;
		virtual void RenderLargeImageWithFixedViewPos(int width, int height, int fixed_view_pos) override;
		virtual void Print(const char* path) override;

		////判断指定类型和ID的部件是否存在
		//virtual bool IsExistedOfPartId(int part_type, int64 part_id) override;

		virtual IDecorationVrArrayVisitor* GetSceneArrayVisitor(const char* name) override;

		virtual void ReleaseBuffer(const void* buf) override;

		//根据gooodsid设置part，类似于SetPart。 SetMaterial????
		virtual void SetPartByGoods(int part_type, int64 goods_id, bool notify) override;
		virtual void SetMaterialByGoods(int part_type, int64 goods_id, bool notify) override;
		virtual void SetDownloadServerURL(const char *url, const char *down_file_root) override;
		virtual void SetLoginInfo(int64 nLibraryId, int nLangId, int64 nUserId) override;

		//根据eds String设置Part
		virtual void SetContentString(const char* content) override;
		virtual void SetIsStandalongVersion(bool val) override;
		/////////////////////Get Visitor
		virtual int64 GetCurrentPartId(int part_type) override;
		virtual const char* GetCurrentPartName(int part_type) override;
		virtual int64 GetCurrentMaterialId(int part_type) override;
		virtual int GetHandrailPos() override;
		virtual int GetCopButtonCount() override;
		virtual int64 GetSubitemMaterial(int part_type, int part_mark) override;
		virtual int GetElectricPartOrientation(int part_type) override;
		virtual float GetElectricPartPosX(int part_type) override;
		virtual float GetElectricPartPosY(int part_type) override;
		virtual int GetElevatorSizeId() override;
		virtual ElevatorSpecification* GetCurrentElevatorSpecification() override;
		virtual int GetCarHeight() override;
		virtual int GetWallElemCount(int part_type) override;

		virtual void OnExternalDeviceLost() override;
		virtual void OnExternalDeviceReCreate(void* hwnd, int w, int h) override;
		virtual void SetPlatformOperation(void* operation) override;
		virtual void OnPlatformAsyncCallBack(int function_code, int wparam, int lparam, const char *extra_data) override;
        virtual void* GetPluginInterfaceObject(const char *guid_text) override;
		virtual void WaitRenderThreadQuited() override;
		virtual void SignalRenderThreadQuited() override;

		virtual bool SendVrMessageWithParas(const char *msg, const char *param_json_text, int json_text_len, int delay_frame) override;
		/*virtual void ExitRenderThread() override;

		virtual void SetLogMark(int mark) override;
		virtual void PrintLogByMark(int mark, const char *fold_path) override;
		virtual void SetValidCounterRang(bool isEnable, int min, int max) override;
		virtual void StopLogCallStack() override;*/

		//2021.01.13
		//获取cop, auxcop, hcop型式
		//一体式：100
		//外挂式：200
		//嵌入式：500
		virtual int GetCopPanelType(int part_type) override;

		virtual void RenderSmallImage(const char* path, float width, float height) override;

	public: //IDecorationVrInterfaceExtend interface
		//virtual void SetMaterialWithTexture(int part_type, int64 material_id, int channel_id, const char* tex_path, bool notify) override;
		IDecorationVrCallBack *GetVrCallBack();
		IDGContext *GetDGContext();
	protected:
		void SendErrorMessage(int segment, int err_code, const char* err_msg);
		void InitializeElevator(const ElevatorSpecification* elev_spec);
		ElevatorSpecification GenerateDefaultSpecification(int width, int depth);

		void VrControllerInitialize(void* hwnd, int flag, void* am=nullptr);

		void InitializeEscalator(int width, int depth);

		void SetElectricChildPart(int part_type, int64 part_id, bool notify);

		void SetElevatorPart(int part_type, IElevatorPartPtr part);

		void* hwnd_;
		VrController vr_controller_;

		bool is_init_;
		//当SkipDoChange 为true时， 忽略对DoChange的调用，
		//增加次函数的目的是 去除图形工作站参数解析过程中多余的vr写入动作
		bool is_skip_do_change_;
		bool is_auto_set_fcop_pos_;
		bool is_remove_skirting_;

		arg_cache arg_cache_;

		rse::vector<std::shared_ptr<IDecorationVrArrayVisitor>> array_visitors_;
	};
}
