#ifndef _VR_EscaSkirtVisitor_H_
#define _VR_EscaSkirtVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Skirt.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaSkirtVisitor : public BaseVisitor
	{
	public:
		VrEscaSkirtVisitor();
		virtual ~VrEscaSkirtVisitor();

		DEFINE_CREATE_FUN(VrEscaSkirtVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Skirt> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
