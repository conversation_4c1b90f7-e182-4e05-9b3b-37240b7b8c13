#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Global_Loading_Scence;

	class VrSightseeingVisitor : public BaseVisitor
	{
	public:
		VrSightseeingVisitor();
		~VrSightseeingVisitor();
		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

		DEFINE_CREATE_FUN(VrSightseeingVisitor);
	protected:
		std::shared_ptr<Global_Loading_Scence> model_;
	};
}