#include "stdafx.h"
#include "decoration_vr_array_visitor.h"

namespace decoration_vr_interface
{

	DecorationVrArrayVisitor::DecorationVrArrayVisitor(IDGArrayVisitor* visitor, const rse::string& name)
		:name_(name), visitor_(visitor)
	{
		
	}

	const char* DecorationVrArrayVisitor::GetName()
	{
		return name_.c_str();
	}

	int DecorationVrArrayVisitor::GetElementInt(int row, int col)
	{
		return visitor_->GetElementInt(row, col);
	}

	void DecorationVrArrayVisitor::SetElementValue(int row, int col, int val)
	{
		visitor_->SetElementValue(row, col, val);
	}

	void DecorationVrArrayVisitor::SetElementValue(int row, int col, bool val)
	{
		visitor_->SetElementValue(row, col, val);
	}

	void DecorationVrArrayVisitor::SetElementValue(int row, int col, float val)
	{
		visitor_->SetElementValue(row, col, val);
	}

	void DecorationVrArrayVisitor::SetElementValue(int row, int col, const char* val)
	{
		visitor_->SetElementValue(row, col, val);
	}

	const char* DecorationVrArrayVisitor::GetElementCompound(int row, int col, int nCompoundTypeId)
	{
		return visitor_->GetElementCompound(row, col, nCompoundTypeId);
	}

	void DecorationVrArrayVisitor::SetElementCompound(int row, int col, int nCompoundTypeId, const char* val)
	{
		visitor_->SetElementCompound(row, col, nCompoundTypeId, val);
	}

	int DecorationVrArrayVisitor::GetRowCount()
	{
		return visitor_->GetRowCount();
	}

	int DecorationVrArrayVisitor::GetColumnCount()
	{
		return visitor_->GetColumnCount();
	}

	int DecorationVrArrayVisitor::AddRow()
	{
		return visitor_->AddRow();
	}

	void DecorationVrArrayVisitor::DeleteRow(int row)
	{
		visitor_->DeleteRow(row);
	}

	void DecorationVrArrayVisitor::Clear()
	{
		visitor_->Clear();
	}

	void DecorationVrArrayVisitor::PrintData(const char* strFileName)
	{
		visitor_->PrintData(strFileName);
	}

	void* DecorationVrArrayVisitor::GetElementObject(int row, int col)
	{
		return visitor_->GetElementObject(row, col);
	}

	void DecorationVrArrayVisitor::SetElementObject(int row, int col, void* obj)
	{
		visitor_->SetElementObject(row, col, obj);
	}

	const char* DecorationVrArrayVisitor::GetElementString(int row, int col)
	{
		return visitor_->GetElementString(row, col);
	}

	float DecorationVrArrayVisitor::GetElementFloat(int row, int col)
	{
		return visitor_->GetElementFloat(row, col);
	}

	bool DecorationVrArrayVisitor::GetElementBool(int row, int col)
	{
		return visitor_->GetElementBool(row, col);
	}

}