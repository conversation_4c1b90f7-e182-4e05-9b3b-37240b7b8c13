#include "stdafx.h"
#include "Sel_Esca_Access_Cover.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		rse::string Sel_Esca_Access_Cover::Getstr_Path(int row)
		{
			const char* val = visitor_->GetElementString(row, 0);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Access_Cover::Setstr_Path(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Sel_Esca_Access_Cover::Getint_DownStates(int row)
		{
			return visitor_->GetElementInt(row, 1);
		}

		void Sel_Esca_Access_Cover::Setint_DownStates(int row, int val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		int Sel_Esca_Access_Cover::Getint_UpStates(int row)
		{
			return visitor_->GetElementInt(row, 2);
		}

		void Sel_Esca_Access_Cover::Setint_UpStates(int row, int val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

	}

}