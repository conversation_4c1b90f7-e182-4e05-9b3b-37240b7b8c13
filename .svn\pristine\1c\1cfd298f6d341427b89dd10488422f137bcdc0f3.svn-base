﻿#include "stdafx.h"
#include "common_part.h"
#include "GlobalInfoDataCommon.h"
#include "VrGlobalInfo.h"
#include "file_pool.h"

namespace decoration_vr_interface
{
	CommonPart::CommonPart()
		:part_type_(-1), part_id_(-1), part_name_(TSTR("")), is_reflect_(false)
	{
	}


	CommonPart::~CommonPart()
	{
	}

	int CommonPart::GetPartType()
	{
		return part_type_;
	}

	int64 CommonPart::GetPartId()
	{
		return part_id_;
	}

	tstring CommonPart::GetPartName()
	{
		return part_name_;
	}

	void CommonPart::SetPartName(const tstring &new_name)
	{
		if (part_name_ != new_name)
		{
			part_name_ = new_name;
		}
	}

	tstring CommonPart::LoadModel()
	{
		auto es = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize();
		int size_id = es->id_;
		int w = es->door_width_;
		int h = es->door_height_;
		auto type = GetPartType();
		if (type == PartTypeImportedCarDoor || type == PartTypeImportedHallDoor)
		{
			int door_type = VrGlobalInfo::Instance()->GetDoorType();
			if (door_type == 104)
			{
				door_type = 100;
			}
			else if (door_type == 105) 
			{
				door_type = 201;
			}
			size_id = h + w * 1000 + door_type * 1000000;
		}
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(GetPartType(), GetPartId(), size_id);
	}

	bool CommonPart::IsValidPart()
	{
		if (part_type_ == PartTypeHandrail && (part_id_ <= 0 || GetDecorationVrInterface()->GetHandrailPos() == kHandrailPosNone))
		{//扶手安装位置为0时，我们不关心part_id_的值
			return true;
		}
		else if (part_type_ == PartTypeMirror)
		{//镜子安装位置为0时，我们不关心part_id_的值
			int setup_pos = 0;
			IConfigPtr car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
			if (car_config)
			{
				auto vari = car_config->GetExtendProperty(EPK_MirrorSetupPos);
				if (vari != nullptr)
				{
					setup_pos = vari->i_;
				}
			}
			if (setup_pos == kHandrailPosNone) return true;
		}
		else if (part_type_ == PartTypeImportedHallDoor && part_id_ <= 0)
		{//导入门part_id_<=0,不加载导入门，使用默认门
			return true;
		}
		else if (part_type_ == PartTypeImportedCarDoor && part_id_ <= 0)
		{//导入门part_id_<=0,不加载导入门，使用默认门
			return true;
		}
		else if (part_type_ == PartTypeFrontAccessory && part_id_ <= 0)
		{//导入前壁part_id_<=0,不加载导入挂件，使用默认前壁生成
			return true;
		}
		else if ((part_type_ == PartTypeICCard || part_type_ == PartTypeAuxICCard) && part_id_ <= 0)
		{
			return true;
		}

		auto gd = GlobalInfoDataCommon::Instance();
		bool is_material_part = gd->GetPartTypeManager()->IsMaterialPart(part_type_);

		if (!is_material_part && part_id_ <= 0)
		{
			gd->LogErrorLn("CommonPart isn't valid part: type=%d, part id < 0", part_type_);
			return false;
		}

		for (auto it = editpart_materials_.begin(), ie = editpart_materials_.end(); it != ie; ++it)
		{
			if (it->second <= 0)
			{
				gd->LogErrorLn("CommonPart's material isn't valid part: type=%d, part id=%ld, material id for edit part id=%d less 0",
					part_type_, part_id_, it->first);
				return false;
			}
		}

		return true;
	}

	int64 CommonPart::GetPartMaterial()
	{
		return GetEditPartMaterial(1);
	}

	void CommonPart::SetPartMaterial(int64 mat)
	{
		SetEditPartMaterial(1, mat);
	}

	int64 CommonPart::GetEditPartMaterial(int id)
	{
		auto it = editpart_materials_.find(id);
		if (it != editpart_materials_.end())
		{
			return it->second;
		}

		return -1;
	}

	bool CommonPart::SetEditPartMaterial(int id, int64 mat)
	{
		auto it = editpart_materials_.find(id);
		if (it != editpart_materials_.end())
		{
			it->second = mat;
			return true;
		}
		return false;
	}

	bool CommonPart::GetIsHasReflect()
	{
		return is_reflect_;
	}

	void CommonPart::SetIsHasReflect(const bool &is_reflect)
	{
		is_reflect_ = is_reflect;
	}

	void CommonPart::ReplaceEditMaterials(CommonPart* src)
	{
		for (auto it = src->editpart_materials_.begin(), ie = src->editpart_materials_.end(); it != ie; ++it)
		{
			SetEditPartMaterial(it->first, it->second);
		}
	}

}
