#ifndef _VR_EscaScenesVisitor_H_
#define _VR_EscaScenesVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Scene.h"

namespace decoration_vr_interface
{
	class VrEscaScenesVisitor : public BaseVisitor
	{
	public:
		VrEscaScenesVisitor();
		virtual ~VrEscaScenesVisitor();

		DEFINE_CREATE_FUN(VrEscaScenesVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Scene> model_;
	};
}

#endif
