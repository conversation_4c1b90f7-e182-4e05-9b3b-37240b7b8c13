#pragma once

#include "VrChangeArg.h"
#include "IVrVisitor.h"
#include "i_decoration_vr_callback.h"
#include "dvi_tinyxml2.h"

namespace decoration_vr_interface
{
	class VrController
	{
	public:
		VrController();
		~VrController();

		void Initialize(void* main_hwnd);

		void AddChange(const VrChangeArg& arg);
		int DoChanges();
		void ClearChange();
		void Initialize(void* main_hwnd, void* parent_hwnd);
		void Initialize(void* am, void* main_hwnd, void* parent_hwnd);

		bool InitEngine();
		void SetResourcesPath(const char* path);
		bool CreateRender(void* hwnd, int w, int h, int device_type);
		bool InitVrController();
		void FinalizeVrController();
		void FinalizeEngine();

		void Finalize();
		bool IsLoadVr() { return is_load_vr_; }
		void RegisterVrVisitor(IVrVisitorPtr visitor_ptr);
		void NotifyChangeCameraRatio(float width, float height);
		void SendMessageToVr(const tchar* msg, int delay_frame);
		void SendMessageToVr(const tchar* msg, MsgParaValue *arr, int arr_length, int delay_frame);
		void DoVrProcess();
        int RenderFrameC();
        bool InitializeForRenderFrameC();
        
		void SetVrCallBack(IDecorationVrCallBack* callback) { vr_callback_ = callback; }
		IDecorationVrCallBack* GetVrCallBack() { return vr_callback_; }

		void PrintDataArrayXml(const tstring& path);

		StereoMode GetStereoMode();
		void SetStereoMode(StereoMode val);

		float GetEyeDistance();
		void SetEyeDistance(float val);

		float GetFocusLength();
		void SetFocusLength(float val);

		void SetDefaultCameraAspectRatio(float ratio);
		void ChangeCameraFace(CameraPos pos);

		bool CreateSlaveRenderWindow(void* parentWnd);


		void ResizeRender(int w, int h);

		void RenderLargeImage(const char* path, float width, float height);

		IDGContext * get_dg_context()
		{
			return context_;
		}
		void OnExternalDeviceLost();
		void OnExternalDeviceReCreate(void* hwnd, int w, int h);
		void OnPlatformAsyncCallBack(int function_code, int wparam, int lparam, const char *extra_data);

		void SetPlatformOperation(void* operation);
        void* GetPluginInterfaceObject(const char *guid_text);
		void WaitRenderThreadQuited();
		void SignalRenderThreadQuited();
		IVrVisitor* GetVrVisitor(int part_type);
		/*void ExitRenderThread();

		void SetLogMark(int mark);
		void PrintLogByMark(int mark, const char *fold_path);
		void SetValidCounterRang(bool isEnable, int min, int max);
		void StopLogCallStack();*/

	protected:
		void Initialize();
		void LoadVrVisitors();
		void LoadTogetherBuilder();

		bool LoadVrConfigXml(dvi_tinyxml2::XMLDocument &xml_doc);
		void ReadRenderSetting(IDGContext::RenderDeviceType &dt, rse::string &hmd);

		bool CreateVrContext(void* am = nullptr);
		void LoadScene();
		void InitializeScene();
		bool IsExsitInChangedList(PartTypeId parttype);
		tstring GetRealPath(const tstring& path);
		
		int GetVrVisitorType(int part_type);
#if defined(XIZI_OTIS)
	private:
		rse::map<int64, int> dt_map_;
		void InitDTMap();
	public:
		int GetGlassDoorType(int64 k)
		{
			auto it = dt_map_.find(k);
			if (it != dt_map_.end())
			{
				return it->second;
			}
			return 0;
		}
#endif
	protected:
		rse::vector<VrChangeArg> changes_;
		IDGContext* context_;
		rse::vector<IVrVisitorPtr> visitor_;
		rse::map<PartTypeId, IVrVisitorPtr> visitor_dispatchers_;
		bool is_load_vr_;
		bool need_finalize_;
		rse::map<PartTypeId, rse::vector<PartTypeId>> together_builder_;
		IDecorationVrCallBack* vr_callback_;
	};

}
