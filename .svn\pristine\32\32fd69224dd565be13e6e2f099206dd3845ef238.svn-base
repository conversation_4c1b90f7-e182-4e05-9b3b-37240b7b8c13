//
//  ModelCarWallElem.h
//  VrVisitor
//
//  Created by vrprg on 17:21:13.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "ModelCarWallElem.h"


namespace decoration_vr_interface
{

float ModelCarWallElem::GetPos_X(int row)
{
    return visitor_->GetElementFloat(row, 0);
}

void ModelCarWallElem::SetPos_X(int row, float val)
{
    visitor_->SetElementValue(row, 0, val);
}

float ModelCarWallElem::GetPos_Y(int row)
{
    return visitor_->GetElementFloat(row, 1);
}

void ModelCarWallElem::SetPos_Y(int row, float val)
{
    visitor_->SetElementValue(row, 1, val);
}

float ModelCarWallElem::GetSize_X(int row)
{
    return visitor_->GetElementFloat(row, 2);
}

void ModelCarWallElem::SetSize_X(int row, float val)
{
    visitor_->SetElementValue(row, 2, val);
}

float ModelCarWallElem::GetSize_Y(int row)
{
    return visitor_->GetElementFloat(row, 3);
}

void ModelCarWallElem::SetSize_Y(int row, float val)
{
    visitor_->SetElementValue(row, 3, val);
}

float ModelCarWallElem::GetPos_Z(int row)
{
    return visitor_->GetElementFloat(row, 4);
}

void ModelCarWallElem::SetPos_Z(int row, float val)
{
    visitor_->SetElementValue(row, 4, val);
}

float ModelCarWallElem::GetSize_Z_L(int row)
{
    return visitor_->GetElementFloat(row, 5);
}

void ModelCarWallElem::SetSize_Z_L(int row, float val)
{
    visitor_->SetElementValue(row, 5, val);
}

float ModelCarWallElem::GetSize_Z_R(int row)
{
    return visitor_->GetElementFloat(row, 6);
}

void ModelCarWallElem::SetSize_Z_R(int row, float val)
{
    visitor_->SetElementValue(row, 6, val);
}

float ModelCarWallElem::GetSize_Z_T(int row)
{
    return visitor_->GetElementFloat(row, 7);
}

void ModelCarWallElem::SetSize_Z_T(int row, float val)
{
    visitor_->SetElementValue(row, 7, val);
}

float ModelCarWallElem::GetSize_Z_B(int row)
{
    return visitor_->GetElementFloat(row, 8);
}

void ModelCarWallElem::SetSize_Z_B(int row, float val)
{
    visitor_->SetElementValue(row, 8, val);
}

int ModelCarWallElem::GetEdge(int row)
{
    return visitor_->GetElementInt(row, 9);
}

void ModelCarWallElem::SetEdge(int row, int val)
{
    visitor_->SetElementValue(row, 9, val);
}

float ModelCarWallElem::GetCorner_Radius_X(int row)
{
    return visitor_->GetElementFloat(row, 10);
}

void ModelCarWallElem::SetCorner_Radius_X(int row, float val)
{
    visitor_->SetElementValue(row, 10, val);
}

float ModelCarWallElem::GetCorner_Radius_Y(int row)
{
    return visitor_->GetElementFloat(row, 11);
}

void ModelCarWallElem::SetCorner_Radius_Y(int row, float val)
{
    visitor_->SetElementValue(row, 11, val);
}

float ModelCarWallElem::GetCorner_Radius_Z(int row)
{
    return visitor_->GetElementFloat(row, 12);
}

void ModelCarWallElem::SetCorner_Radius_Z(int row, float val)
{
    visitor_->SetElementValue(row, 12, val);
}

int ModelCarWallElem::GetCorner_Type(int row)
{
    return visitor_->GetElementInt(row, 13);
}

void ModelCarWallElem::SetCorner_Type(int row, int val)
{
    visitor_->SetElementValue(row, 13, val);
}

float ModelCarWallElem::GetGap(int row)
{
    return visitor_->GetElementFloat(row, 14);
}

void ModelCarWallElem::SetGap(int row, float val)
{
    visitor_->SetElementValue(row, 14, val);
}

int ModelCarWallElem::GetMarker(int row)
{
    return visitor_->GetElementInt(row, 15);
}

void ModelCarWallElem::SetMarker(int row, int val)
{
    visitor_->SetElementValue(row, 15, val);
}

int ModelCarWallElem::GetLM_Number(int row)
{
    return visitor_->GetElementInt(row, 16);
}

void ModelCarWallElem::SetLM_Number(int row, int val)
{
    visitor_->SetElementValue(row, 16, val);
}

}