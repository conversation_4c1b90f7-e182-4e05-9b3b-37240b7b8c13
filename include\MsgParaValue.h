// VrMessage.h: interface for the CUserMsgReceiver class.
//
//////////////////////////////////////////////////////////////////////

//#if _MSC_VER > 1000
#ifndef _MSG_PARA_H_
#define _MSG_PARA_H_

enum MsgParaValueType
{
	MPVT_INT32,
	MPVT_UINT32,
	MPVT_FLOAT32,
	MPVT_DOUBLE,
	MPVT_STRING,
};

struct MsgParaValue
{
	tchar *name_;

	MsgParaValueType value_type_;
	union
	{
		uint32_t ui_val_;
		float f_val_;
		int i_val_;
		tchar *str_val_;
		double d_val_;
	};

	MsgParaValue(tchar *n, MsgParaValueType t, const float &f_v) : name_(n), value_type_(t), f_val_(f_v) {}
	MsgParaValue(tchar *n, MsgParaValueType t, const uint32_t &ui_v) : name_(n), value_type_(t), ui_val_(ui_v) {}
	MsgParaValue(tchar *n, MsgParaValueType t, const int &i_v) : name_(n), value_type_(t), i_val_(i_v) {}
	MsgParaValue(tchar *n, MsgParaValueType t, tchar *str_v) : name_(n), value_type_(t), str_val_(str_v) {}
	MsgParaValue(tchar *n, MsgParaValueType t, const double &f_v) : name_(n), value_type_(t), d_val_(f_v) {}
	MsgParaValue() : name_(nullptr), value_type_(MPVT_INT32), i_val_(0) { }
};

#endif
 
//#endif // _MSC_VER > 1000
