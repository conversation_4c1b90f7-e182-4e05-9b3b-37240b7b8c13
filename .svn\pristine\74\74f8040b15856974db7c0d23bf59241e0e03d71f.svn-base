#ifndef _VR_EscaAcessOverFlagVisitor_H_
#define _VR_EscaAcessOverFlagVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Access_Cover_Flag.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	
	class VrEscaAccessOverFlagVisitor : public BaseVisitor
	{
	public:
		VrEscaAccessOverFlagVisitor();
		virtual ~VrEscaAccessOverFlagVisitor();

		DEFINE_CREATE_FUN(VrEscaAccessOverFlagVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<esca::Sel_Esca_Access_Cover_Flag> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif //endif
