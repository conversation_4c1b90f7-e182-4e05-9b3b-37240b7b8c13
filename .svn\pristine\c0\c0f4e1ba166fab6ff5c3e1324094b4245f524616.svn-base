//
//  Sel_Jamb.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Jamb.h"


namespace decoration_vr_interface
{

int Sel_Jamb::GetType(int row)
{
    return visitor_->GetElementInt(row, 0);
}

void Sel_Jamb::SetType(int row, int val)
{
    visitor_->SetElementValue(row, 0, val);
}

rse::string Sel_Jamb::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 1);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Jamb::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 1, val);
}

}