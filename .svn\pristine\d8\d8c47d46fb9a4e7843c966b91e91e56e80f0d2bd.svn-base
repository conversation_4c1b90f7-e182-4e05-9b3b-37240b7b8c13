﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug2013|Win32">
      <Configuration>Debug2013</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug2013|x64">
      <Configuration>Debug2013</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug2022|Win32">
      <Configuration>Debug2022</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug2022|x64">
      <Configuration>Debug2022</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release2013 64|Win32">
      <Configuration>Release2013 64</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release2013 64|x64">
      <Configuration>Release2013 64</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release2013|Win32">
      <Configuration>Release2013</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release2013|x64">
      <Configuration>Release2013</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release2022|Win32">
      <Configuration>Release2022</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release2022|x64">
      <Configuration>Release2022</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2666FEF3-4420-4724-805F-1A4B595E43A2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>DecorationVrInterfaceDll</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\..\..\rse\bin\Debug2013</OutDir>
    <IntDir>$(Configuration)</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\bin\$(ProjectName)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\PicWorkStationWindowsFormsForALi_XiZiOTIS\bin\Release</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\PicWorkStationWindowsFormsForALi_XiZiOTIS\bin\Release</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\rse\bin\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\rse\bin\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\rse\bin\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\rse\bin\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\rse\bin\$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;RENDER_SKETCHER_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>false</SDLCheck>
      <AdditionalIncludeDirectories>D:\workspace\trunk\rse\tools\RseDiscoverGraph\RseDiscoverGraph\Include;.\include;..\Release\include;.\vr_visitor\array_visitor;.\vr_visitor;.\netWork;$(ProjectDir);D:\workspace\trunk\SDSMaintainsClient\DecorationVrInterfaceDll\DecorationVrInterfaceDll\parseJson</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories>E:\workspace\rse\trunk\rse\tools\RseDiscoverGraph\RseDiscoverGraph\Include;.\include;..\Release\include;.\vr_visitor\array_visitor;.\util;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\netWork;$(ProjectDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b32ud.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>F:\hitachi root\CGData\trunk\rse\tools\RseDiscoverGraph\RseDiscoverGraph\Include;..\Release\include;.\include;.\vr_visitor;.\network;.\otherfile;.\parsejson;.\part;.\part_operator;.\vr_visitor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>F:\hitachi root\CGData\trunk\rse\lib;..\X64_DebugLibDll;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>DecorationVrInterface.def</ModuleDefinitionFile>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b32ud.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;RENDER_SKETCHER_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph\RseDiscoverGraph\Include;.\include;..\Release\include;.\vr_visitor\array_visitor;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\netWork;$(ProjectDir)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\X86_RLibDll;..\..\..\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b32u.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\..\rse\lib\$(TargetName)$(TargetExt)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;OPTIMIZATION;RENDER_SKETCHER_WINDOWS;_CRT_SECURE_NO_WARNINGS;XIZI_OTIS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;..\..\..\rse\engine_mt\include;.\esca;.\include;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\util;.\netWork;$(ProjectDir);G:\hitachi root\CGData\trunk\rse\engine_mt\include;G:\hitachi root\CGData\trunk\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;G:\hitachi root\CGData\trunk\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b32u.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)\$(TargetName)$(TargetExt)</OutputFile>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;OPTIMIZATION;RENDER_SKETCHER_WINDOWS;_CRT_SECURE_NO_WARNINGS;XIZI_OTIS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;..\..\..\rse\engine_mt\include;.\esca;.\include;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\util;.\netWork;$(ProjectDir);G:\hitachi root\CGData\trunk\rse\engine_mt\include;G:\hitachi root\CGData\trunk\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;G:\hitachi root\CGData\trunk\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2022b32u.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)\$(TargetName)$(TargetExt)</OutputFile>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;RENDER_SKETCHER_WINDOWS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;..\..\..\rse\engine_mt\include;.\esca;.\include;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\util;.\netWork;$(ProjectDir)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b32u.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)\$(TargetName)$(TargetExt)</OutputFile>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;RENDER_SKETCHER_WINDOWS;_CRT_SECURE_NO_WARNINGS;XIZI_OTIS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;..\..\..\rse\engine_mt\include;.\esca;.\include;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\util;.\netWork;$(ProjectDir)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b32ud.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;RENDER_SKETCHER_WINDOWS;_CRT_SECURE_NO_WARNINGS;XIZI_OTIS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;..\..\..\rse\engine_mt\include;.\esca;.\include;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\util;.\netWork;$(ProjectDir)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>RseDiscoverGraph_vc2022b32ud.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>DecorationVrInterface.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release2013|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>DecorationVrInterface.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release2022|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>DecorationVrInterface.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release2013 64|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;RENDER_SKETCHER_WINDOWS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;..\..\..\rse\engine_mt\include;.\esca;.\include;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\include;.\jsoncpp\src\lib_json;.\util;.\netWork;$(ProjectDir)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalDependencies>RseDiscoverGraph_vc2013b64u.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\rse\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ImportLibrary>..\..\..\rse\lib\$(TargetName).lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug2013|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>DecorationVrInterface.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug2022|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;DECORATIONVRINTERFACEDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>DecorationVrInterface.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="arg_cache.h" />
    <ClInclude Include="BasePartOperator.h" />
    <ClInclude Include="BaseVisitor.h" />
    <ClInclude Include="car_top.h" />
    <ClInclude Include="car_top_factory.h" />
    <ClInclude Include="car_wall_factory.h" />
    <ClInclude Include="car_wall_part.h" />
    <ClInclude Include="common_part_factory.h" />
    <ClInclude Include="common_part.h" />
    <ClInclude Include="config_part.h" />
    <ClInclude Include="ConfigAnalyzer.h" />
    <ClInclude Include="config_factory.h" />
    <ClInclude Include="ConfigOperator.h" />
    <ClInclude Include="ConstPartInnerParams.h" />
    <ClInclude Include="ConstValueMap.h" />
    <ClInclude Include="db_visitor.h" />
    <ClInclude Include="decoration_vr_array_visitor.h" />
    <ClInclude Include="decoration_vr_callback.h" />
    <ClInclude Include="decoration_vr_interface.h" />
    <ClInclude Include="decoration_vr_interface_include.h" />
    <ClInclude Include="download_assist.h" />
    <ClInclude Include="download_visitor.h" />
    <ClInclude Include="ElectricOperator.h" />
    <ClInclude Include="electric_factory.h" />
    <ClInclude Include="electric_part.h" />
    <ClInclude Include="ElevatorConfig.h" />
    <ClInclude Include="ElevatorConfigManager.h" />
    <ClInclude Include="ElevatorPartFactoryManager.h" />
    <ClInclude Include="ElevatorPartOperator.h" />
    <ClInclude Include="ElevatorSize.h" />
    <ClInclude Include="elevator_size_parser.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="esca\EscaAcessOverFactory.h" />
    <ClInclude Include="esca\EscaBalustradeFactory.h" />
    <ClInclude Include="esca\EscaHandrailEnterFactory.h" />
    <ClInclude Include="esca\EscaSideCladdingFactory.h" />
    <ClInclude Include="esca\EscaSkirtLightingFactory.h" />
    <ClInclude Include="esca\esca_elevator_part.h" />
    <ClInclude Include="esca\esca_elevator_part_factory.h" />
    <ClInclude Include="file_pool.h" />
    <ClInclude Include="FrontWallTypeOperator.h" />
    <ClInclude Include="GlobalInfoDataCommon.h" />
    <ClInclude Include="hall_room_part.h" />
    <ClInclude Include="hall_room_part_factory.h" />
    <ClInclude Include="html_decoration_vr_interface.h" />
    <ClInclude Include="IConfig.h" />
    <ClInclude Include="IDbVisitor.h" />
    <ClInclude Include="IDownloadVisitor.h" />
    <ClInclude Include="IElevatorConfig.h" />
    <ClInclude Include="IElevatorPart.h" />
    <ClInclude Include="IElevatorPartFactory.h" />
    <ClInclude Include="include\decoration_vr_interface_lib.h" />
    <ClInclude Include="include\i_decoration_vr_array_visitor.h" />
    <ClInclude Include="include\i_decoration_vr_callback.h" />
    <ClInclude Include="include\i_decoration_vr_interface.h" />
    <ClInclude Include="include\i_decoration_vr_interface_extend.h" />
    <ClInclude Include="include\i_html_decoration_vr_callback.h" />
    <ClInclude Include="include\i_html_decoration_vr_interface.h" />
    <ClInclude Include="include\MsgParaValue.h" />
    <ClInclude Include="include\part_type.h" />
    <ClInclude Include="i_decoration_vr_bind.h" />
    <ClInclude Include="jsoncpp\include\json\allocator.h" />
    <ClInclude Include="jsoncpp\include\json\assertions.h" />
    <ClInclude Include="jsoncpp\include\json\autolink.h" />
    <ClInclude Include="jsoncpp\include\json\config.h" />
    <ClInclude Include="jsoncpp\include\json\features.h" />
    <ClInclude Include="jsoncpp\include\json\forwards.h" />
    <ClInclude Include="jsoncpp\include\json\json.h" />
    <ClInclude Include="jsoncpp\include\json\reader.h" />
    <ClInclude Include="jsoncpp\include\json\value.h" />
    <ClInclude Include="jsoncpp\include\json\version.h" />
    <ClInclude Include="jsoncpp\include\json\writer.h" />
    <ClInclude Include="jsoncpp\src\lib_json\json_tool.h" />
    <ClInclude Include="material_channel_pool.h" />
    <ClInclude Include="netWork\HttpAsyncManage.h" />
    <ClInclude Include="parse_json\BaseFactory.h" />
    <ClInclude Include="parse_json\svr_base_data.h" />
    <ClInclude Include="parse_json\svr_file_digital_info.h" />
    <ClInclude Include="parse_json\svr_json_helper.h" />
    <ClInclude Include="parse_json\svr_material_channel.h" />
    <ClInclude Include="parse_json\svr_material_special_rule.h" />
    <ClInclude Include="parse_json\svr_part_basic_info.h" />
    <ClInclude Include="parse_json\svr_part_material.h" />
    <ClInclude Include="parse_json\svr_part_model_info.h" />
    <ClInclude Include="parse_json\svr_car_wall.h" />
    <ClInclude Include="parse_json\svr_common_part.h" />
    <ClInclude Include="parse_json\svr_config.h" />
    <ClInclude Include="parse_json\svr_material.h" />
    <ClInclude Include="parse_json\svr_wall_element.h" />
    <ClInclude Include="parse_json\svr_wall_size_info.h" />
    <ClInclude Include="PartOperatorManager.h" />
    <ClInclude Include="PartTypeManager.h" />
    <ClInclude Include="skirting_factory.h" />
    <ClInclude Include="skirting_part.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="dvi_tinyxml2.h" />
    <ClInclude Include="Util.h" />
    <ClInclude Include="util\lru_cache.h" />
    <ClInclude Include="util\sha256.h" />
    <ClInclude Include="vr_visitor\array_visitor\DGBaseVisitor.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Camera_EffectParameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Camera_Position.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Control_Flag.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_CopyObject.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Escalators_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Escalators_Rung_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Esca_PartMark.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Esca_Setting_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_PartType_Info.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Script_Floor.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Step0_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_StepLight0_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Step_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_TestNote.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Work0_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Work1_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_ExpendType.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_Flag.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Background.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Balustrade.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb_Lighting.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking_Ctrl_Box.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Enter.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Guid.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Lighting.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Photoelectric.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Scene.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding_Lighting.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Brush.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Lighting.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Step.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Step_Lighting.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Traffic_Light.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Truss_Lighting.h" />
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_RGB_Share.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Hwndmsg.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Loading_Scence.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Other_Datas.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Select.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Car.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Hall.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\material_channel.h" />
    <ClInclude Include="vr_visitor\array_visitor\ModelCarWallElem.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Accessory.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_CarIndicator.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Car_Shell.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Ceiling.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Cop.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_CopLcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Door_Imported.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Emids.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_FireBox.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Hall.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HandRail.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HI.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HILcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HL.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HLLcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Jamb.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Lop.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_LopLcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Mirror.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Shaft.h" />
    <ClInclude Include="vr_visitor\esca\EscaAccessOverVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaAcessOverExpendTypeVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaAcessOverFlagVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaBalustradeVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaCombLightingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaCombVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaConfigVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaDeckingCtrlBoxVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaDeckingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaHandrailEnterVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaHandrailGuidVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaHandrailLightingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaHandrailVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaPhotoelectricVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaScenesVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaSideCladdingLightingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaSideCladdingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaSkirtBrushVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaSkirtLightingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaSkirtVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaStepLightingVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaStepVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaTrafficLightVisitor.h" />
    <ClInclude Include="vr_visitor\esca\EscaTrussLightingVisitor.h" />
    <ClInclude Include="vr_visitor\VrEmidsVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallFireBoxVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallFloorVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallShaftVisitor.h" />
    <ClInclude Include="vr_visitor\IVrVisitor.h" />
    <ClInclude Include="vr_visitor\VrAccessoryVisitor.h" />
    <ClInclude Include="vr_visitor\VrBottomVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarConfigVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarDoorVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarIndicatorVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarShellVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarWallVisitor.h" />
    <ClInclude Include="vr_visitor\VrChangeArg.h" />
    <ClInclude Include="vr_visitor\VrConfigInfo.h" />
    <ClInclude Include="vr_visitor\VrCopDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrCopVisitor.h" />
    <ClInclude Include="vr_visitor\VrFrontWallVisitor.h" />
    <ClInclude Include="vr_visitor\VrGlobalInfo.h" />
    <ClInclude Include="vr_visitor\VrHallConfigVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallDoorVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallIndicatorDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallIndicatorVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallWallVisitor.h" />
    <ClInclude Include="vr_visitor\VrHandrailVisitor.h" />
    <ClInclude Include="vr_visitor\VrJambVisitor.h" />
    <ClInclude Include="vr_visitor\VrLanternDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrLanternVisitor.h" />
    <ClInclude Include="vr_visitor\VrLopDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrLopVisitor.h" />
    <ClInclude Include="vr_visitor\VrMirrorVisitor.h" />
    <ClInclude Include="vr_visitor\VrSightseeingVisitor.h" />
    <ClInclude Include="vr_visitor\VrTopVisitor.h" />
    <ClInclude Include="vr_visitor\vr_controller.h" />
    <ClInclude Include="vr_visitor\vr_visitor_factory.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="arg_cache.cpp" />
    <ClCompile Include="BasePartOperator.cpp" />
    <ClCompile Include="BaseVisitor.cpp" />
    <ClCompile Include="car_top.cpp" />
    <ClCompile Include="car_top_factory.cpp" />
    <ClCompile Include="car_wall_factory.cpp" />
    <ClCompile Include="car_wall_part.cpp" />
    <ClCompile Include="common_part_factory.cpp" />
    <ClCompile Include="common_part.cpp" />
    <ClCompile Include="config_part.cpp" />
    <ClCompile Include="ConfigAnalyzer.cpp" />
    <ClCompile Include="config_factory.cpp" />
    <ClCompile Include="ConfigOperator.cpp" />
    <ClCompile Include="ConstValueMap.cpp" />
    <ClCompile Include="db_visitor.cpp" />
    <ClCompile Include="decoration_vr_array_visitor.cpp" />
    <ClCompile Include="decoration_vr_callback.cpp" />
    <ClCompile Include="decoration_vr_interface.cpp" />
    <ClCompile Include="decoration_vr_interface_lib.cpp" />
    <ClCompile Include="download_assist.cpp" />
    <ClCompile Include="download_visitor.cpp" />
    <ClCompile Include="ElectricOperator.cpp" />
    <ClCompile Include="electric_factory.cpp" />
    <ClCompile Include="electric_part.cpp" />
    <ClCompile Include="ElevatorConfig.cpp" />
    <ClCompile Include="ElevatorConfigManager.cpp" />
    <ClCompile Include="ElevatorPartFactoryManager.cpp" />
    <ClCompile Include="ElevatorPartOperator.cpp" />
    <ClCompile Include="ElevatorSize.cpp" />
    <ClCompile Include="elevator_size_parser.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="esca\EscaAcessOverFactory.cpp" />
    <ClCompile Include="esca\EscaBalustradeFactory.cpp" />
    <ClCompile Include="esca\EscaHandrailEnterFactory.cpp" />
    <ClCompile Include="esca\EscaSideCladdingFactory.cpp" />
    <ClCompile Include="esca\EscaSkirtLightingFactory.cpp" />
    <ClCompile Include="esca\esca_elevator_part.cpp" />
    <ClCompile Include="esca\esca_elevator_part_factory.cpp" />
    <ClCompile Include="file_pool.cpp" />
    <ClCompile Include="FrontWallTypeOperator.cpp" />
    <ClCompile Include="GlobalInfoDataCommon.cpp" />
    <ClCompile Include="hall_room_part.cpp" />
    <ClCompile Include="hall_room_part_factory.cpp" />
    <ClCompile Include="html_decoration_vr_interface.cpp" />
    <ClCompile Include="jsoncpp\src\lib_json\json_reader.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_value.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_writer.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="material_channel_pool.cpp" />
    <ClCompile Include="netWork\HttpAsyncManage.cpp" />
    <ClCompile Include="parse_json\svr_file_digital_info.cpp" />
    <ClCompile Include="parse_json\svr_json_helper.cpp" />
    <ClCompile Include="parse_json\svr_material_channel.cpp" />
    <ClCompile Include="parse_json\svr_material_special_rule.cpp" />
    <ClCompile Include="parse_json\svr_part_basic_info.cpp" />
    <ClCompile Include="parse_json\svr_part_material.cpp" />
    <ClCompile Include="parse_json\svr_part_model_info.cpp" />
    <ClCompile Include="parse_json\svr_car_wall.cpp" />
    <ClCompile Include="parse_json\svr_common_part.cpp" />
    <ClCompile Include="parse_json\svr_config.cpp" />
    <ClCompile Include="parse_json\svr_material.cpp" />
    <ClCompile Include="parse_json\svr_wall_element.cpp" />
    <ClCompile Include="parse_json\svr_wall_size_info.cpp" />
    <ClCompile Include="PartOperatorManager.cpp" />
    <ClCompile Include="PartTypeManager.cpp" />
    <ClCompile Include="skirting_factory.cpp" />
    <ClCompile Include="skirting_part.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="dvi_tinyxml2.cpp" />
    <ClCompile Include="Util.cpp" />
    <ClCompile Include="util\lru_cache.cpp" />
    <ClCompile Include="util\sha256.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\DGBaseVisitor.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Camera_EffectParameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Camera_Position.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Control_Flag.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_CopyObject.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Escalators_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Escalators_Rung_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Esca_PartMark.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Esca_Setting_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_PartType_Info.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Script_Floor.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Step0_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_StepLight0_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Step_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_TestNote.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Work0_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Work1_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_ExpendType.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_Flag.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Background.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Balustrade.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb_Lighting.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking_Ctrl_Box.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Enter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Guid.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Lighting.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Photoelectric.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Scene.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding_Lighting.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Brush.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Lighting.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Step.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Step_Lighting.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Traffic_Light.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Truss_Lighting.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_RGB_Share.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Hwndmsg.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Loading_Scence.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Other_Datas.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Select.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Car.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Hall.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\material_channel.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\ModelCarWallElem.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Accessory.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_CarIndicator.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Car_Shell.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Ceiling.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Cop.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_CopLcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Door_Imported.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Emids.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_FireBox.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Hall.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HandRail.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HI.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HILcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HL.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HLLcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Jamb.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Lop.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_LopLcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Mirror.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Shaft.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaAccessOverVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaAcessOverExpendTypeVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaAcessOverFlagVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaBalustradeVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaCombLightingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaCombVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaConfigVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaDeckingCtrlBoxVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaDeckingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaHandrailEnterVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaHandrailGuidVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaHandrailLightingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaHandrailVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaPhotoelectricVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaScenesVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaSideCladdingLightingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaSideCladdingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaSkirtBrushVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaSkirtLightingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaSkirtVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaStepLightingVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaStepVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaTrafficLightVisitor.cpp" />
    <ClCompile Include="vr_visitor\esca\EscaTrussLightingVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrEmidsVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallFireBoxVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallFloorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallShaftVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrAccessoryVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrBottomVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarConfigVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarDoorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarIndicatorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarShellVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarWallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrChangeArg.cpp" />
    <ClCompile Include="vr_visitor\VrConfigInfo.cpp" />
    <ClCompile Include="vr_visitor\VrCopDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCopVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrFrontWallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrGlobalInfo.cpp" />
    <ClCompile Include="vr_visitor\VrHallConfigVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallDoorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallIndicatorDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallIndicatorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallWallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHandrailVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrJambVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLanternDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLanternVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLopDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLopVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrMirrorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrSightseeingVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrTopVisitor.cpp" />
    <ClCompile Include="vr_visitor\vr_controller.cpp" />
    <ClCompile Include="vr_visitor\vr_visitor_factory.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="jsoncpp\src\lib_json\json_valueiterator.inl" />
    <None Include="jsoncpp\src\lib_json\version.h.in" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>