#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_Camera_Position : public DGBaseVisitor
		{
		public:
			Global_Camera_Position(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			int GetNumber(int row);
			void SetNumber(int row, int val);

			int GetAspectRatio_X(int row);
			void SetAspectRatio_X(int row, int val);

			int GetAspectRatio_Y(int row);
			void SetAspectRatio_Y(int row, int val);

		};
	}
}