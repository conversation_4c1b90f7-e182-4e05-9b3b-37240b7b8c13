#ifndef ELEVATOR_PART_FACTORY_MANAGE_H_1D849D2F_432E_4FB0_93E2_8A41F2743292
#define ELEVATOR_PART_FACTORY_MANAGE_H_1D849D2F_432E_4FB0_93E2_8A41F2743292
#include "IElevatorPartFactory.h"

namespace decoration_vr_interface
{
class ElevatorPartFactoryManager : public IElevatorPartFactoryManager
{
public:
	ElevatorPartFactoryManager();

	void Initialize();

	virtual void RegisterPartManager(IElevatorPartFactoryPtr partFactory) override;
    virtual IElevatorPartFactory* GetPartFactory(int partType) override;

protected:
	IElevatorPartFactoryPtr GeneratePartFactory(int part_type);
	rse::map<int, IElevatorPartFactoryPtr> part_factory_map_;
};
}
#endif