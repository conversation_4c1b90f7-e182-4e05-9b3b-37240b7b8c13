#include "stdafx.h"
#include "svr_common_part.h"
#include "svr_json_helper.h"

using namespace decoration_vr_interface;
namespace svr_data
{
	bool SvrCommonPart::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		if (!basic_info_.Parse(jv["basicInfo"]))
		{
			return false;
		}

		if (!ParseMaterialInfos(jv["materialInfos"]))
		{
			return false;
		}

		if (!ParseModelInfos(jv["modelInfos"]))
		{
			return false;
		}

		if (!ParseFileDigitals(jv["fileDigitals"]))
		{
			return false;
		}

		return true;
	}

	bool SvrCommonPart::ParseMaterialInfos(const Json::Value& jv)
	{
		auto size = jv.size();
		material_infos_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!material_infos_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrCommonPart::ParseModelInfos(const Json::Value& jv)
	{
		auto size = jv.size();
		model_infos_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!model_infos_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrCommonPart::ParseFileDigitals(const Json::Value& jv)
	{
		auto size = jv.size();
		file_digitals_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!file_digitals_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrExhibitCommonPart::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		if (!part_info_.Parse(jv["partInfo"]))
		{
			return false;
		}

		if (!ParseChildParts(jv["childParts"]))
		{
			return false;
		}

		if (!ParseMaterialInfos(jv["materialInfos"]))
		{
			return false;
		}

		if (!ParsePartTypeHasReflect(jv["partTypeHasReflect"]));

		return true;
	}

	bool SvrExhibitCommonPart::ParsePartTypeHasReflect(const Json::Value& jv)
	{
		auto size = jv.size();
		partType_has_reflect_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!GetJsonValue(jv[i], partType_has_reflect_[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrExhibitCommonPart::ParseChildParts(const Json::Value& jv)
	{
		auto size = jv.size();
		child_parts_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!child_parts_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrExhibitCommonPart::ParseMaterialInfos(const Json::Value& jv)
	{
		auto size = jv.size();
		material_infos_.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!material_infos_[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}
}