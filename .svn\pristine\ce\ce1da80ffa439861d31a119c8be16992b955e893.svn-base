//
//  Sel_HILcd.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_HILcd.h"


namespace decoration_vr_interface
{

bool Sel_HILcd::Getm_bool_Select(int row)
{
    return visitor_->GetElementBool(row, 0);
}

void Sel_HILcd::Setm_bool_Select(int row, bool val)
{
    visitor_->SetElementValue(row, 0, val);
}

rse::string Sel_HILcd::Getm_str_Path(int row)
{
    const char* val = visitor_->GetElementString(row, 1);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_HILcd::Setm_str_Path(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 1, val);
}

int Sel_HILcd::Getm_int_Mark(int row)
{
    return visitor_->GetElementInt(row, 2);
}

void Sel_HILcd::Setm_int_Mark(int row, int val)
{
    visitor_->SetElementValue(row, 2, val);
}

}