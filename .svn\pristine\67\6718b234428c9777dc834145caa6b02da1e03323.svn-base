



#include "stdafx.h"
#include "arg_cache.h"

#include "elevator_size_parser.h"
#include "VrGlobalInfo.h"
#include "electric_part.h"

#include <set>
#include <cstdlib>

namespace decoration_vr_interface
{
	arg_cache::arg_cache()
	{
		//2021.08.03 截图参数，默认为0:截取轿厢,4:截取井道 天梭一体化
		arg_type_map_.insert(std::make_pair(TSTR("SHOWTYPE"), ALL));
		//2021.06.29 xizi_otis 单梯和双梯梯形渲染大厅效果图时分别截取1个，2个厅门
		arg_type_map_.insert(std::make_pair(TSTR("MODEL"), ALL));

		//偏心距
		arg_type_map_.insert(std::make_pair(TSTR("BIASDISTANCE"), ALL));
		arg_type_map_.insert(std::make_pair(TSTR("FOV"), ALL));
		//观光梯 0：no， 1：yes
		arg_type_map_.insert(std::make_pair(TSTR("PANORAMIC"), ALL));

		//2021.01.13
		arg_type_map_.insert(std::make_pair(TSTR("FLOORCOUNT"), PartTypeCopFloorCount));
		//2019.12.10
		arg_type_map_.insert(std::make_pair(TSTR("CARSHELLID"), PartTypeCarShell));
		arg_type_map_.insert(std::make_pair(TSTR("CARSHELLMATERIALID"), PartTypeCarShell));
		arg_type_map_.insert(std::make_pair(TSTR("CARSHELLMATERIALID2"), PartTypeCarShell));
		/*arg_type_map_.insert(std::make_pair(TSTR("HALLROOMID"), PartTypeHall));*/
		arg_type_map_.insert(std::make_pair(TSTR("HALLROOMID"), PartTypeHallConfig));
		arg_type_map_.insert(std::make_pair(TSTR("IMPORTEDDOORID"), PartTypeImportedCarDoor));
		arg_type_map_.insert(std::make_pair(TSTR("IMPORTEDDOORMAT01"), PartTypeImportedCarDoor));
		arg_type_map_.insert(std::make_pair(TSTR("IMPORTEDDOORMAT02"), PartTypeImportedCarDoor));
		arg_type_map_.insert(std::make_pair(TSTR("IMPORTEDHALLDOORID"), PartTypeImportedHallDoor));
		arg_type_map_.insert(std::make_pair(TSTR("IMPORTEDHALLDOORMAT01"), PartTypeImportedHallDoor));
		arg_type_map_.insert(std::make_pair(TSTR("IMPORTEDHALLDOORMAT02"), PartTypeImportedHallDoor));
		arg_type_map_.insert(std::make_pair(TSTR("ELEVATORSHAFTID"), PartTypeHallShaft));
		arg_type_map_.insert(std::make_pair(TSTR("ELEVATORSHAFTMAT01"), PartTypeHallShaft));
		arg_type_map_.insert(std::make_pair(TSTR("ELEVATORSHAFTMAT02"), PartTypeHallShaft));
		arg_type_map_.insert(std::make_pair(TSTR("ELEVATORSHAFTFLOORMAT01"), PartTypeHallShaft));
		arg_type_map_.insert(std::make_pair(TSTR("SHAFTWIDTH"), PartTypeHallShaft));
		arg_type_map_.insert(std::make_pair(TSTR("SHAFTDEPTH"), PartTypeHallShaft));


		arg_type_map_.insert(std::make_pair(TSTR("CARID"), PartTypeCarConfig));

		arg_type_map_.insert(std::make_pair(TSTR("TOPID"), PartTypeTop));
		arg_type_map_.insert(std::make_pair(TSTR("TOPMATERIAL01"), PartTypeTop));
		arg_type_map_.insert(std::make_pair(TSTR("TOPMATERIAL02"), PartTypeTop));
		arg_type_map_.insert(std::make_pair(TSTR("TOPMATERIAL03"), PartTypeTop));

		arg_type_map_.insert(std::make_pair(TSTR("HANDRAILID"), PartTypeHandrail));
		arg_type_map_.insert(std::make_pair(TSTR("HANDRAILPOS"), PartTypeHandrail));
		arg_type_map_.insert(std::make_pair(TSTR("HANDRAILLEFTPOSMARK"), PartTypeHandrail));
		arg_type_map_.insert(std::make_pair(TSTR("HANDRAILRIGHTPOSMARK"), PartTypeHandrail));
		arg_type_map_.insert(std::make_pair(TSTR("HANDRAILBACKPOSMARK"), PartTypeHandrail));

		arg_type_map_.insert(std::make_pair(TSTR("COPID"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPMATERIALID"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPBUTTONID"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPDISPLAYID"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPPOS"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPPOSX"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPPOSY"), PartTypeCop));
		arg_type_map_.insert(std::make_pair(TSTR("COPBUTTONMATERIALID"), PartTypeCop));

		//20250421 IC卡
		arg_type_map_.insert(std::make_pair(TSTR("ICCARDID"), PartTypeICCard));
		arg_type_map_.insert(std::make_pair(TSTR("ICCARDPOS"), PartTypeICCard));
		arg_type_map_.insert(std::make_pair(TSTR("ICCARDPOSX"), PartTypeICCard));
		arg_type_map_.insert(std::make_pair(TSTR("ICCARDPOSY"), PartTypeICCard));
		arg_type_map_.insert(std::make_pair(TSTR("ICCARDSETUPPOS"), PartTypeICCard));
		//20250421 IC卡
		arg_type_map_.insert(std::make_pair(TSTR("AUXICCARDID"), PartTypeAuxICCard));
		arg_type_map_.insert(std::make_pair(TSTR("AUXICCARDPOS"), PartTypeAuxICCard));
		arg_type_map_.insert(std::make_pair(TSTR("AUXICCARDPOSX"), PartTypeAuxICCard));
		arg_type_map_.insert(std::make_pair(TSTR("AUXICCARDPOSY"), PartTypeAuxICCard));
		arg_type_map_.insert(std::make_pair(TSTR("AUXICCARDSETUPPOS"), PartTypeAuxICCard));

		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPID"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPMATERIALID"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPBUTTONID"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPDISPLAYID"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPPOS"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPPOSX"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPPOSY"), PartTypeAuxCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXCOPBUTTONMATERIALID"), PartTypeAuxCop));

		arg_type_map_.insert(std::make_pair(TSTR("HDCOPID"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPMATERIALID"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPBUTTONID"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPDISPLAYID"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPPOS"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPPOSX"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPPOSY"), PartTypeHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("HDCOPBUTTONMATERIALID"), PartTypeHDCop));

		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPID"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPMATERIALID"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPBUTTONID"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPDISPLAYID"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPPOS"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPPOSX"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPPOSY"), PartTypeAuxHDCop));
		arg_type_map_.insert(std::make_pair(TSTR("AUXHDCOPBUTTONMATERIALID"), PartTypeAuxHDCop));

		arg_type_map_.insert(std::make_pair(TSTR("DOORHEADERMATERIALID"), PartTypeFrontWall));
		arg_type_map_.insert(std::make_pair(TSTR("FRONTWALLMATERIALID"), PartTypeFrontWall));
		arg_type_map_.insert(std::make_pair(TSTR("FRONTWALLID"), PartTypeFrontAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("FRONTWALLMATERIALID02"), PartTypeFrontAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("COPWALLMATERIALID"), PartTypeFrontWall));
		arg_type_map_.insert(std::make_pair(TSTR("ENTRANCECOLUMNMATERIALID"), PartTypeFrontWall));

		arg_type_map_.insert(std::make_pair(TSTR("FRONTWALLCONSTRUCTIONID"), PartTypeCarConfig));

		arg_type_map_.insert(std::make_pair(TSTR("DOORMATERIALID"), PartTypeCarDoor));
		//开门方式变化，重新生成大厅和轿厢
		arg_type_map_.insert(std::make_pair(TSTR("DOOROPENTYPE"), ALL));
		//贯通门
		arg_type_map_.insert(std::make_pair(TSTR("OPPOSITEDOOR"), ALL));
		//EMIDS
		arg_type_map_.insert(std::make_pair(TSTR("EMIDSID"), PartTypeEMIDS));
		arg_type_map_.insert(std::make_pair(TSTR("EMIDSMATERIALID"), PartTypeEMIDS));
		arg_type_map_.insert(std::make_pair(TSTR("EMIDSMATERIALID2"), PartTypeEMIDS));
		arg_type_map_.insert(std::make_pair(TSTR("EMIDSSETUPPOS"), PartTypeEMIDS));
		arg_type_map_.insert(std::make_pair(TSTR("EMIDSSETUPPOSX"), PartTypeEMIDS));
		arg_type_map_.insert(std::make_pair(TSTR("EMIDSSETUPPOSY"), PartTypeEMIDS));

		arg_type_map_.insert(std::make_pair(TSTR("LEFTSKIRT"), PartTypeLeftWall));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTCOUNT"), PartTypeLeftWall));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTELEM1MATERIALID"), PartTypeLeftWall));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTELEM2MATERIALID"), PartTypeLeftWall));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTELEM3MATERIALID"), PartTypeLeftWall));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTELEM4MATERIALID"), PartTypeLeftWall));

		arg_type_map_.insert(std::make_pair(TSTR("BACKSKIRT"), PartTypeBackWall));
		arg_type_map_.insert(std::make_pair(TSTR("BACKCOUNT"), PartTypeBackWall));
		arg_type_map_.insert(std::make_pair(TSTR("BACKELEM1MATERIALID"), PartTypeBackWall));
		arg_type_map_.insert(std::make_pair(TSTR("BACKELEM2MATERIALID"), PartTypeBackWall));
		arg_type_map_.insert(std::make_pair(TSTR("BACKELEM3MATERIALID"), PartTypeBackWall));
		arg_type_map_.insert(std::make_pair(TSTR("BACKELEM4MATERIALID"), PartTypeBackWall));

		arg_type_map_.insert(std::make_pair(TSTR("RIGHTSKIRT"), PartTypeRightWall));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTCOUNT"), PartTypeRightWall));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTELEM1MATERIALID"), PartTypeRightWall));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTELEM2MATERIALID"), PartTypeRightWall));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTELEM3MATERIALID"), PartTypeRightWall));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTELEM4MATERIALID"), PartTypeRightWall));
		
		arg_type_map_.insert(std::make_pair(TSTR("BOTTOMMATERIALID"), PartTypeBottom));

		arg_type_map_.insert(std::make_pair(TSTR("BOTTOMACCESSORY"), PartTypeBottomAccessory));

		arg_type_map_.insert(std::make_pair(TSTR("HALLDOORMATERIAL"), PartTypeHallDoor));

		arg_type_map_.insert(std::make_pair(TSTR("JAMBID"), PartTypeJamb));
		arg_type_map_.insert(std::make_pair(TSTR("JAMBMATERIAL"), PartTypeJamb));
		arg_type_map_.insert(std::make_pair(TSTR("JAMBMATERIAL02"), PartTypeJamb));

		arg_type_map_.insert(std::make_pair(TSTR("LOPID"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPMATERIALID"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPMATERIALID02"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPBUTTONID"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPDISPLAYID"), PartTypeLopDisplay));
		arg_type_map_.insert(std::make_pair(TSTR("LOPPOS"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPPOSX"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPPOSY"), PartTypeLop));
		arg_type_map_.insert(std::make_pair(TSTR("LOPBUTTONMATERIALID"), PartTypeLop));

		arg_type_map_.insert(std::make_pair(TSTR("HIID"), PartTypeHallIndicator));
		arg_type_map_.insert(std::make_pair(TSTR("HIPOS"), PartTypeHallIndicator));
		arg_type_map_.insert(std::make_pair(TSTR("HIMATERIALID"), PartTypeHallIndicator));
		arg_type_map_.insert(std::make_pair(TSTR("HIMATERIALID02"), PartTypeHallIndicator));
		arg_type_map_.insert(std::make_pair(TSTR("HIDISPLAYID"), PartTypeHallIndicatorDisplay));

		arg_type_map_.insert(std::make_pair(TSTR("HLID"), PartTypeLantern));
		arg_type_map_.insert(std::make_pair(TSTR("HLPOS"), PartTypeLantern));
		arg_type_map_.insert(std::make_pair(TSTR("HLMATERIALID"), PartTypeLantern));
		arg_type_map_.insert(std::make_pair(TSTR("HLMATERIALID02"), PartTypeLantern));
		arg_type_map_.insert(std::make_pair(TSTR("HLDISPLAYID"), PartTypeLanternDisplay));

		//消防盒
		arg_type_map_.insert(std::make_pair(TSTR("FIREBOXID"), PartTypeHallFireBox));
		arg_type_map_.insert(std::make_pair(TSTR("FIREBOXPOS"), PartTypeHallFireBox));
		arg_type_map_.insert(std::make_pair(TSTR("FIREBOXMATERIALID"), PartTypeHallFireBox));
		arg_type_map_.insert(std::make_pair(TSTR("FIREBOXMATERIALID02"), PartTypeHallFireBox));

		arg_type_map_.insert(std::make_pair(TSTR("HALLWALLMATERIALID"), PartTypeHallWall));
		arg_type_map_.insert(std::make_pair(TSTR("HALLBOTTOMMATERIALID"), PartTypeHallBottom));

		arg_type_map_.insert(std::make_pair(TSTR("COPSETUPTYPE"), PartTypeFrontWall));

		arg_type_map_.insert(std::make_pair(TSTR("RETAINSKIRTING"), PartTypeCarConfig));

		arg_type_map_.insert(std::make_pair(TSTR("HALLID"), PartTypeHallConfig));
		arg_type_map_.insert(std::make_pair(TSTR("MIRRORID"), PartTypeMirror));
		arg_type_map_.insert(std::make_pair(TSTR("MIRRORSETUPPOS"), PartTypeMirror));
		arg_type_map_.insert(std::make_pair(TSTR("MIRRORSETUPPOSX"), PartTypeMirror));
		arg_type_map_.insert(std::make_pair(TSTR("MIRRORSETUPPOSY"), PartTypeMirror));

		arg_type_map_.insert(std::make_pair(TSTR("SIGHTSEEINGID"), PartTypeCarConfig));

		arg_type_map_.insert(std::make_pair(TSTR("LEFTWALLACCESSORYID"), PartTypeLeftAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTWALLACCESSORYMAT01"), PartTypeLeftAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTWALLACCESSORYMAT02"), PartTypeLeftAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("LEFTWALLACCESSORYMAT03"), PartTypeLeftAccessory));

		arg_type_map_.insert(std::make_pair(TSTR("RIGHTWALLACCESSORYID"), PartTypeRightAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTWALLACCESSORYMAT01"), PartTypeRightAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTWALLACCESSORYMAT02"), PartTypeRightAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTWALLACCESSORYMAT03"), PartTypeRightAccessory));

		arg_type_map_.insert(std::make_pair(TSTR("BACKWALLACCESSORYID"), PartTypeBackAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("BACKWALLACCESSORYMAT01"), PartTypeBackAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("BACKWALLACCESSORYMAT02"), PartTypeBackAccessory));
		arg_type_map_.insert(std::make_pair(TSTR("BACKWALLACCESSORYMAT03"), PartTypeBackAccessory));

		arg_type_map_.insert(std::make_pair(TSTR("LEFTWALLID"), PartTypeLeftWall));
		arg_type_map_.insert(std::make_pair(TSTR("RIGHTWALLID"), PartTypeRightWall));
		arg_type_map_.insert(std::make_pair(TSTR("BACKWALLID"), PartTypeBackWall));


		/*esca*/
		arg_type_map_.insert(std::make_pair(TSTR("ESCACONFIGID"), PartTypeEscalatorConfig));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASTEPID"), PartTypeEscaStep));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASTEPMAT1"), PartTypeEscaStep));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASTEPLIGHTINGID"), PartTypeEscaStepLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASTEPLIGHTINGMAT1"), PartTypeEscaStepLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILID"), PartTypeEscaHandrail));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILMAT1"), PartTypeEscaHandrail));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILLIGHTINGID"), PartTypeEscaHandrailLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILLIGHTINGMAT1	"), PartTypeEscaHandrailLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILGUIDID"), PartTypeEscaHandrailGuid));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILGUIDMAT1"), PartTypeEscaHandrailGuid));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILENTERID"), PartTypeEscaHandrailEnter));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAHANDRAILENTERMAT1"), PartTypeEscaHandrailEnter));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAACESSOVERID"), PartTypeEscaAcessOver));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAACESSOVERMAT1"), PartTypeEscaAcessOver));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAACESSOVERFLAGID"), PartTypeEscaAcessOverFlag));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAACESSOVERFLAGMAT1"), PartTypeEscaAcessOverFlag));
		arg_type_map_.insert(std::make_pair(TSTR("ESCABALUSTRADEID"), PartTypeEscaBalustrade));
		arg_type_map_.insert(std::make_pair(TSTR("ESCABALUSTRADEMAT1"), PartTypeEscaBalustrade));
		arg_type_map_.insert(std::make_pair(TSTR("ESCACOMBID"), PartTypeEscaComb));
		arg_type_map_.insert(std::make_pair(TSTR("ESCACOMBMAT1"), PartTypeEscaComb));
		arg_type_map_.insert(std::make_pair(TSTR("ESCACOMBLIGHTINGID"), PartTypeEscaCombLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCACOMBLIGHTINGMAT1"), PartTypeEscaCombLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCADECKINGID"), PartTypeEscaDecking));
		arg_type_map_.insert(std::make_pair(TSTR("ESCADECKINGMAT1"), PartTypeEscaDecking));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASKIRTID"), PartTypeEscaSkirt));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASKIRTMAT1"), PartTypeEscaSkirt));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASKIRTLIGHTINGID"), PartTypeEscaSkirtLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASKIRTLIGHTINGMAT1"), PartTypeEscaSkirtLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASKIRTBRUSHID"), PartTypeEscaSkirtBrush));
		arg_type_map_.insert(std::make_pair(TSTR("ESCATRAFFICLIGHTID"), PartTypeEscaTrafficLight));
		arg_type_map_.insert(std::make_pair(TSTR("ESCATRAFFICLIGHTMAT1"), PartTypeEscaTrafficLight));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASIDECLADDINGID"), PartTypeEscaSideCladding));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASIDECLADDINGMAT1"), PartTypeEscaSideCladding));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASIDECLADDINGLIGHTINGID"), PartTypeEscaSideCladdingLighting));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAPHOTOELECTRICID"), PartTypeEscaPhotoelectric));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAPHOTOELECTRICMAT1"), PartTypeEscaPhotoelectric));
		arg_type_map_.insert(std::make_pair(TSTR("ESCAACESSOVEREXPENDTYPEID"), PartTypeEscaAcessOverExpendType));
		arg_type_map_.insert(std::make_pair(TSTR("ESCASCENESID"), PartTypeEscaScenes));
		/*esca*/
	}

	arg_cache::~arg_cache()
	{
	}

	void arg_cache::Parse(const char* content)
	{
#ifdef RENDER_SKETCHER_ANDROID
		LOGI("old content =  %s", content_.c_str());
#endif

		content_ = content;

#ifdef RENDER_SKETCHER_ANDROID
		LOGI("new content =  %s", content_.c_str());
#endif
		auto gd = GlobalInfoDataCommon::Instance();

		change_type_set_.clear();
		category_set_.clear();

		auto mgr = GlobalInfoDataCommon::Instance()->GetPartTypeManager();

		tstring src = Util::StringToTString(content);
		src = Util::Trim(src);

		rse::vector<tstring> dest;
		Util::Split(dest, src, TSTR(","));
		for (int i = 0, sz = dest.size(); i < sz - 1; i += 2)
		{
			auto k = Util::Trim(Util::Trim(dest[i]), '\'');
			Util::ToUpper(k);
			auto v = Util::Trim(Util::Trim(dest[i + 1]), '\'');

			auto old_v = cache_[k];
			cache_[k] = v;
			if (old_v != v)
			{
#ifdef RENDER_SKETCHER_ANDROID
				LOGI("%s old value %s, new value %s", k.c_str(), old_v.c_str(), v.c_str());
#endif
				auto part_type = arg_type_map_[k];
				change_type_set_.insert(part_type);
				auto ty = mgr->GetTypeInfo(part_type);
				if (ty)
				{
					category_set_.insert(ty->category_);
				}
				else if (part_type == PartTypeCopFloorCount)
				{
					category_set_.insert(0);
				}
			}
		}

		DoorSpecialHandle();
	}

	void arg_cache::SetPart(const char* content)
	{
#ifdef RENDER_SKETCHER_HTML
		printf("content = %s\n", content);
		//printf("MessageCop mouse down pos(%f,%f)\n", mouse_pos.x, mouse_pos.y);
#endif
		auto gd = GlobalInfoDataCommon::Instance();
#ifdef RENDER_SKETCHER_ANDROID
		LOGI("Begin parse content string...");
#endif
		Parse(content);

		{//2021-06-29 xizi_otis 单梯和双梯梯形渲染大厅效果图时分别截取1个，2个厅门
			int model = 0;
			auto it = cache_.find(TSTR("MODEL"));
			if (it != cache_.end())
			{
				model = Util::AToI(it->second.c_str());
			}
			VrGlobalInfo::Instance()->get_global_other_datas_array_visitor()->SetElevatorModel(0, model);
		}
		{//2021-08-03 截图参数，默认为0:截取轿厢,4:截取井道 天梭一体化
			int show_type = 0;
			auto it = cache_.find(TSTR("SHOWTYPE"));
			if (it != cache_.end())
			{
				show_type = Util::AToI(it->second.c_str());
			}
			VrGlobalInfo::Instance()->get_setting_parameter_array_visitor()->SetProjectType(0, show_type);
		}
		{//偏心距
			float bias_distance = 0.0f;
			auto it = cache_.find(TSTR("BIASDISTANCE"));
			if (it != cache_.end())
			{
				bias_distance = Util::AToF(it->second.c_str());
			}
			VrGlobalInfo::Instance()->get_car_array_visitor()->SetDoorBiasDistance(0, bias_distance);
		}
		//获取井道宽度、深度相关参数
		{
			int shaft_width = 0;
			auto it = cache_.find(TSTR("SHAFTWIDTH"));
			if (it != cache_.end())
			{
				shaft_width = Util::AToF(it->second.c_str());
				GlobalInfoDataCommon::Instance()->SetShaftWidth(shaft_width);
			}

			int shaft_depth = 0;
			it = cache_.find(TSTR("SHAFTDEPTH"));
			if (it != cache_.end())
			{
				shaft_depth = Util::AToF(it->second.c_str());
				GlobalInfoDataCommon::Instance()->SetShaftDepth(shaft_depth);
			}
		}
		{
			GlobalInfoDataCommon::Instance()->door_type_ = 100;
			auto it = cache_.find("DOOROPENTYPE");
			if (it != cache_.end())
			{
				GlobalInfoDataCommon::Instance()->door_type_ = Util::AToI(it->second.c_str());
			}

			GlobalInfoDataCommon::Instance()->lop_btn_mat_id_ = 0;
			it = cache_.find("LOPBUTTONMATERIALID");
			if (it != cache_.end())
			{
				GlobalInfoDataCommon::Instance()->lop_btn_mat_id_ = Util::AToI(it->second.c_str());
			}

			GlobalInfoDataCommon::Instance()->cop_btn_mat_id_ = 0;
			it = cache_.find("COPBUTTONMATERIALID");
			if (it != cache_.end())
			{
				GlobalInfoDataCommon::Instance()->cop_btn_mat_id_ = Util::AToI(it->second.c_str());
			}
		}

		int part_type = GetChangeType();
#ifdef RENDER_SKETCHER_HTML
		printf("change part type = %d\n", part_type);
#endif
		GlobalInfoDataCommon::Instance()->cur_part_type_ = part_type;
		//2021.01.13
		auto it = cache_.find("FLOORCOUNT");
		if (it != cache_.end())
		{
			int floor_count = Util::AToI(it->second.c_str()); 
			if (floor_count > 0)
			{
				auto config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
				auto config_list = config_mgr->get_elevator_config_list_ptr();
				for (auto it = config_list->begin(), ie = config_list->end(); it != ie; ++it)
				{
					(*it)->GetElevatorSetting()->floor_count_ = floor_count;
				}
			}
		}
		/////////////////////////////////

		{//贯通门
			int opposite = 0;
			auto it = cache_.find(TSTR("OPPOSITEDOOR"));
			if (it != cache_.end())
			{
				opposite = Util::AToI(it->second.c_str());
			}
			GlobalInfoDataCommon::Instance()->opposite_door_ = opposite;
		}

		{//fov
			float fovy = -10.0f;
			auto it = cache_.find(TSTR("FOV"));
			if (it != cache_.end())
			{
				fovy = Util::AToF(it->second.c_str());
			}
			VrGlobalInfo::Instance()->get_setting_parameter_array_visitor()->SetCameraFov(0, fovy);
		}

		{//ICCard 1：上置，-1：下置
			int setup_pos = 1;
			auto it = cache_.find(TSTR("ICCARDSETUPPOS"));
			if (it != cache_.end())
			{
				setup_pos = Util::AToI(it->second.c_str());
			}
			GlobalInfoDataCommon::Instance()->SetGlobalData(PartTypeICCard, setup_pos);

			setup_pos = 1;
			it = cache_.find(TSTR("AUXICCARDSETUPPOS"));
			if (it != cache_.end())
			{
				setup_pos = Util::AToI(it->second.c_str());
			}
			GlobalInfoDataCommon::Instance()->SetGlobalData(PartTypeAuxICCard, setup_pos);
		}

		if (ChangeElevatorSpecification())
		{
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("Need change elevator specification...");
			LOGI("Elevator size changed!");
#endif
			ChangeAll(content);
			return;
		}

		switch (part_type)
		{
		case -1:
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("arg_cache::SetPart no change");
#endif
			GetDecorationVrInterface()->SendVrMessage("msg_build_completed", 1);
			break;
		case ALL:
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("arg_cache::SetPart all change");
#endif
			ChangeAll(content);
			break;
		case PartTypeBottom:
			if (!GlobalInfoDataCommon::Instance()->IsStandalongVersion())
			{
				//只变化轿底
#ifdef RENDER_SKETCHER_ANDROID
				LOGI("arg_cache::SetPart bottom change");
#endif
				tstring k = TSTR("BOTTOMMATERIALID");
				int64 bottom_id = atoll(Util::TStringToString(cache_[k].c_str()).c_str());
				GetDecorationVrInterface()->SetMaterialByGoods(PartTypeBottom, bottom_id, true);
			}
			break;
		case PartTypeCopFloorCount:
			{
#ifdef RENDER_SKETCHER_ANDROID
				LOGI("arg_cache::SetPart cop floor count change");
#endif
				if (!GetDecorationVrInterface()->DoChange(PartTypeCop, true))
				{
					cache_.clear();
					return;
				}
			}
			break;
		case PartTypeFrontWall:
			SetFrontWallMaterial();
			break;
		default:
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("arg_cache::SetPart part change : %d", part_type);
#endif
			auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
			if (!part_operator->SetPartByContent(part_type, content))
			{
				if (cb_ != nullptr)
				{
                    cache_.clear();
                    
#if defined(RENDER_SKETCHER_ANDROID) || defined(RENDER_SKETCHER_APPLE)
					LOGI("create hall by content failed");
#endif
					char ch[10] = {0};
#if defined(RENDER_SKETCHER_APPLE)
                    sprintf(ch, "%d", 10);
#else
					itoa(part_type, ch, 10);
                    
#endif
					std::string str = "create part by content failed, part_type: ";
					str += ch;
					
					cb_(CreateVRPart, -1, str.c_str());

				}
				return;
			}
			if (part_type == PartTypeHandrail || part_type == PartTypeCarConfig)
			{
				SetHandrailPos();
			}
			if (part_type == PartTypeAuxCop || part_type == PartTypeHDCop || part_type == PartTypeAuxHDCop)
			{
				part_type = PartTypeCop;
			}
			if (!GetDecorationVrInterface()->DoChange(part_type, true))
			{
				cache_.clear();
				return;
			}
			break;
		}
	}


	static bool EqualElevatorSpecification(const ElevatorSpecification* e1, const ElevatorSpecification* e2)
	{
		return e1->elevator_size_id_ == e2->elevator_size_id_ &&
			e1->width_ == e2->width_&&
			e1->depth_ == e2->depth_;
	}

	static bool EqualElevatorSpecificationWithHeight(const ElevatorSpecification* e1, const ElevatorSpecification* e2)
	{
		return e1->elevator_size_id_ == e2->elevator_size_id_ &&
			e1->width_ == e2->width_&&
			e1->depth_ == e2->depth_&&
			e1->height_ == e2->height_&&
			e1->door_width_ == e2->door_width_&&
			e1->door_height_ == e2->door_height_;
	}

	bool arg_cache::ChangeElevatorSpecification()
	{
		ElevatorSpecification es;
		if (GlobalInfoDataCommon::Instance()->GetElevatorType() == kConstTypeEscalator)
		{
			bool res = ElevatorSpecificationParser::GetEscalatorElevSpecification(es, cache_);
			if (!res)
			{
				GlobalInfoDataCommon::Instance()->LogErrorLn("GetElevatorSpecification occur error!");
			}
			//
			if (res && !EqualElevatorSpecification(&es, &es_))
			{
				GetDecorationVrInterface()->ChangeEscalatorSpecification(es.elevator_size_id_, es.width_, es.height_);
				es_ = es;
				//return true;
			}
		}
		else
		{
			//判断是否需要切换轿厢规格
			bool res = ElevatorSpecificationParser::GetElevatorSpecification(es, cache_);
			if (!res)
			{
				GlobalInfoDataCommon::Instance()->LogErrorLn("GetElevatorSpecification occur error!");
			}
			//
			if (res && !EqualElevatorSpecificationWithHeight(&es, &es_))
			{
				GetDecorationVrInterface()->ChangeElevatorSpecification(&es);
				es_ = es;
				return true;
			}
		}

		return false;
	}

	void arg_cache::SetHandrailPos()
	{
		int left, right, back;
		ElevatorSpecificationParser::GetHandrailPosMark(left, right, back, cache_);
		GetDecorationVrInterface()->SetHandrailPosMark(PartTypeLeftWall, left);
		GetDecorationVrInterface()->SetHandrailPosMark(PartTypeRightWall, right);
		GetDecorationVrInterface()->SetHandrailPosMark(PartTypeBackWall, back);
	}

	void arg_cache::ChangeAll(const char* content)
	{
		if (GlobalInfoDataCommon::Instance()->GetElevatorType() == kConstTypeEscalator)
		{
			auto car_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(PartTypeEscalatorConfig);
			if (!car_operator->SetPartByContent(PartTypeEscalatorConfig, content))
			{
				if (cb_ != nullptr)
				{
#ifdef RENDER_SKETCHER_ANDROID
					LOGI("create esca car by content failed");
#endif
					cache_.clear();
					cb_(CreateVRPart, -1, "create esca car by content failed");
				}
				return;
			}
			if (!GetDecorationVrInterface()->DoChange(PartTypeEscalatorConfig, true))
			{
				cache_.clear();
				return;
			}
		}
		else
		{
#if !defined(ONLY_CAR)
			auto hall_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(PartTypeHallConfig);
			if (!hall_operator->SetPartByContent(PartTypeHallConfig, content))
			{
				if (cb_ != nullptr)
				{
#ifdef RENDER_SKETCHER_ANDROID
					LOGI("create hall by content failed");
#endif
					cache_.clear();
					cb_(CreateVRPart, -1, "create hall by content failed");
				}
				return;
			}
			if (!GetDecorationVrInterface()->DoChange(PartTypeHallConfig, false))
			{
				cache_.clear();
				return;
			}
#endif

			auto car_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(PartTypeCarConfig);
			if (!car_operator->SetPartByContent(PartTypeCarConfig, content))
			{
				if (cb_ != nullptr)
				{
#ifdef RENDER_SKETCHER_ANDROID
					LOGI("create car by content failed");
#endif
					cache_.clear();
					cb_(CreateVRPart, -1, "create car by content failed");
				}
				return;
			}
			SetHandrailPos();
			if (!GetDecorationVrInterface()->DoChange(PartTypeCarConfig, true))
			{
				cache_.clear();
				return;
			}
		}
	}

	int arg_cache::GetChangeType()
	{
		auto gd = GlobalInfoDataCommon::Instance();

		int type_count = change_type_set_.size();
		int category_count = category_set_.size();

		//没有部件变化
		if (type_count == 0)
		{
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("no part changed");
#endif
			return -1;
		}

		//部件类型变化为ALL(door_type即使如此), 大厅轿厢都有部件变化
		if (change_type_set_.count(ALL) == 1 || category_count > 1)
		{
#ifdef RENDER_SKETCHER_ANDROID
			LOGI("all part changed");
#endif
			return ALL;
		} 
		
		if (category_count == 1)
		{
			if (type_count > 1)
			{
				//0:car, 1:hall, 2:esca
				int arr[] = { PartTypeCarConfig, PartTypeHallConfig, PartTypeEscalatorConfig };
				auto category = *category_set_.begin();
				return arr[category];
			}
			else if (type_count == 1)
			{
				return *change_type_set_.begin();
			}
		}
		else if (category_count == 0)
		{
			//默认出现这种情况可能是出问题了
			return ALL;
		}
#ifdef RENDER_SKETCHER_ANDROID
		LOGI("default no part changed");
#endif
		return -1;
	}

	void arg_cache::DoorSpecialHandle()
	{
		auto config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
		auto car_config = config_mgr->GetCurrentCarConfig();
		auto hall_config = config_mgr->GetCurrentHallConfig();

		auto it1 = change_type_set_.find(PartTypeCarDoor);
		auto it2 = change_type_set_.find(PartTypeImportedCarDoor);
		if (it1 != change_type_set_.end() && it2 != change_type_set_.end())
		{
			int64 part_id = Util::AToInt64(cache_["IMPORTEDDOORID"].c_str());
			if (part_id > 0)
			{
				change_type_set_.erase(it1);
				if (car_config)
				{
					car_config->RemovePart(PartTypeCarDoor);
				}
			}
			else
			{
				change_type_set_.erase(it2);
				if (car_config)
				{
					car_config->RemovePart(PartTypeImportedCarDoor);
				}
			}
		}
		
		it1 = change_type_set_.find(PartTypeHallDoor);
		it2 = change_type_set_.find(PartTypeImportedHallDoor);
		if (it1 != change_type_set_.end() && it2 != change_type_set_.end())
		{
			int64 part_id = Util::AToInt64(cache_["IMPORTEDHALLDOORID"].c_str());
			if (part_id > 0)
			{
				change_type_set_.erase(it1);
				if (hall_config)
				{
					hall_config->RemovePart(PartTypeHallDoor);
				}
			}
			else
			{
				change_type_set_.erase(it2);
				if (hall_config)
				{
					hall_config->RemovePart(PartTypeImportedHallDoor);
				}
			}
		}

		it1 = change_type_set_.find(PartTypeFrontWall);
		it2 = change_type_set_.find(PartTypeFrontAccessory);
		if (it1 != change_type_set_.end() && it2 != change_type_set_.end())
		{
			int64 part_id = Util::AToInt64(cache_["FRONTWALLID"].c_str());
			if (part_id > 0)
			{
				change_type_set_.erase(it1);
				if (hall_config)
				{
					hall_config->RemovePart(PartTypeFrontWall);
				}
			}
			else
			{
				change_type_set_.erase(it2);
				if (hall_config)
				{
					hall_config->RemovePart(PartTypeFrontAccessory);
				}
			}
		}
	}

	void arg_cache::SetFrontWallMaterial()
	{
#ifdef RENDER_SKETCHER_HTML
		printf("arg_cache::SetFrontWallMaterial");
#endif
		tstring para1 = TSTR("FRONTWALLMATERIALID");
		if (cache_.find(para1) != cache_.end()) 
		{
			int64 mat_id = Util::AToInt64(cache_[para1].c_str());
#ifdef RENDER_SKETCHER_HTML
			printf("FRONTWALLMATERIALID = %s\n", cache_[para1].c_str());
#endif
			if (mat_id > 0) 
			{
				GetDecorationVrInterface()->SetMaterialByGoods(PartTypeFrontWall, mat_id, false);
			}
		}
		tstring para2 = TSTR("DOORHEADERMATERIALID");
		if (cache_.find(para2) != cache_.end())
		{
			int64 mat_id = Util::AToInt64(cache_[para2].c_str());
#ifdef RENDER_SKETCHER_HTML
			printf("DOORHEADERMATERIALID = %s\n", cache_[para2].c_str());
#endif
			if (mat_id > 0)
			{
				GetDecorationVrInterface()->SetMaterialByGoods(PartTypeDoorHeader, mat_id, false);
			}
		}
		tstring para3 = TSTR("COPWALLMATERIALID");
		if (cache_.find(para3) != cache_.end())
		{
			int64 mat_id = Util::AToInt64(cache_[para3].c_str());
#ifdef RENDER_SKETCHER_HTML
			printf("COPWALLMATERIALID = %s\n", cache_[para3].c_str());
#endif
			if (mat_id > 0)
			{
				GetDecorationVrInterface()->SetMaterialByGoods(PartTypeCopWall, mat_id, false);
			}
		}
		tstring para4 = TSTR("ENTRANCECOLUMNMATERIALID");
		if (cache_.find(para4) != cache_.end())
		{
			int64 mat_id = Util::AToInt64(cache_[para4].c_str());
#ifdef RENDER_SKETCHER_HTML
			printf("ENTRANCECOLUMNMATERIALID = %s\n", cache_[para4].c_str());
#endif
			if (mat_id > 0)
			{
				GetDecorationVrInterface()->SetMaterialByGoods(PartTypeEntranceColumn, mat_id, false);
			}
		}

		tstring para5 = TSTR("FRONTWALLCONSTRUCTIONID");
		if (cache_.find(para5) != cache_.end())
		{
			int ft = Util::AToI(cache_[para5].c_str());
			if (ft > 0)
			{
				auto config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
				auto car_config = config_mgr->GetCurrentCarConfig();
				auto cop_part = static_cast<ElectricPart*>(car_config->GetPart(PartTypeCop));
				cop_part->SetPanelType(ft);
			}
		}

		if (!GetDecorationVrInterface()->DoChange(PartTypeFrontWall, true))
		{
			cache_.clear();
			return;
		}
	}

}
