#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_HI;
class MaterialChannel;

class VrHallIndicatorVisitor : public BaseVisitor
{
public:
	VrHallIndicatorVisitor();
	~VrHallIndicatorVisitor();

	DEFINE_CREATE_FUN(VrHallIndicatorVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	int GetSetupPos(int pos, int ele_id);
	std::shared_ptr<Sel_HI> model_;
	std::shared_ptr<MaterialChannel> material_;
};
}
