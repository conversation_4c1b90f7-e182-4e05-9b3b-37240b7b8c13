#ifndef _VR_CARCONFIG_VISITOR_H_
#define _VR_CARCONFIG_VISITOR_H_

#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class VrCarConfigVisitor :public BaseVisitor
	{
	public:
		VrCarConfigVisitor();
		virtual ~VrCarConfigVisitor();

		DEFINE_CREATE_FUN(VrCarConfigVisitor);
		virtual void Initialize() override;
		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	};
}

#endif//_VR_CARCONFIG_VISITOR_H_

