#include "stdafx.h"
#include "Global_Esca_PartMark.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Global_Esca_PartMark::Getarr_PartMark(int row)
		{
			return visitor_->GetElementInt(row, 1);
		}

		void Global_Esca_PartMark::Setarr_PartMark(int row, int val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		float Global_Esca_PartMark::Getfloat_offsetX(int row)
		{
			return visitor_->GetElementFloat(row, 2);
		}

		void Global_Esca_PartMark::Setfloat_offsetX(int row, float val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

		bool Global_Esca_PartMark::GetSelect(int row)
		{
			return visitor_->GetElementBool(row, 5);
		}

		void Global_Esca_PartMark::SetSelect(int row, bool val)
		{
			visitor_->SetElementValue(row, 5, val);
		}
	}
}