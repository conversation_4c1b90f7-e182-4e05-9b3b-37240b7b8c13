#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Accessory;
	class MaterialChannel;
	class Sel_Hung_Part;

	class VrAccessoryVisitor : public BaseVisitor
	{
	public:
		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<Sel_Accessory> model_;
		std::shared_ptr<MaterialChannel> material_;

		int row_;
	};

	class VrLeftAccessoryVisitor : public VrAccessoryVisitor
	{
	public:
		VrLeftAccessoryVisitor();
		virtual ~VrLeftAccessoryVisitor();

		DEFINE_CREATE_FUN(VrLeftAccessoryVisitor);
	};

	class VrBackAccessoryVisitor : public VrAccessoryVisitor
	{
	public:
		VrBackAccessoryVisitor();
		virtual ~VrBackAccessoryVisitor();

		DEFINE_CREATE_FUN(VrBackAccessoryVisitor);
	};

	class VrRightAccessoryVisitor : public VrAccessoryVisitor
	{
	public:
		VrRightAccessoryVisitor();
		virtual ~VrRightAccessoryVisitor();

		DEFINE_CREATE_FUN(VrRightAccessoryVisitor);
	};

	class VrBottomAccessoryVisitor : public BaseVisitor
	{
	public:
		VrBottomAccessoryVisitor();
		virtual ~VrBottomAccessoryVisitor();

		DEFINE_CREATE_FUN(VrBottomAccessoryVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<Sel_Hung_Part> model_;
	};

	class VrICCardVisitor : public BaseVisitor
	{
	public:
		VrICCardVisitor();
		virtual ~VrICCardVisitor();

		DEFINE_CREATE_FUN(VrICCardVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<Sel_Hung_Part> model_;
	};
}