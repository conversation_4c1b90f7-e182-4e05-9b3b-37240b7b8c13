#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_HL;
class MaterialChannel;

class VrLanternVisitor : public BaseVisitor
{
public:
	VrLanternVisitor();
	~VrLanternVisitor();

	DEFINE_CREATE_FUN(VrLanternVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	int GetSetupPos(int pos, int ele_id);
	std::shared_ptr<Sel_HL> model_;
	std::shared_ptr<MaterialChannel> material_;
};
}
