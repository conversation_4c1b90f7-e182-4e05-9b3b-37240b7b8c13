#ifndef _VR_EscaStepLightingVisitor_H_
#define _VR_EscaStepLightingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Step_Lighting.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaStepLightingVisitor : public BaseVisitor
	{
	public:
		VrEscaStepLightingVisitor();
		virtual ~VrEscaStepLightingVisitor();

		DEFINE_CREATE_FUN(VrEscaStepLightingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Step_Lighting> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
