#include "stdafx.h"
#include "ElevatorPartOperator.h"

namespace decoration_vr_interface
{
	ElevatorPartOperator::ElevatorPartOperator()
	{
	}


	ElevatorPartOperator::~ElevatorPartOperator()
	{
	}

	bool ElevatorPartOperator::SetPart(int part_type, int64 part_id)
	{
		auto part = CreatePart(part_type, part_id);
		if (!part)
		{
			return false;
		}

		BaseSetElevatorPart(part_type, part);
		return true;
	}

	bool ElevatorPartOperator::SetPartByContent(int part_type, const rse::string& c)
	{
		auto part = CreatePart(part_type, c);
		if (!part)
		{
			return false;
		}

		BaseSetElevatorPart(part_type, part);
		return true;
	}

}