#include "stdafx.h"
#include "Sel_Esca_Comb.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		rse::string Sel_Esca_Comb::Getstr_Path(int row)
		{
			const char* val = visitor_->GetElementString(row, 0);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Comb::Setstr_Path(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

	}

}