﻿//
//  Sel_Ceiling.h
//  VrVisitor
//
//  Created by vrprg on 10:25:05.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Accessory.h"


namespace decoration_vr_interface
{

	bool Sel_Accessory::GetSelectable(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Accessory::SetSelectable(int row, bool enable)
	{
		visitor_->SetElementValue(row, 0, enable);
	}

	rse::string Sel_Accessory::GetPath(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Accessory::SetPath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}


	float Sel_Accessory::GetWidth(int row)
	{
		return visitor_->GetElementFloat(row, 3);
	}


	void Sel_Accessory::SetWidth(int row, float val)
	{
		visitor_->SetElementValue(row, 3, val);
	}

	float Sel_Accessory::GetHeight(int row)
	{
		return visitor_->GetElementFloat(row, 4);
	}

	void Sel_Accessory::SetHeight(int row, float val)
	{
		visitor_->SetElementValue(row, 4, val);
	}

	float Sel_Accessory::GetDoorWidth(int row)
	{
		return visitor_->GetElementFloat(row, 5);
	}

	void Sel_Accessory::SetDoorWidth(int row, float val)
	{
		visitor_->SetElementValue(row, 5, val);
	}

	bool Sel_Hung_Part::GetSelectable(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Hung_Part::SetSelectable(int row, bool enable)
	{
		visitor_->SetElementValue(row, 0, enable);
	}

	rse::string Sel_Hung_Part::GetPath(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Hung_Part::SetPath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	float Sel_Hung_Part::GetOffsetX(int row) 
	{ 
		return visitor_->GetElementInt(row, 2); 
	}
	void Sel_Hung_Part::SetOffsetX(int row, float val) 
	{ 
		visitor_->SetElementValue(row, 2, val); 
	}

	float Sel_Hung_Part::GetOffsetY(int row) 
	{ 
		return visitor_->GetElementInt(row, 3); 
	}
	void Sel_Hung_Part::SetOffsetY(int row, float val) 
	{ 
		visitor_->SetElementValue(row, 3, val); 
	}

	int Sel_Hung_Part::GetLocation(int row) 
	{ 
		return visitor_->GetElementInt(row, 4); 
	}
	void Sel_Hung_Part::SetLocation(int row, int val) 
	{ 
		visitor_->SetElementValue(row, 4, val); 
	}

	int Sel_Hung_Part::GetPartType(int row) 
	{ 
		return visitor_->GetElementInt(row, 5); 
	}

	void Sel_Hung_Part::SetPartType(int row, int val) 
	{ 
		visitor_->SetElementValue(row, 5, val); 
	}

	int Sel_Hung_Part::GetExtra1(int row)
	{
		if (visitor_->GetColumnCount() > 6)
		{
			return visitor_->GetElementInt(row, 6);
		}
		return 1;
	}

	void Sel_Hung_Part::SetExtra1(int row, int val)
	{
		if (visitor_->GetColumnCount() > 6)
		{
			return visitor_->SetElementValue(row, 6, val);
		}
	}
}