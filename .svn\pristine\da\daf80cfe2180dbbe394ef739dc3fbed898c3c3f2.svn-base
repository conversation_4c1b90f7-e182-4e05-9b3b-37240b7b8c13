﻿//
//  Global_Setting_Car.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Global_Setting_Car.h"


namespace decoration_vr_interface
{

float Global_Setting_Car::GetCar_Pos_X(int row)
{
    return visitor_->GetElementFloat(row, 0);
}

void Global_Setting_Car::SetCar_Pos_X(int row, float val)
{
    visitor_->SetElementValue(row, 0, val);
}

float Global_Setting_Car::GetCar_Pos_Y(int row)
{
    return visitor_->GetElementFloat(row, 1);
}

void Global_Setting_Car::SetCar_Pos_Y(int row, float val)
{
    visitor_->SetElementValue(row, 1, val);
}

float Global_Setting_Car::GetCar_Pos_Z(int row)
{
    return visitor_->GetElementFloat(row, 2);
}

void Global_Setting_Car::SetCar_Pos_Z(int row, float val)
{
    visitor_->SetElementValue(row, 2, val);
}

float Global_Setting_Car::GetCar_Size_X(int row)
{
    return visitor_->GetElementFloat(row, 3);
}

void Global_Setting_Car::SetCar_Size_X(int row, float val)
{
    visitor_->SetElementValue(row, 3, val);
}

float Global_Setting_Car::GetCar_Size_Y(int row)
{
    return visitor_->GetElementFloat(row, 4);
}

void Global_Setting_Car::SetCar_Size_Y(int row, float val)
{
    visitor_->SetElementValue(row, 4, val);
}

float Global_Setting_Car::GetCar_Size_Z(int row)
{
    return visitor_->GetElementFloat(row, 5);
}

void Global_Setting_Car::SetCar_Size_Z(int row, float val)
{
    visitor_->SetElementValue(row, 5, val);
}

float Global_Setting_Car::GetDoor_Size_X(int row)
{
    return visitor_->GetElementFloat(row, 6);
}

void Global_Setting_Car::SetDoor_Size_X(int row, float val)
{
    visitor_->SetElementValue(row, 6, val);
}

float Global_Setting_Car::GetDoor_Size_Y(int row)
{
    return visitor_->GetElementFloat(row, 7);
}

void Global_Setting_Car::SetDoor_Size_Y(int row, float val)
{
    visitor_->SetElementValue(row, 7, val);
}

float Global_Setting_Car::GetDoor_Size_Z(int row)
{
    return visitor_->GetElementFloat(row, 8);
}

void Global_Setting_Car::SetDoor_Size_Z(int row, float val)
{
    visitor_->SetElementValue(row, 8, val);
}

float Global_Setting_Car::GetCeiling_Ply(int row)
{
    return visitor_->GetElementFloat(row, 9);
}

void Global_Setting_Car::SetCeiling_Ply(int row, float val)
{
    visitor_->SetElementValue(row, 9, val);
}

float Global_Setting_Car::GetDoor_Offset_X(int row)
{
    return visitor_->GetElementFloat(row, 10);
}

void Global_Setting_Car::SetDoor_Offset_X(int row, float val)
{
    visitor_->SetElementValue(row, 10, val);
}

float Global_Setting_Car::GetDoorThick(int row)
{
    return visitor_->GetElementFloat(row, 11);
}

void Global_Setting_Car::SetDoorThick(int row, float val)
{
    visitor_->SetElementValue(row, 11, val);
}

float Global_Setting_Car::GetDoorBiasDistance(int row)
{
    if (visitor_->GetColumnCount() > 12)
    {
        return visitor_->GetElementFloat(row, 12);
    }
    return 0.0f;
}

void Global_Setting_Car::SetDoorBiasDistance(int row, float val)
{
    if (visitor_->GetColumnCount() > 12)
    {
        visitor_->SetElementValue(row, 12, val);
    }
}

}