#ifndef _VR_CARDOOR_VISITOR_H_
#define _VR_CARDOOR_VISITOR_H_
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Door_Imported;
	class MaterialChannel;
	class VrCarDoorVisitor :public BaseVisitor
	{
	public:
		VrCarDoorVisitor();
		virtual ~VrCarDoorVisitor();

		DEFINE_CREATE_FUN(VrCarDoorVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void NotifyVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	private:
		std::shared_ptr<MaterialChannel> material_;
		std::shared_ptr<MaterialChannel> material_oppo_;
		std::shared_ptr<Sel_Door_Imported> model_;
	};
}
#endif//_VR_CARDOOR_VISITOR_H_

