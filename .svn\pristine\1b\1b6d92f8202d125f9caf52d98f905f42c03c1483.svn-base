#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Sel_Esca_Skirt_Brush : public DGBaseVisitor
		{
		public:
			Sel_Esca_Skirt_Brush(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			bool Getbool_select(int row);
			void Setbool_select(int row, bool val);

			rse::string Getstr_Path(int row);
			void Setstr_Path(int row, const tchar* val);

		};
	}

}