#include "stdafx.h"
#include "svr_config.h"

#include "svr_json_helper.h"
#include "part_type.h"

namespace svr_data
{
	bool SvrExhibitConfig::Parse(const Json::Value& jv)
	{	
		if (IsNullJsonValue(jv))
		{
			return false;
		}

		if (!SvrExhibitCommonPart::Parse(jv))
		{
			return false;
		}

		if (!ParseSetupPositionDatas(jv["setupPositionDatas"]))
		{
			return false;
		}

		return true;
	}

	bool SvrExhibitConfig::ParseSetupPositionDatas(const Json::Value& jv)
	{
		auto size = jv.size();
		SetupPositionDatas.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!GetJsonValue(jv[i], SetupPositionDatas[i]))
			{
				return false;
			}
		}

		return true;
	}

	SvrExhibitCarConfig::SvrExhibitCarConfig() 
	{ 
		part_info_.basic_info_.PartType = decoration_vr_interface::PartTypeCarConfig;
	}

	bool SvrExhibitCarConfig::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return false;
		}

		if (!SvrExhibitConfig::Parse(jv))
		{
			return false;
		}

		if (!ParseWalls(jv["walls"]))
		{
			return false;
		}

		if (!ParsFitElevatorSizes(jv["fitElevatorSizes"]))
		{
			return false;
		}

		if (!GetJsonValue(jv["frontWallType"], FrontWallType))
		{
			return false;
		}

		if (!GetJsonValue(jv["doorWidth"], DoorWidth))
		{
			return false;
		}

		if (!GetJsonValue(jv["doorHeight"], DoorHeight))
		{
			return false;
		}

		GetJsonValue(jv["floorCount"], FloorCount);

		return true;
	}

	bool SvrExhibitCarConfig::ParseWalls(const Json::Value& jv)
	{
		auto size = jv.size();
		Walls.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!Walls[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrExhibitCarConfig::ParsFitElevatorSizes(const Json::Value& jv)
	{
		auto size = jv.size();
		FitElevatorSizes.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!GetJsonValue(jv[i], FitElevatorSizes[i]))
			{
				return false;
			}
		}

		return true;
	}

}
