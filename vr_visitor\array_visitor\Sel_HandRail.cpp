//
//  Sel_HandRail.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_HandRail.h"


namespace decoration_vr_interface
{

rse::string Sel_HandRail::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 0);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_HandRail::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 0, val);
}

int Sel_HandRail::GetPosition(int row)
{
    return visitor_->GetElementInt(row, 1);
}

void Sel_HandRail::SetPosition(int row, int val)
{
    visitor_->SetElementValue(row, 1, val);
}

int Sel_HandRail::Getleft(int row)
{
    return visitor_->GetElementInt(row, 2);
}

void Sel_HandRail::Setleft(int row, int val)
{
    visitor_->SetElementValue(row, 2, val);
}

int Sel_HandRail::Getright(int row)
{
    return visitor_->GetElementInt(row, 3);
}

void Sel_HandRail::Setright(int row, int val)
{
    visitor_->SetElementValue(row, 3, val);
}

int Sel_HandRail::Getback(int row)
{
    return visitor_->GetElementInt(row, 4);
}

void Sel_HandRail::Setback(int row, int val)
{
    visitor_->SetElementValue(row, 4, val);
}

}