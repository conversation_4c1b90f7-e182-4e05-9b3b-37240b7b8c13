#pragma once

#include "VrChangeArg.h"

namespace decoration_vr_interface
{
	class IVrVisitor
	{
	public:
		virtual int GetType() = 0;
		virtual void Initialize() = 0;
		virtual bool UpdateVr(VrChangeArg* vr_change) = 0;
		virtual void NotifyVr(VrChangeArg* vr_change) = 0;
		virtual const rse::vector<PartTypeId>& GetAvailablePartTypeId() = 0;
		virtual void PrintData(const tchar* file_name) = 0;
	};

	typedef std::shared_ptr<IVrVisitor> IVrVisitorPtr;
}