#ifndef _VR_EscaHandrailEnterVisitor_H_
#define _VR_EscaHandrailEnterVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Handrail_Enter.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaHandrailEnterVisitor : public BaseVisitor
	{
	public:
		VrEscaHandrailEnterVisitor();
		virtual ~VrEscaHandrailEnterVisitor();

		DEFINE_CREATE_FUN(VrEscaHandrailEnterVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Handrail_Enter> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
