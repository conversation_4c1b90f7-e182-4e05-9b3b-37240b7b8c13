#include "stdafx.h"
#include "db_visitor.h"

#include "netWork/HttpAsyncManage.h"

using namespace svr_data;
namespace decoration_vr_interface
{
	DbVisitor::DbVisitor()
	{
	}


	DbVisitor::~DbVisitor()
	{
	}


	tstring DbVisitor::GetServiceFuncName(int part_type)
	{
		switch (part_type)
		{
		case PartTypeCarConfig:
			return TSTR("LoadCar");
		case PartTypeHallConfig:
			return TSTR("LoadHall");
		case PartTypeTop:
			return TSTR("LoadTop");
		case PartTypeCop:
			return TSTR("LoadCop");
		case PartTypeCopDisplay:
			return TSTR("LoadCopDisplay");
		case PartTypeCopButton:
			return TSTR("LoadCopButton");
		case PartTypeAuxCop:
			return TSTR("LoadAuxCop");
		case PartTypeAuxCopDisplay:
			return TSTR("LoadAuxCopDisplay");
		case PartTypeAuxCopButton:
			return TSTR("LoadAuxCopButton");
		case PartTypeHDCop:
		case PartTypeAuxHDCop:
			return TSTR("LoadHdCop");
		case PartTypeHDCopButton:
		case PartTypeAuxHDCopButton:
			return TSTR("LoadHdCopButton");
		case PartTypeHandrail:
			return TSTR("LoadHandrail");
		case PartTypeMirror:
			return TSTR("LoadMirror");
		case PartTypeLeftWall:
		case PartTypeRightWall:
		case PartTypeBackWall:
			return TSTR("LoadCarWall");
		case PartTypeLop:
			return TSTR("LoadLop");
		case PartTypeLopDisplay:
			return TSTR("LoadLopDisplay");
		case PartTypeLopButton:
			return TSTR("LoadLopButton");
		case PartTypeLantern:
			return TSTR("LoadLantern");
		case PartTypeHallFireBox:
			return TSTR("LoadHallFireBox");
		case PartTypeHallIndicatorDisplay:
			return TSTR("LoadHallIndicatorDisplay");
		case PartTypeHallIndicator:
			return TSTR("LoadHallIndicator");
		case PartTypeEMIDS:
			return TSTR("LoadEmids");
		case PartTypeJamb:
			return TSTR("LoadHallJamb");
		case PartTypeBottom:
		case PartTypeFrontWall:
		case PartTypeCopWall:
		case PartTypeDoorHeader:
		case PartTypeCarDoor:
		case PartTypeHallDoor:
		case PartTypeHallBottom:
		//case PartTypeJamb:
		case PartTypeHallWall:
			return TSTR("LoadMaterial");

		case PartTypeEscalatorConfig:
			return TSTR("LoadEscalatorConfig");
		case PartTypeEscaStep:
			return TSTR("LoadEscaStep");

		case PartTypeEscaStepLighting:
			return TSTR("LoadEscaStepLighting");
		case PartTypeEscaHandrail:
			return TSTR("LoadEscaHandrail");
		case PartTypeEscaHandrailGuid:
			return TSTR("LoadEscaHandrailGuid");
		case PartTypeEscaHandrailEnter:
			return TSTR("LoadEscaHandrailEnter");
		case PartTypeEscaAcessOver:
			return TSTR("LoadEscaAcessOver");
		case PartTypeEscaBalustrade:
			return TSTR("LoadEscaBalustrade");
		case PartTypeEscaComb:
			return TSTR("LoadEscaComb");
		case PartTypeEscaCombLighting:
			return TSTR("LoadEscaCombLighting");
		case PartTypeEscaDecking:
			return TSTR("LoadEscaDecking");
		case PartTypeEscaSkirt:
			return TSTR("LoadEscaSkirt");
		case PartTypeEscaSkirtLighting:
			return TSTR("LoadEscaSkirtLighting");
		case PartTypeEscaSkirtBrush:
			return TSTR("LoadEscaSkirtBrush");
		case PartTypeEscaTrafficLight:
			return TSTR("LoadEscaTrafficLight");
		case PartTypeEscaSideCladding:
			return TSTR("LoadEscaSideCladding");
		case PartTypeEscaSideCladdingLighting:
			return TSTR("LoadEscaSideCladdingLighting");

		case PartTypeEscaScenesType:
			return TSTR("LoadEscaScenes");
		case PartTypeEscaPhotoelectric:
			return TSTR("LoadEscaPhotoelectric");
		default:
			return TSTR("");
		}
	}

	tstring DbVisitor::GetServiceGoodsFuncName(int part_type)
	{
		switch (part_type)
		{
		case PartTypeCarConfig:
			return TSTR("LoadCarByGoodsId");
		case PartTypeHallConfig:
			return TSTR("LoadHallByGoodsId");
		case PartTypeTop:
			return TSTR("LoadTopByGoodsId");
		case PartTypeCop:
			return TSTR("LoadCopByGoodsId");
		case PartTypeCopDisplay:
			return TSTR("LoadCopDisplayByGoodsId");
		case PartTypeCopButton:
			return TSTR("LoadCopButtonByGoodsId");
		case PartTypeAuxCop:
			return TSTR("LoadAuxCopByGoodsId");
		case PartTypeAuxCopDisplay:
			return TSTR("LoadAuxCopDisplayByGoodsId");
		case PartTypeAuxCopButton:
			return TSTR("LoadAuxCopButtonByGoodsId");
		case PartTypeHDCop:
		case PartTypeAuxHDCop:
			return TSTR("LoadHdCopByGoodsId");
		case PartTypeHDCopButton:
		case PartTypeAuxHDCopButton:
			return TSTR("LoadHdCopButtonByGoodsId");
		case PartTypeHandrail:
			return TSTR("LoadHandrailByGoodsId");
		case PartTypeMirror:
			return TSTR("LoadMirrorByGoodsId");
		case PartTypeLeftWall:
		case PartTypeRightWall:
		case PartTypeBackWall:
			return TSTR("LoadCarWallByGoodsId");
		case PartTypeLop:
			return TSTR("LoadLopByGoodsId");
		case PartTypeLopDisplay:
			return TSTR("LoadLopDisplayByGoodsId");
		case PartTypeLopButton:
			return TSTR("LoadLopButtonByGoodsId");
		case PartTypeLantern:
			return TSTR("LoadHallLanternByGoodsId");
		case PartTypeHallFireBox:
			return TSTR("LoadHallFireBoxByGoodsId");
		case PartTypeHallIndicatorDisplay:
			return TSTR("LoadHallIndicatorDisplayByGoodsId");
		case PartTypeHallIndicator:
			return TSTR("LoadHallIndicatorByGoodsId");
		case PartTypeEMIDS:
			return TSTR("LoadEMIDSByGoodsId");
		case PartTypeJamb:
			return TSTR("LoadHallJambByGoodsId");
		case PartTypeBottom:
		case PartTypeFrontWall:
		case PartTypeCopWall:
		case PartTypeDoorHeader:
		case PartTypeCarDoor:
		case PartTypeHallDoor:
		case PartTypeHallBottom:
			//case PartTypeJamb:
		case PartTypeHallWall:
			return TSTR("LoadMaterialByGoodsId");

		case PartTypeLeftAccessory:
			return TSTR("LoadLeftAccessoryByGoodsId");
		case PartTypeRightAccessory:
			return TSTR("LoadRightAccessoryByGoodsId");
		case PartTypeBackAccessory:
			return TSTR("LoadBackAccessoryByGoodsId");

		case PartTypeEscalatorConfig:
			return TSTR("LoadEscalatorConfigByGoodsId");
		case PartTypeEscaStep:
			return TSTR("LoadEscaStepByGoodsId");

		case PartTypeEscaStepLighting:
			return TSTR("LoadEscaStepLightingByGoodsId");
		case PartTypeEscaHandrail:
			return TSTR("LoadEscaHandrailByGoodsId");
		//case PartTypeEscaHandrailLighting:
		//	return TSTR("aaaaaaaaaa");
		case PartTypeEscaHandrailGuid:
			return TSTR("LoadEscaHandrailGuidByGoodsId");
		case PartTypeEscaHandrailEnter:
			return TSTR("LoadEscaHandrailEnterByGoodsId");
		case PartTypeEscaAcessOver:
			return TSTR("LoadEscaAcessOverByGoodsId");
		//case PartTypeEscaAcessOverFlag:
		//	return TSTR("aaaaaaaaaa");
		case PartTypeEscaBalustrade:
			return TSTR("LoadEscaBalustradeByGoodsId");
		case PartTypeEscaComb:
			return TSTR("LoadEscaCombByGoodsId");
		case PartTypeEscaCombLighting:
			return TSTR("LoadEscaCombLightingByGoodsId");
		case PartTypeEscaDecking:
			return TSTR("LoadEscaDeckingByGoodsId");
		case PartTypeEscaSkirt:
			return TSTR("LoadEscaSkirtByGoodsId");
		case PartTypeEscaSkirtLighting:
			return TSTR("LoadEscaSkirtLightingByGoodsId");
		//case PartTypeEscaSkirtLightingType:
		//	return TSTR("aaaaaaaaaa");
		case PartTypeEscaSkirtBrush:
			return TSTR("LoadEscaSkirtBrushByGoodsId");
		case PartTypeEscaTrafficLight:
			return TSTR("LoadEscaTrafficLightByGoodsId");
		case PartTypeEscaSideCladding:
			return TSTR("LoadEscaSideCladdingByGoodsId");
		case PartTypeEscaSideCladdingLighting:
			return TSTR("LoadEscaSideCladdingLightingByGoodsId");

		/*case PartTypeEscaPiece:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaStepFloorNumber:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaSideCladdingPit:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaSideCladdingLighting:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaPhotoelectric:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaOtherParts:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaAcessOverExpendType:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaCenterBrace:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaScenes:
			return TSTR("aaaaaaaaaa");*/

		case PartTypeEscaScenesType:
			return TSTR("LoadEscaScenesByGoodsId");
		case PartTypeEscaPhotoelectric:
			return TSTR("LoadEscaPhotoelectricByGoodsId");

		/*case PartTypeEscaScenesArrangement:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaDeckingCtrlBox:
			return TSTR("aaaaaaaaaa");
		case PartTypeEscaTrussLighting:
			return TSTR("aaaaaaaaaa");
		case HoistWayConfig:
			return TSTR("aaaaaaaaaa");
		case TractionMachine:
			return TSTR("aaaaaaaaaa");
		case ControlCabinet:
			return TSTR("aaaaaaaaaa");
		case ImageGroup_HoistWay:
			return TSTR("aaaaaaaaaa");
		case ImageGroup_Car:
			return TSTR("aaaaaaaaaa");
		case ImageGroup_Hall:
			return TSTR("aaaaaaaaaa");*/
		default:
			return TSTR("");
		}
	}

    decoration_vr_interface::DbVisitor* DbVisitor::Instance()
    {
        return static_cast<DbVisitor*>(GlobalInfoDataCommon::Instance()->GetDbVisitor());
        //return &ins;
    }

	svr_data::IDataBasePtr DbVisitor::GetClassData(rse::string command, Json::Value& jobj)
	{
		auto global_info = GlobalInfoDataCommon::Instance();
		auto data_site = Util::TStringToString(global_info->GetDataSiteUrl().c_str());
		auto service_name = Util::TStringToString(kConstDataServiceName);
		HttpAysncManage ham(data_site, service_name);

		auto login_info = global_info->GetLoginInfo();

		int lang_id = login_info != nullptr ? login_info->lang_id_ : 0;
		auto lib_id = login_info != nullptr ? login_info->library_id_ : 0;
		auto user_id = login_info != nullptr ? login_info->user_id_ : 0;

		rse::string sJsonSerialize = ham.DataSerializeToUtf8String(lib_id, lang_id, user_id, jobj);
		Json::Value jv;
		ham.SendRequest(jv, command, sJsonSerialize);
		return ham.GetClassData(jv);
	}

	svr_data::IDataBasePtr DbVisitor::LoadMaterial(int64 mat_id)
	{
		Json::Value jobj;
		jobj["partId"] = mat_id;
		return GetClassData(TSTR("LoadMaterial"), jobj);
	}

	svr_data::IDataBasePtr DbVisitor::LoadPart(int part_type, int64 part_id)
	{
		Json::Value jobj;
		jobj["partId"] = part_id;
		return GetClassData(GetServiceFuncName(part_type), jobj);
	}

	svr_data::IDataBasePtr DbVisitor::LoadMaterialByGoodsId(int64 part_id)
	{
		Json::Value jobj;
		jobj["goodsId"] = part_id;
		return GetClassData(TSTR("LoadMaterialByGoodsId"), jobj);
	}

	svr_data::IDataBasePtr DbVisitor::LoadBootomMaterialByGoodsId(int64 goods_id, int width, int depth, int doorWidth, bool isThroughDoor)
	{
		Json::Value jobj;
		jobj["goodsId"] = goods_id;
		jobj["width"] = width;
		jobj["depth"] = depth;
		jobj["doorWidth"] = doorWidth;
		jobj["isThroughDoor"] = isThroughDoor;
		return GetClassData(TSTR("LoadBottomMaterialByGoodsId"), jobj);
	}

	//svr_data::IDataBasePtr DbVisitor::LoadBottomMaterialByGoodsId(int64 part_id)
	//{
	//	Json::Value jobj;
	//	jobj["goodsId"] = part_id;
	//	return GetClassData(TSTR("LoadMaterialByGoodsId"), jobj);
	//}

	svr_data::IDataBasePtr DbVisitor::LoadPartByGoodsId(int part_type, int64 part_id)
	{
		Json::Value jobj;
		jobj["partId"] = part_id;
		return GetClassData(GetServiceGoodsFuncName(part_type), jobj);
	}

	svr_data::IDataBasePtr DbVisitor::LoadPartByString(int part_type, const rse::string& c)
	{
		Json::Value jobj;
		switch (part_type)  
		{
		case PartTypeCarConfig:	
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitCar"), jobj);
			break;
		case PartTypeHallConfig:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHall"), jobj);
			break;
		case PartTypeLeftWall:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			auto ret = GetClassData(TSTR("LoadExhibitLeftWallByString"), jobj);
			
			return GetExhibitWallFromExhibitCarConfig(part_type, ret);
		}
		case PartTypeRightWall:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			auto ret = GetClassData(TSTR("LoadExhibitRightWallByString"), jobj);

			return GetExhibitWallFromExhibitCarConfig(part_type, ret);
		}
		case PartTypeBackWall:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			auto ret = GetClassData(TSTR("LoadExhibitBackWallByString"), jobj);

			return GetExhibitWallFromExhibitCarConfig(part_type, ret);
		}
		case PartTypeId::PartTypeCarDoor:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitCarDoorByString"), jobj);
		}
		case PartTypeId::PartTypeFrontWall:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitFrontWallByString"), jobj);
		}
		case PartTypeId::PartTypeCopWall:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitCopWallByString"), jobj);
		}
		case PartTypeId::PartTypeDoorHeader:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitDoorHeaderByString"), jobj);
		}
		case PartTypeId::PartTypeBottom:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitBottomByString"), jobj);
		}
		case PartTypeId::PartTypeJamb:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitJambByString"), jobj);
		}
		case PartTypeId::PartTypeHallDoor:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallDoorByString"), jobj);
		}
		case PartTypeId::PartTypeTop:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitTopByString"), jobj);
		}
		case PartTypeId::PartTypeAuxCop:
		case PartTypeId::PartTypeAuxCopDisplay:
		case PartTypeId::PartTypeAuxCopButton:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitAuxCopByString"), jobj);
		}
		case PartTypeId::PartTypeCop:
		case PartTypeId::PartTypeCopButton:
		case PartTypeId::PartTypeCopDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitCopByString"), jobj);
		}
		case PartTypeId::PartTypeHDCop:
		case PartTypeId::PartTypeHDCopButton:
		case PartTypeId::PartTypeHDCopDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHdCopByString"), jobj);
		}
		case PartTypeId::PartTypeAuxHDCop:
		case PartTypeId::PartTypeAuxHDCopButton:
		case PartTypeId::PartTypeAuxHDCopDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitAuxHdCopByString"), jobj);
		}
		case PartTypeId::PartTypeHandrail:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHandrailByString"), jobj);
		}
		case PartTypeId::PartTypeMirror:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitMirrorByString"), jobj);
		}
		case PartTypeId::PartTypeLop:
		case PartTypeId::PartTypeLopButton:
		case PartTypeId::PartTypeLopDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitLopByString"), jobj);
		}
		case PartTypeId::PartTypeLantern:
		case PartTypeId::PartTypeLanternDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallLanternByString"), jobj);
		}

		case PartTypeId::PartTypeHallFireBox:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallFireBoxByString"), jobj);
		}

		case PartTypeId::PartTypeEMIDS:
		case PartTypeId::PartTypeEMIDSDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitEmidsByString"), jobj);
		}
		case PartTypeId::PartTypeHallIndicator:
		case PartTypeId::PartTypeHallIndicatorDisplay:
		{
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallIndicatorByString"), jobj);
		}
		case PartTypeFrontAccessory:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitImprotedFrontByString"), jobj);
		case PartTypeLeftAccessory:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitLeftAccessoryByString"), jobj);
		case PartTypeRightAccessory:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitRightAccessoryByString"), jobj);
		case PartTypeBackAccessory:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitBackAccessoryByString"), jobj);
		case PartTypeImportedCarDoor:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitCarDoorLIByString"), jobj);
		case PartTypeImportedHallDoor:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallDoorLIByString"), jobj);
		case PartTypeCarShell:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitShellByString"), jobj);
		case PartTypeHall:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallRoomByString"), jobj);
		case PartTypeHallShaft:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitShaftByString"), jobj);
		case PartTypeHallWall:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallWallByString"), jobj);
		case PartTypeHallBottom:
			jobj["partInfos"] = decoration_vr_interface::Util::TStringToString(c.c_str());
			return GetClassData(TSTR("LoadExhibitHallFloorByString"), jobj);
		default:
			break;
		}

		return nullptr;
	}

	svr_data::IDataBasePtr DbVisitor::GetExhibitWallFromExhibitCarConfig(int part_type, svr_data::IDataBasePtr ret)
	{
		if (ret == nullptr) return nullptr;

		auto dbConfig = static_cast<svr_data::SvrExhibitCarConfig*>(ret.get());
		if (dbConfig != nullptr)
		{
			auto find_it = std::find_if(dbConfig->Walls.begin(), dbConfig->Walls.end(), [&part_type](SvrWallData &it)
			{
				return it.basic_info_.PartType == part_type;
			});

			if (find_it != dbConfig->Walls.end())
			{
				auto info = RSE_MAKE_SHARED<SvrExhibitWall>();
				info->WallInfo = *find_it;
				info->MaterialInfos = dbConfig->material_infos_;
				return info;
			}
		}

		return ret;
	}
}
