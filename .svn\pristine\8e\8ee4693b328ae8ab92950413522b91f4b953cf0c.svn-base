#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	class Global_Other_Datas : public DGBaseVisitor
	{
	public:
		Global_Other_Datas(IDGSceneEx* scene, const tchar* arr_name)
			:DGBaseVisitor(scene, arr_name) {}
		float GetFrontSkirtingHeight(int row);
		void SetFrontSkirtingHeight(int row, float val);

		int GetElevatorModel(int row);
		void SetElevatorModel(int row, int val);
	};
}