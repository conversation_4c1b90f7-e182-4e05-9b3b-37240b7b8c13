#ifndef _VR_EscaDeckingVisitor_H_
#define _VR_EscaDeckingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Decking.h"


namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaDeckingVisitor : public BaseVisitor
	{
	public:
		VrEscaDeckingVisitor();
		virtual ~VrEscaDeckingVisitor();

		DEFINE_CREATE_FUN(VrEscaDeckingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Decking> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
