#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class Sel_HallDoor_Imported;
	class VrHallDoorVisitor : public BaseVisitor
	{
	public:
		VrHallDoorVisitor();
		~VrHallDoorVisitor();

		DEFINE_CREATE_FUN(VrHallDoorVisitor);
		virtual void Initialize() override;
		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<MaterialChannel> material_;
		std::shared_ptr<Sel_HallDoor_Imported> model_;
	};
}
