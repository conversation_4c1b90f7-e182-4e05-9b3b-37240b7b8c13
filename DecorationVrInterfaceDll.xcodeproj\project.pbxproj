// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 47;
	objects = {

/* Begin PBXAggregateTarget section */
		D05124A71BFF17B200CDA819 /* BUILD_ALL_MBCS */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = D05124A81BFF17B200CDA819 /* Build configuration list for PBXAggregateTarget "BUILD_ALL_MBCS" */;
			buildPhases = (
			);
			dependencies = (
				2B56D0132064D14600DF2F76 /* PBXTargetDependency */,
			);
			name = BUILD_ALL_MBCS;
			productName = BUILD_ALL_MBCS;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		2B0791BF2134E46100A65EE1 /* EscaAccessOverVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07918F2134E46000A65EE1 /* EscaAccessOverVisitor.cpp */; };
		2B0791C02134E46100A65EE1 /* EscaAccessOverVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791902134E46000A65EE1 /* EscaAccessOverVisitor.h */; };
		2B0791C12134E46100A65EE1 /* EscaAcessOverExpendTypeVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791912134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.cpp */; };
		2B0791C22134E46100A65EE1 /* EscaAcessOverExpendTypeVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791922134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.h */; };
		2B0791C32134E46100A65EE1 /* EscaAcessOverFlagVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791932134E46000A65EE1 /* EscaAcessOverFlagVisitor.cpp */; };
		2B0791C42134E46100A65EE1 /* EscaAcessOverFlagVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791942134E46000A65EE1 /* EscaAcessOverFlagVisitor.h */; };
		2B0791C52134E46100A65EE1 /* EscaBalustradeVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791952134E46000A65EE1 /* EscaBalustradeVisitor.cpp */; };
		2B0791C62134E46100A65EE1 /* EscaBalustradeVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791962134E46100A65EE1 /* EscaBalustradeVisitor.h */; };
		2B0791C72134E46100A65EE1 /* EscaCombLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791972134E46100A65EE1 /* EscaCombLightingVisitor.cpp */; };
		2B0791C82134E46100A65EE1 /* EscaCombLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791982134E46100A65EE1 /* EscaCombLightingVisitor.h */; };
		2B0791C92134E46100A65EE1 /* EscaCombVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791992134E46100A65EE1 /* EscaCombVisitor.cpp */; };
		2B0791CA2134E46100A65EE1 /* EscaCombVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07919A2134E46100A65EE1 /* EscaCombVisitor.h */; };
		2B0791CB2134E46100A65EE1 /* EscaConfigVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07919B2134E46100A65EE1 /* EscaConfigVisitor.cpp */; };
		2B0791CC2134E46100A65EE1 /* EscaConfigVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07919C2134E46100A65EE1 /* EscaConfigVisitor.h */; };
		2B0791CD2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07919D2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.cpp */; };
		2B0791CE2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07919E2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.h */; };
		2B0791CF2134E46100A65EE1 /* EscaDeckingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07919F2134E46100A65EE1 /* EscaDeckingVisitor.cpp */; };
		2B0791D02134E46100A65EE1 /* EscaDeckingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A02134E46100A65EE1 /* EscaDeckingVisitor.h */; };
		2B0791D12134E46100A65EE1 /* EscaHandrailEnterVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A12134E46100A65EE1 /* EscaHandrailEnterVisitor.cpp */; };
		2B0791D22134E46100A65EE1 /* EscaHandrailEnterVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A22134E46100A65EE1 /* EscaHandrailEnterVisitor.h */; };
		2B0791D32134E46100A65EE1 /* EscaHandrailGuidVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A32134E46100A65EE1 /* EscaHandrailGuidVisitor.cpp */; };
		2B0791D42134E46100A65EE1 /* EscaHandrailGuidVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A42134E46100A65EE1 /* EscaHandrailGuidVisitor.h */; };
		2B0791D52134E46100A65EE1 /* EscaHandrailLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A52134E46100A65EE1 /* EscaHandrailLightingVisitor.cpp */; };
		2B0791D62134E46100A65EE1 /* EscaHandrailLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A62134E46100A65EE1 /* EscaHandrailLightingVisitor.h */; };
		2B0791D72134E46100A65EE1 /* EscaHandrailVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A72134E46100A65EE1 /* EscaHandrailVisitor.cpp */; };
		2B0791D82134E46100A65EE1 /* EscaHandrailVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A82134E46100A65EE1 /* EscaHandrailVisitor.h */; };
		2B0791D92134E46100A65EE1 /* EscaPhotoelectricVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A92134E46100A65EE1 /* EscaPhotoelectricVisitor.cpp */; };
		2B0791DA2134E46100A65EE1 /* EscaPhotoelectricVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791AA2134E46100A65EE1 /* EscaPhotoelectricVisitor.h */; };
		2B0791DB2134E46100A65EE1 /* EscaScenesVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791AB2134E46100A65EE1 /* EscaScenesVisitor.cpp */; };
		2B0791DC2134E46100A65EE1 /* EscaScenesVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791AC2134E46100A65EE1 /* EscaScenesVisitor.h */; };
		2B0791DD2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791AD2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.cpp */; };
		2B0791DE2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791AE2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.h */; };
		2B0791DF2134E46100A65EE1 /* EscaSideCladdingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791AF2134E46100A65EE1 /* EscaSideCladdingVisitor.cpp */; };
		2B0791E02134E46100A65EE1 /* EscaSideCladdingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B02134E46100A65EE1 /* EscaSideCladdingVisitor.h */; };
		2B0791E12134E46100A65EE1 /* EscaSkirtBrushVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B12134E46100A65EE1 /* EscaSkirtBrushVisitor.cpp */; };
		2B0791E22134E46100A65EE1 /* EscaSkirtBrushVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B22134E46100A65EE1 /* EscaSkirtBrushVisitor.h */; };
		2B0791E32134E46100A65EE1 /* EscaSkirtLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B32134E46100A65EE1 /* EscaSkirtLightingVisitor.cpp */; };
		2B0791E42134E46100A65EE1 /* EscaSkirtLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B42134E46100A65EE1 /* EscaSkirtLightingVisitor.h */; };
		2B0791E52134E46100A65EE1 /* EscaSkirtVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B52134E46100A65EE1 /* EscaSkirtVisitor.cpp */; };
		2B0791E62134E46100A65EE1 /* EscaSkirtVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B62134E46100A65EE1 /* EscaSkirtVisitor.h */; };
		2B0791E72134E46100A65EE1 /* EscaStepLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B72134E46100A65EE1 /* EscaStepLightingVisitor.cpp */; };
		2B0791E82134E46100A65EE1 /* EscaStepLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B82134E46100A65EE1 /* EscaStepLightingVisitor.h */; };
		2B0791E92134E46100A65EE1 /* EscaStepVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B92134E46100A65EE1 /* EscaStepVisitor.cpp */; };
		2B0791EA2134E46100A65EE1 /* EscaStepVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791BA2134E46100A65EE1 /* EscaStepVisitor.h */; };
		2B0791EB2134E46100A65EE1 /* EscaTrafficLightVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791BB2134E46100A65EE1 /* EscaTrafficLightVisitor.cpp */; };
		2B0791EC2134E46100A65EE1 /* EscaTrafficLightVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791BC2134E46100A65EE1 /* EscaTrafficLightVisitor.h */; };
		2B0791ED2134E46100A65EE1 /* EscaTrussLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791BD2134E46100A65EE1 /* EscaTrussLightingVisitor.cpp */; };
		2B0791EE2134E46100A65EE1 /* EscaTrussLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791BE2134E46100A65EE1 /* EscaTrussLightingVisitor.h */; };
		2B0792422134E47D00A65EE1 /* Global_Camera_EffectParameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F02134E47D00A65EE1 /* Global_Camera_EffectParameter.cpp */; };
		2B0792432134E47D00A65EE1 /* Global_Camera_EffectParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F12134E47D00A65EE1 /* Global_Camera_EffectParameter.h */; };
		2B0792442134E47D00A65EE1 /* Global_Camera_Position.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F22134E47D00A65EE1 /* Global_Camera_Position.cpp */; };
		2B0792452134E47D00A65EE1 /* Global_Camera_Position.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F32134E47D00A65EE1 /* Global_Camera_Position.h */; };
		2B0792462134E47D00A65EE1 /* Global_Control_Flag.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F42134E47D00A65EE1 /* Global_Control_Flag.cpp */; };
		2B0792472134E47D00A65EE1 /* Global_Control_Flag.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F52134E47D00A65EE1 /* Global_Control_Flag.h */; };
		2B0792482134E47D00A65EE1 /* Global_CopyObject.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F62134E47D00A65EE1 /* Global_CopyObject.cpp */; };
		2B0792492134E47D00A65EE1 /* Global_CopyObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F72134E47D00A65EE1 /* Global_CopyObject.h */; };
		2B07924A2134E47D00A65EE1 /* Global_Esca_PartMark.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F82134E47D00A65EE1 /* Global_Esca_PartMark.cpp */; };
		2B07924B2134E47D00A65EE1 /* Global_Esca_PartMark.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F92134E47D00A65EE1 /* Global_Esca_PartMark.h */; };
		2B07924C2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791FA2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.cpp */; };
		2B07924D2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791FB2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.h */; };
		2B07924E2134E47D00A65EE1 /* Global_Escalators_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791FC2134E47D00A65EE1 /* Global_Escalators_Parameter.cpp */; };
		2B07924F2134E47D00A65EE1 /* Global_Escalators_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791FD2134E47D00A65EE1 /* Global_Escalators_Parameter.h */; };
		2B0792502134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791FE2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.cpp */; };
		2B0792512134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791FF2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.h */; };
		2B0792522134E47D00A65EE1 /* Global_PartType_Info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792002134E47D00A65EE1 /* Global_PartType_Info.cpp */; };
		2B0792532134E47D00A65EE1 /* Global_PartType_Info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792012134E47D00A65EE1 /* Global_PartType_Info.h */; };
		2B0792542134E47D00A65EE1 /* Global_Script_Floor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792022134E47D00A65EE1 /* Global_Script_Floor.cpp */; };
		2B0792552134E47D00A65EE1 /* Global_Script_Floor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792032134E47D00A65EE1 /* Global_Script_Floor.h */; };
		2B0792562134E47D00A65EE1 /* Global_Step0_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792042134E47D00A65EE1 /* Global_Step0_Parameter.cpp */; };
		2B0792572134E47D00A65EE1 /* Global_Step0_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792052134E47D00A65EE1 /* Global_Step0_Parameter.h */; };
		2B0792582134E47D00A65EE1 /* Global_Step_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792062134E47D00A65EE1 /* Global_Step_Parameter.cpp */; };
		2B0792592134E47D00A65EE1 /* Global_Step_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792072134E47D00A65EE1 /* Global_Step_Parameter.h */; };
		2B07925A2134E47D00A65EE1 /* Global_StepLight0_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792082134E47D00A65EE1 /* Global_StepLight0_Parameter.cpp */; };
		2B07925B2134E47D00A65EE1 /* Global_StepLight0_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792092134E47D00A65EE1 /* Global_StepLight0_Parameter.h */; };
		2B07925C2134E47D00A65EE1 /* Global_TestNote.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07920A2134E47D00A65EE1 /* Global_TestNote.cpp */; };
		2B07925D2134E47D00A65EE1 /* Global_TestNote.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07920B2134E47D00A65EE1 /* Global_TestNote.h */; };
		2B07925E2134E47D00A65EE1 /* Global_Work0_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07920C2134E47D00A65EE1 /* Global_Work0_Parameter.cpp */; };
		2B07925F2134E47D00A65EE1 /* Global_Work0_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07920D2134E47D00A65EE1 /* Global_Work0_Parameter.h */; };
		2B0792602134E47D00A65EE1 /* Global_Work1_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07920E2134E47D00A65EE1 /* Global_Work1_Parameter.cpp */; };
		2B0792612134E47D00A65EE1 /* Global_Work1_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07920F2134E47D00A65EE1 /* Global_Work1_Parameter.h */; };
		2B0792622134E47D00A65EE1 /* Sel_Esca_Access_Cover.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792102134E47D00A65EE1 /* Sel_Esca_Access_Cover.cpp */; };
		2B0792632134E47D00A65EE1 /* Sel_Esca_Access_Cover.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792112134E47D00A65EE1 /* Sel_Esca_Access_Cover.h */; };
		2B0792642134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792122134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.cpp */; };
		2B0792652134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792132134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.h */; };
		2B0792662134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792142134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.cpp */; };
		2B0792672134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792152134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.h */; };
		2B0792682134E47D00A65EE1 /* Sel_Esca_Background.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792162134E47D00A65EE1 /* Sel_Esca_Background.cpp */; };
		2B0792692134E47D00A65EE1 /* Sel_Esca_Background.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792172134E47D00A65EE1 /* Sel_Esca_Background.h */; };
		2B07926A2134E47D00A65EE1 /* Sel_Esca_Balustrade.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792182134E47D00A65EE1 /* Sel_Esca_Balustrade.cpp */; };
		2B07926B2134E47D00A65EE1 /* Sel_Esca_Balustrade.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792192134E47D00A65EE1 /* Sel_Esca_Balustrade.h */; };
		2B07926C2134E47D00A65EE1 /* Sel_Esca_Comb.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07921A2134E47D00A65EE1 /* Sel_Esca_Comb.cpp */; };
		2B07926D2134E47D00A65EE1 /* Sel_Esca_Comb.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07921B2134E47D00A65EE1 /* Sel_Esca_Comb.h */; };
		2B07926E2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07921C2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.cpp */; };
		2B07926F2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07921D2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.h */; };
		2B0792702134E47D00A65EE1 /* Sel_Esca_Decking.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07921E2134E47D00A65EE1 /* Sel_Esca_Decking.cpp */; };
		2B0792712134E47D00A65EE1 /* Sel_Esca_Decking.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07921F2134E47D00A65EE1 /* Sel_Esca_Decking.h */; };
		2B0792722134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792202134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.cpp */; };
		2B0792732134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792212134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.h */; };
		2B0792742134E47D00A65EE1 /* Sel_Esca_Handrail.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792222134E47D00A65EE1 /* Sel_Esca_Handrail.cpp */; };
		2B0792752134E47D00A65EE1 /* Sel_Esca_Handrail.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792232134E47D00A65EE1 /* Sel_Esca_Handrail.h */; };
		2B0792762134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792242134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.cpp */; };
		2B0792772134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792252134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.h */; };
		2B0792782134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792262134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.cpp */; };
		2B0792792134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792272134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.h */; };
		2B07927A2134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792282134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.cpp */; };
		2B07927B2134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792292134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.h */; };
		2B07927C2134E47D00A65EE1 /* Sel_Esca_Photoelectric.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07922A2134E47D00A65EE1 /* Sel_Esca_Photoelectric.cpp */; };
		2B07927D2134E47D00A65EE1 /* Sel_Esca_Photoelectric.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07922B2134E47D00A65EE1 /* Sel_Esca_Photoelectric.h */; };
		2B07927E2134E47D00A65EE1 /* Sel_Esca_Scene.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07922C2134E47D00A65EE1 /* Sel_Esca_Scene.cpp */; };
		2B07927F2134E47D00A65EE1 /* Sel_Esca_Scene.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07922D2134E47D00A65EE1 /* Sel_Esca_Scene.h */; };
		2B0792802134E47D00A65EE1 /* Sel_Esca_Side_Cladding.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07922E2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.cpp */; };
		2B0792812134E47D00A65EE1 /* Sel_Esca_Side_Cladding.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07922F2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.h */; };
		2B0792822134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792302134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.cpp */; };
		2B0792832134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792312134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.h */; };
		2B0792842134E47D00A65EE1 /* Sel_Esca_Skirt.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792322134E47D00A65EE1 /* Sel_Esca_Skirt.cpp */; };
		2B0792852134E47D00A65EE1 /* Sel_Esca_Skirt.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792332134E47D00A65EE1 /* Sel_Esca_Skirt.h */; };
		2B0792862134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792342134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.cpp */; };
		2B0792872134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792352134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.h */; };
		2B0792882134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792362134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.cpp */; };
		2B0792892134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792372134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.h */; };
		2B07928A2134E47D00A65EE1 /* Sel_Esca_Step.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792382134E47D00A65EE1 /* Sel_Esca_Step.cpp */; };
		2B07928B2134E47D00A65EE1 /* Sel_Esca_Step.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792392134E47D00A65EE1 /* Sel_Esca_Step.h */; };
		2B07928C2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07923A2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.cpp */; };
		2B07928D2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07923B2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.h */; };
		2B07928E2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07923C2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.cpp */; };
		2B07928F2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07923D2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.h */; };
		2B0792902134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07923E2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.cpp */; };
		2B0792912134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07923F2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.h */; };
		2B0792922134E47D00A65EE1 /* Sel_RGB_Share.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792402134E47D00A65EE1 /* Sel_RGB_Share.cpp */; };
		2B0792932134E47D00A65EE1 /* Sel_RGB_Share.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792412134E47D00A65EE1 /* Sel_RGB_Share.h */; };
		2B0792982134E51500A65EE1 /* EscalatorSize.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792942134E51400A65EE1 /* EscalatorSize.h */; };
		2B0792992134E51500A65EE1 /* EscalatorSize.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792952134E51400A65EE1 /* EscalatorSize.cpp */; };
		2B07929A2134E51500A65EE1 /* dvi_tinyxml2.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792962134E51400A65EE1 /* dvi_tinyxml2.cpp */; };
		2B07929B2134E51500A65EE1 /* dvi_tinyxml2.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792972134E51400A65EE1 /* dvi_tinyxml2.h */; };
		2B0792AB2134E5AF00A65EE1 /* esca_elevator_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07929D2134E5AE00A65EE1 /* esca_elevator_part.cpp */; };
		2B0792AC2134E5AF00A65EE1 /* esca_elevator_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07929E2134E5AE00A65EE1 /* esca_elevator_part.h */; };
		2B0792AD2134E5AF00A65EE1 /* esca_elevator_part_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07929F2134E5AE00A65EE1 /* esca_elevator_part_factory.cpp */; };
		2B0792AE2134E5AF00A65EE1 /* esca_elevator_part_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A02134E5AE00A65EE1 /* esca_elevator_part_factory.h */; };
		2B0792AF2134E5AF00A65EE1 /* EscaAcessOverFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A12134E5AE00A65EE1 /* EscaAcessOverFactory.cpp */; };
		2B0792B02134E5AF00A65EE1 /* EscaAcessOverFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A22134E5AE00A65EE1 /* EscaAcessOverFactory.h */; };
		2B0792B12134E5AF00A65EE1 /* EscaBalustradeFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A32134E5AE00A65EE1 /* EscaBalustradeFactory.cpp */; };
		2B0792B22134E5AF00A65EE1 /* EscaBalustradeFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A42134E5AE00A65EE1 /* EscaBalustradeFactory.h */; };
		2B0792B32134E5AF00A65EE1 /* EscaHandrailEnterFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A52134E5AE00A65EE1 /* EscaHandrailEnterFactory.cpp */; };
		2B0792B42134E5AF00A65EE1 /* EscaHandrailEnterFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A62134E5AE00A65EE1 /* EscaHandrailEnterFactory.h */; };
		2B0792B52134E5AF00A65EE1 /* EscaSideCladdingFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A72134E5AE00A65EE1 /* EscaSideCladdingFactory.cpp */; };
		2B0792B62134E5AF00A65EE1 /* EscaSideCladdingFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A82134E5AE00A65EE1 /* EscaSideCladdingFactory.h */; };
		2B0792B72134E5AF00A65EE1 /* EscaSkirtLightingFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A92134E5AE00A65EE1 /* EscaSkirtLightingFactory.cpp */; };
		2B0792B82134E5AF00A65EE1 /* EscaSkirtLightingFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792AA2134E5AE00A65EE1 /* EscaSkirtLightingFactory.h */; };
		2B4CAFA925B80B3B004B0767 /* BaseVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1E72064E70400DF2F76 /* BaseVisitor.cpp */; };
		2B4CAFAA25B80B3B004B0767 /* EscaBalustradeFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A32134E5AE00A65EE1 /* EscaBalustradeFactory.cpp */; };
		2B4CAFAB25B80B3B004B0767 /* VrFrontWallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1292064E58F00DF2F76 /* VrFrontWallVisitor.cpp */; };
		2B4CAFAC25B80B3B004B0767 /* car_top.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FC2064E70900DF2F76 /* car_top.cpp */; };
		2B4CAFAD25B80B3B004B0767 /* Sel_Bottom_Accessory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BBE12D2222F633D0003D940 /* Sel_Bottom_Accessory.cpp */; };
		2B4CAFAE25B80B3B004B0767 /* Sel_Esca_Comb_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07921C2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.cpp */; };
		2B4CAFAF25B80B3B004B0767 /* Sel_Esca_Photoelectric.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07922A2134E47D00A65EE1 /* Sel_Esca_Photoelectric.cpp */; };
		2B4CAFB025B80B3B004B0767 /* Sel_Esca_Balustrade.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792182134E47D00A65EE1 /* Sel_Esca_Balustrade.cpp */; };
		2B4CAFB125B80B3B004B0767 /* decoration_vr_interface_lib.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F62064E70800DF2F76 /* decoration_vr_interface_lib.cpp */; };
		2B4CAFB225B80B3B004B0767 /* EscaSkirtBrushVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B12134E46100A65EE1 /* EscaSkirtBrushVisitor.cpp */; };
		2B4CAFB325B80B3B004B0767 /* sha256.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E8C207466E200126085 /* sha256.cpp */; };
		2B4CAFB425B80B3B004B0767 /* Sel_Esca_Comb.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07921A2134E47D00A65EE1 /* Sel_Esca_Comb.cpp */; };
		2B4CAFB525B80B3B004B0767 /* Sel_Esca_Skirt_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792362134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.cpp */; };
		2B4CAFB625B80B3B004B0767 /* Sel_Esca_Access_Cover_Flag.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792142134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.cpp */; };
		2B4CAFB725B80B3B004B0767 /* svr_part_model_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E60207465D800126085 /* svr_part_model_info.cpp */; };
		2B4CAFB825B80B3B004B0767 /* EscaSideCladdingLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791AD2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.cpp */; };
		2B4CAFB925B80B3B004B0767 /* EscaStepLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B72134E46100A65EE1 /* EscaStepLightingVisitor.cpp */; };
		2B4CAFBA25B80B3B004B0767 /* Sel_LopLcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D10C2064E58F00DF2F76 /* Sel_LopLcd.cpp */; };
		2B4CAFBB25B80B3B004B0767 /* Global_Control_Flag.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F42134E47D00A65EE1 /* Global_Control_Flag.cpp */; };
		2B4CAFBC25B80B3B004B0767 /* Sel_Esca_Handrail_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792282134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.cpp */; };
		2B4CAFBD25B80B3B004B0767 /* Sel_Accessory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F22064E58F00DF2F76 /* Sel_Accessory.cpp */; };
		2B4CAFBE25B80B3B004B0767 /* BasePartOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1CD2064E6FE00DF2F76 /* BasePartOperator.cpp */; };
		2B4CAFBF25B80B3B004B0767 /* Sel_Hall.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0FC2064E58F00DF2F76 /* Sel_Hall.cpp */; };
		2B4CAFC025B80B3B004B0767 /* Global_TestNote.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07920A2134E47D00A65EE1 /* Global_TestNote.cpp */; };
		2B4CAFC125B80B3B004B0767 /* VrLanternVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D13B2064E58F00DF2F76 /* VrLanternVisitor.cpp */; };
		2B4CAFC225B80B3B004B0767 /* EscaBalustradeVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791952134E46000A65EE1 /* EscaBalustradeVisitor.cpp */; };
		2B4CAFC325B80B3B004B0767 /* VrCarDoorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D11B2064E58F00DF2F76 /* VrCarDoorVisitor.cpp */; };
		2B4CAFC425B80B3B004B0767 /* VrChangeArg.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1212064E58F00DF2F76 /* VrChangeArg.cpp */; };
		2B4CAFC525B80B3B004B0767 /* ElectricOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C02064E6FB00DF2F76 /* ElectricOperator.cpp */; };
		2B4CAFC625B80B3B004B0767 /* Sel_Esca_Access_Cover_ExpendType.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792122134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.cpp */; };
		2B4CAFC725B80B3B004B0767 /* EscaAcessOverExpendTypeVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791912134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.cpp */; };
		2B4CAFC825B80B3B004B0767 /* VrLanternDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1392064E58F00DF2F76 /* VrLanternDisplayVisitor.cpp */; };
		2B4CAFC925B80B3B004B0767 /* VrCarConfigVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1192064E58F00DF2F76 /* VrCarConfigVisitor.cpp */; };
		2B4CAFCA25B80B3B004B0767 /* EscaHandrailEnterVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A12134E46100A65EE1 /* EscaHandrailEnterVisitor.cpp */; };
		2B4CAFCB25B80B3B004B0767 /* skirting_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DE2064E70200DF2F76 /* skirting_part.cpp */; };
		2B4CAFCC25B80B3B004B0767 /* Sel_Cop.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F82064E58F00DF2F76 /* Sel_Cop.cpp */; };
		2B4CAFCD25B80B3B004B0767 /* Global_Loading_Scence.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E42064E58F00DF2F76 /* Global_Loading_Scence.cpp */; };
		2B4CAFCE25B80B3B004B0767 /* VrCopDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1252064E58F00DF2F76 /* VrCopDisplayVisitor.cpp */; };
		2B4CAFCF25B80B3B004B0767 /* EscaHandrailLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A52134E46100A65EE1 /* EscaHandrailLightingVisitor.cpp */; };
		2B4CAFD025B80B3B004B0767 /* EscaHandrailEnterFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A52134E5AE00A65EE1 /* EscaHandrailEnterFactory.cpp */; };
		2B4CAFD125B80B3B004B0767 /* electric_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DD2064E70200DF2F76 /* electric_part.cpp */; };
		2B4CAFD225B80B3B004B0767 /* VrHallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC7B23C57A3500682713 /* VrHallVisitor.cpp */; };
		2B4CAFD325B80B3B004B0767 /* common_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D52064E70000DF2F76 /* common_part.cpp */; };
		2B4CAFD425B80B3B004B0767 /* json_reader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0852064E4E800DF2F76 /* json_reader.cpp */; };
		2B4CAFD525B80B3B004B0767 /* Sel_Esca_Background.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792162134E47D00A65EE1 /* Sel_Esca_Background.cpp */; };
		2B4CAFD625B80B3B004B0767 /* VrAccessoryVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1152064E58F00DF2F76 /* VrAccessoryVisitor.cpp */; };
		2B4CAFD725B80B3B004B0767 /* esca_elevator_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07929D2134E5AE00A65EE1 /* esca_elevator_part.cpp */; };
		2B4CAFD825B80B3B004B0767 /* EscaSkirtVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B52134E46100A65EE1 /* EscaSkirtVisitor.cpp */; };
		2B4CAFD925B80B3B004B0767 /* Sel_Esca_Skirt.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792322134E47D00A65EE1 /* Sel_Esca_Skirt.cpp */; };
		2B4CAFDA25B80B3B004B0767 /* Sel_Esca_Access_Cover.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792102134E47D00A65EE1 /* Sel_Esca_Access_Cover.cpp */; };
		2B4CAFDB25B80B3B004B0767 /* common_part_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2002064E70A00DF2F76 /* common_part_factory.cpp */; };
		2B4CAFDC25B80B3B004B0767 /* Sel_Esca_Decking.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07921E2134E47D00A65EE1 /* Sel_Esca_Decking.cpp */; };
		2B4CAFDD25B80B3B004B0767 /* Global_Setting_Car.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E82064E58F00DF2F76 /* Global_Setting_Car.cpp */; };
		2B4CAFDE25B80B3B004B0767 /* Sel_Ceiling.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F62064E58F00DF2F76 /* Sel_Ceiling.cpp */; };
		2B4CAFDF25B80B3B004B0767 /* download_assist.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BED8C0B228BE75400AEF4D0 /* download_assist.cpp */; };
		2B4CAFE025B80B3B004B0767 /* Sel_Esca_Side_Cladding_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792302134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.cpp */; };
		2B4CAFE125B80B3B004B0767 /* svr_car_wall.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E51207465D600126085 /* svr_car_wall.cpp */; };
		2B4CAFE225B80B3B004B0767 /* stdafx.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C82064E6FD00DF2F76 /* stdafx.cpp */; };
		2B4CAFE325B80B3B004B0767 /* EscaAcessOverFlagVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791932134E46000A65EE1 /* EscaAcessOverFlagVisitor.cpp */; };
		2B4CAFE425B80B3B004B0767 /* EscaAcessOverFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A12134E5AE00A65EE1 /* EscaAcessOverFactory.cpp */; };
		2B4CAFE525B80B3B004B0767 /* svr_config.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E59207465D700126085 /* svr_config.cpp */; };
		2B4CAFE625B80B3B004B0767 /* GlobalInfoDataCommon.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D62064E70000DF2F76 /* GlobalInfoDataCommon.cpp */; };
		2B4CAFE725B80B3B004B0767 /* VrCarIndicatorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D11D2064E58F00DF2F76 /* VrCarIndicatorVisitor.cpp */; };
		2B4CAFE825B80B3B004B0767 /* config_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D12064E6FF00DF2F76 /* config_part.cpp */; };
		2B4CAFE925B80B3B004B0767 /* VrJambVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1372064E58F00DF2F76 /* VrJambVisitor.cpp */; };
		2B4CAFEA25B80B3B004B0767 /* FrontWallTypeOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DA2064E70100DF2F76 /* FrontWallTypeOperator.cpp */; };
		2B4CAFEB25B80B3B004B0767 /* Global_Escalators_Rung_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791FE2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.cpp */; };
		2B4CAFEC25B80B3B004B0767 /* Sel_Esca_Decking_Ctrl_Box.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792202134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.cpp */; };
		2B4CAFED25B80B3B004B0767 /* Sel_HLLcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1062064E58F00DF2F76 /* Sel_HLLcd.cpp */; };
		2B4CAFEE25B80B3B004B0767 /* arg_cache.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1BE2064E6FB00DF2F76 /* arg_cache.cpp */; };
		2B4CAFEF25B80B3B004B0767 /* EscaPhotoelectricVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A92134E46100A65EE1 /* EscaPhotoelectricVisitor.cpp */; };
		2B4CAFF025B80B3B004B0767 /* Sel_Esca_Skirt_Brush.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792342134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.cpp */; };
		2B4CAFF125B80B3B004B0767 /* db_visitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F42064E70700DF2F76 /* db_visitor.cpp */; };
		2B4CAFF225B80B3B004B0767 /* car_top_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FB2064E70900DF2F76 /* car_top_factory.cpp */; };
		2B4CAFF325B80B3B004B0767 /* Global_Setting_Hall.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0EA2064E58F00DF2F76 /* Global_Setting_Hall.cpp */; };
		2B4CAFF425B80B3B004B0767 /* VrConfigInfo.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1232064E58F00DF2F76 /* VrConfigInfo.cpp */; };
		2B4CAFF525B80B3B004B0767 /* Sel_Jamb.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1082064E58F00DF2F76 /* Sel_Jamb.cpp */; };
		2B4CAFF625B80B3B004B0767 /* car_wall_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F02064E70600DF2F76 /* car_wall_part.cpp */; };
		2B4CAFF725B80B3B004B0767 /* VrLopDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D13D2064E58F00DF2F76 /* VrLopDisplayVisitor.cpp */; };
		2B4CAFF825B80B3B004B0767 /* Global_StepLight0_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792082134E47D00A65EE1 /* Global_StepLight0_Parameter.cpp */; };
		2B4CAFF925B80B3B004B0767 /* Sel_Lop.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D10A2064E58F00DF2F76 /* Sel_Lop.cpp */; };
		2B4CAFFA25B80B3B004B0767 /* EscaConfigVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07919B2134E46100A65EE1 /* EscaConfigVisitor.cpp */; };
		2B4CAFFB25B80B3B004B0767 /* Sel_Esca_Truss_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07923E2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.cpp */; };
		2B4CAFFC25B80B3B004B0767 /* Sel_Mirror.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D10E2064E58F00DF2F76 /* Sel_Mirror.cpp */; };
		2B4CAFFD25B80B3B004B0767 /* ConfigOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1EB2064E70500DF2F76 /* ConfigOperator.cpp */; };
		2B4CAFFE25B80B3B004B0767 /* EscaHandrailGuidVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A32134E46100A65EE1 /* EscaHandrailGuidVisitor.cpp */; };
		2B4CAFFF25B80B3B004B0767 /* hall_room_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC8423C57A7400682713 /* hall_room_part.cpp */; };
		2B4CB00025B80B3B004B0767 /* VrTopVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1452064E58F00DF2F76 /* VrTopVisitor.cpp */; };
		2B4CB00125B80B3B004B0767 /* VrHallShaftVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC7823C57A3400682713 /* VrHallShaftVisitor.cpp */; };
		2B4CB00225B80B3B004B0767 /* EscaSkirtLightingFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A92134E5AE00A65EE1 /* EscaSkirtLightingFactory.cpp */; };
		2B4CB00325B80B3B004B0767 /* Global_CopyObject.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F62134E47D00A65EE1 /* Global_CopyObject.cpp */; };
		2B4CB00425B80B3B004B0767 /* EscaCombLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791972134E46100A65EE1 /* EscaCombLightingVisitor.cpp */; };
		2B4CB00525B80B3B004B0767 /* decoration_vr_array_visitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C92064E6FD00DF2F76 /* decoration_vr_array_visitor.cpp */; };
		2B4CB00625B80B3B004B0767 /* EscaSkirtLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B32134E46100A65EE1 /* EscaSkirtLightingVisitor.cpp */; };
		2B4CB00725B80B3B004B0767 /* ElevatorSize.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1BA2064E6FA00DF2F76 /* ElevatorSize.cpp */; };
		2B4CB00825B80B3B004B0767 /* Sel_HI.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1002064E58F00DF2F76 /* Sel_HI.cpp */; };
		2B4CB00925B80B3B004B0767 /* svr_json_helper.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E55207465D600126085 /* svr_json_helper.cpp */; };
		2B4CB00A25B80B3B004B0767 /* vr_visitor_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1132064E58F00DF2F76 /* vr_visitor_factory.cpp */; };
		2B4CB00B25B80B3B004B0767 /* svr_part_basic_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E5E207465D800126085 /* svr_part_basic_info.cpp */; };
		2B4CB00C25B80B3B004B0767 /* svr_wall_size_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E56207465D700126085 /* svr_wall_size_info.cpp */; };
		2B4CB00D25B80B3B004B0767 /* dvi_tinyxml2.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792962134E51400A65EE1 /* dvi_tinyxml2.cpp */; };
		2B4CB00E25B80B3B004B0767 /* PartTypeManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2032064E70B00DF2F76 /* PartTypeManager.cpp */; };
		2B4CB00F25B80B3B004B0767 /* file_pool.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C62064E6FC00DF2F76 /* file_pool.cpp */; };
		2B4CB01025B80B3B004B0767 /* ElevatorPartFactoryManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DC2064E70200DF2F76 /* ElevatorPartFactoryManager.cpp */; };
		2B4CB01125B80B3B004B0767 /* Sel_Esca_Side_Cladding.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07922E2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.cpp */; };
		2B4CB01225B80B3B004B0767 /* Global_Esca_PartMark.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F82134E47D00A65EE1 /* Global_Esca_PartMark.cpp */; };
		2B4CB01325B80B3B004B0767 /* EscaSideCladdingFactory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792A72134E5AE00A65EE1 /* EscaSideCladdingFactory.cpp */; };
		2B4CB01425B80B3B004B0767 /* electric_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1B82064E6FA00DF2F76 /* electric_factory.cpp */; };
		2B4CB01525B80B3B004B0767 /* EscaCombVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791992134E46100A65EE1 /* EscaCombVisitor.cpp */; };
		2B4CB01625B80B3B004B0767 /* Sel_Door_Imported.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6523C57A1600682713 /* Sel_Door_Imported.cpp */; };
		2B4CB01725B80B3B004B0767 /* svr_common_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E4E207465D500126085 /* svr_common_part.cpp */; };
		2B4CB01825B80B3B004B0767 /* VrCopVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1272064E58F00DF2F76 /* VrCopVisitor.cpp */; };
		2B4CB01925B80B3B004B0767 /* VrMirrorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1412064E58F00DF2F76 /* VrMirrorVisitor.cpp */; };
		2B4CB01A25B80B3B004B0767 /* Global_Escalators_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791FC2134E47D00A65EE1 /* Global_Escalators_Parameter.cpp */; };
		2B4CB01B25B80B3B004B0767 /* decoration_vr_interface.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1CF2064E6FE00DF2F76 /* decoration_vr_interface.cpp */; };
		2B4CB01C25B80B3B004B0767 /* json_value.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0872064E4E800DF2F76 /* json_value.cpp */; };
		2B4CB01D25B80B3B004B0767 /* vr_controller.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1112064E58F00DF2F76 /* vr_controller.cpp */; };
		2B4CB01E25B80B3B004B0767 /* VrLopVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D13F2064E58F00DF2F76 /* VrLopVisitor.cpp */; };
		2B4CB01F25B80B3B004B0767 /* Util.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2072064E70C00DF2F76 /* Util.cpp */; };
		2B4CB02025B80B3B004B0767 /* Sel_HandRail.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0FE2064E58F00DF2F76 /* Sel_HandRail.cpp */; };
		2B4CB02125B80B3B004B0767 /* PartOperatorManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2012064E70A00DF2F76 /* PartOperatorManager.cpp */; };
		2B4CB02225B80B3B004B0767 /* VrSightseeingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1432064E58F00DF2F76 /* VrSightseeingVisitor.cpp */; };
		2B4CB02325B80B3B004B0767 /* svr_part_material.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E69207465DA00126085 /* svr_part_material.cpp */; };
		2B4CB02425B80B3B004B0767 /* Global_Work0_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07920C2134E47D00A65EE1 /* Global_Work0_Parameter.cpp */; };
		2B4CB02525B80B3B004B0767 /* VrGlobalInfo.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D12B2064E58F00DF2F76 /* VrGlobalInfo.cpp */; };
		2B4CB02625B80B3B004B0767 /* Sel_CopLcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0FA2064E58F00DF2F76 /* Sel_CopLcd.cpp */; };
		2B4CB02725B80B3B004B0767 /* EscaStepVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791B92134E46100A65EE1 /* EscaStepVisitor.cpp */; };
		2B4CB02825B80B3B004B0767 /* download_visitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F72064E70800DF2F76 /* download_visitor.cpp */; };
		2B4CB02925B80B3B004B0767 /* Sel_Shaft.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6823C57A1700682713 /* Sel_Shaft.cpp */; };
		2B4CB02A25B80B3B004B0767 /* Sel_RGB_Share.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792402134E47D00A65EE1 /* Sel_RGB_Share.cpp */; };
		2B4CB02B25B80B3B004B0767 /* Sel_HILcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1022064E58F00DF2F76 /* Sel_HILcd.cpp */; };
		2B4CB02C25B80B3B004B0767 /* Global_PartType_Info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792002134E47D00A65EE1 /* Global_PartType_Info.cpp */; };
		2B4CB02D25B80B3B004B0767 /* ModelCarWallElem.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F02064E58F00DF2F76 /* ModelCarWallElem.cpp */; };
		2B4CB02E25B80B3B004B0767 /* svr_file_digital_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E53207465D600126085 /* svr_file_digital_info.cpp */; };
		2B4CB02F25B80B3B004B0767 /* EscaSideCladdingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791AF2134E46100A65EE1 /* EscaSideCladdingVisitor.cpp */; };
		2B4CB03025B80B3B004B0767 /* svr_material.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E57207465D700126085 /* svr_material.cpp */; };
		2B4CB03125B80B3B004B0767 /* Global_Step_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792062134E47D00A65EE1 /* Global_Step_Parameter.cpp */; };
		2B4CB03225B80B3B004B0767 /* Sel_Car_Shell.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6D23C57A1700682713 /* Sel_Car_Shell.cpp */; };
		2B4CB03325B80B3B004B0767 /* json_writer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0892064E4E800DF2F76 /* json_writer.cpp */; };
		2B4CB03425B80B3B004B0767 /* ConfigAnalyzer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F32064E70700DF2F76 /* ConfigAnalyzer.cpp */; };
		2B4CB03525B80B3B004B0767 /* Sel_Esca_Scene.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07922C2134E47D00A65EE1 /* Sel_Esca_Scene.cpp */; };
		2B4CB03625B80B3B004B0767 /* ConstValueMap.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C72064E6FD00DF2F76 /* ConstValueMap.cpp */; };
		2B4CB03725B80B3B004B0767 /* svr_wall_element.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E67207465DA00126085 /* svr_wall_element.cpp */; };
		2B4CB03825B80B3B004B0767 /* VrHallIndicatorDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1312064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.cpp */; };
		2B4CB03925B80B3B004B0767 /* material_channel_pool.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FF2064E70A00DF2F76 /* material_channel_pool.cpp */; };
		2B4CB03A25B80B3B004B0767 /* Sel_Esca_Handrail.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792222134E47D00A65EE1 /* Sel_Esca_Handrail.cpp */; };
		2B4CB03B25B80B3B004B0767 /* Global_Esca_Setting_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791FA2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.cpp */; };
		2B4CB03C25B80B3B004B0767 /* EscalatorSize.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792952134E51400A65EE1 /* EscalatorSize.cpp */; };
		2B4CB03D25B80B3B004B0767 /* EscaScenesVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791AB2134E46100A65EE1 /* EscaScenesVisitor.cpp */; };
		2B4CB03E25B80B3B004B0767 /* svr_material_channel.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E4F207465D500126085 /* svr_material_channel.cpp */; };
		2B4CB03F25B80B3B004B0767 /* skirting_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D32064E6FF00DF2F76 /* skirting_factory.cpp */; };
		2B4CB04025B80B3B004B0767 /* config_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1BD2064E6FB00DF2F76 /* config_factory.cpp */; };
		2B4CB04125B80B3B004B0767 /* VrCarWallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D11F2064E58F00DF2F76 /* VrCarWallVisitor.cpp */; };
		2B4CB04225B80B3B004B0767 /* svr_material_special_rule.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E62207465D900126085 /* svr_material_special_rule.cpp */; };
		2B4CB04325B80B3B004B0767 /* Global_Setting_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0EC2064E58F00DF2F76 /* Global_Setting_Parameter.cpp */; };
		2B4CB04425B80B3B004B0767 /* VrHallConfigVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D12D2064E58F00DF2F76 /* VrHallConfigVisitor.cpp */; };
		2B4CB04525B80B3B004B0767 /* Global_Camera_Position.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F22134E47D00A65EE1 /* Global_Camera_Position.cpp */; };
		2B4CB04625B80B3B004B0767 /* EscaTrussLightingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791BD2134E46100A65EE1 /* EscaTrussLightingVisitor.cpp */; };
		2B4CB04725B80B3B004B0767 /* Global_Step0_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792042134E47D00A65EE1 /* Global_Step0_Parameter.cpp */; };
		2B4CB04825B80B3B004B0767 /* VrHandrailVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1352064E58F00DF2F76 /* VrHandrailVisitor.cpp */; };
		2B4CB04925B80B3B004B0767 /* Sel_HallDoor_Imported.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6623C57A1600682713 /* Sel_HallDoor_Imported.cpp */; };
		2B4CB04A25B80B3B004B0767 /* EscaTrafficLightVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791BB2134E46100A65EE1 /* EscaTrafficLightVisitor.cpp */; };
		2B4CB04B25B80B3B004B0767 /* Global_Select.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E62064E58F00DF2F76 /* Global_Select.cpp */; };
		2B4CB04C25B80B3B004B0767 /* Global_Camera_EffectParameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791F02134E47D00A65EE1 /* Global_Camera_EffectParameter.cpp */; };
		2B4CB04D25B80B3B004B0767 /* Sel_Esca_Handrail_Enter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792242134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.cpp */; };
		2B4CB04E25B80B3B004B0767 /* Sel_HL.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1042064E58F00DF2F76 /* Sel_HL.cpp */; };
		2B4CB04F25B80B3B004B0767 /* VrHallDoorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D12F2064E58F00DF2F76 /* VrHallDoorVisitor.cpp */; };
		2B4CB05025B80B3B004B0767 /* hall_room_part_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC8623C57A7400682713 /* hall_room_part_factory.cpp */; };
		2B4CB05125B80B3B004B0767 /* Sel_CarIndicator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F42064E58F00DF2F76 /* Sel_CarIndicator.cpp */; };
		2B4CB05225B80B3B004B0767 /* Global_Work1_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07920E2134E47D00A65EE1 /* Global_Work1_Parameter.cpp */; };
		2B4CB05325B80B3B004B0767 /* material_channel.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0EE2064E58F00DF2F76 /* material_channel.cpp */; };
		2B4CB05425B80B3B004B0767 /* VrHallIndicatorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1332064E58F00DF2F76 /* VrHallIndicatorVisitor.cpp */; };
		2B4CB05525B80B3B004B0767 /* ElevatorConfig.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D82064E70000DF2F76 /* ElevatorConfig.cpp */; };
		2B4CB05625B80B3B004B0767 /* EscaHandrailVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0791A72134E46100A65EE1 /* EscaHandrailVisitor.cpp */; };
		2B4CB05725B80B3B004B0767 /* DGBaseVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E02064E58F00DF2F76 /* DGBaseVisitor.cpp */; };
		2B4CB05825B80B3B004B0767 /* Sel_Esca_Step_Lighting.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07923A2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.cpp */; };
		2B4CB05925B80B3B004B0767 /* car_wall_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FD2064E70900DF2F76 /* car_wall_factory.cpp */; };
		2B4CB05A25B80B3B004B0767 /* Sel_Esca_Handrail_Guid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792262134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.cpp */; };
		2B4CB05B25B80B3B004B0767 /* lru_cache.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E8D207466E200126085 /* lru_cache.cpp */; };
		2B4CB05C25B80B3B004B0767 /* Global_Script_Floor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792022134E47D00A65EE1 /* Global_Script_Floor.cpp */; };
		2B4CB05D25B80B3B004B0767 /* EscaDeckingCtrlBoxVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07919D2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.cpp */; };
		2B4CB05E25B80B3B004B0767 /* ElevatorPartOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2052064E70B00DF2F76 /* ElevatorPartOperator.cpp */; };
		2B4CB05F25B80B3B004B0767 /* HttpAsyncManage.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0A42064E54C00DF2F76 /* HttpAsyncManage.cpp */; };
		2B4CB06025B80B3B004B0767 /* VrBottomVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1172064E58F00DF2F76 /* VrBottomVisitor.cpp */; };
		2B4CB06125B80B3B004B0767 /* elevator_size_parser.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1E92064E70500DF2F76 /* elevator_size_parser.cpp */; };
		2B4CB06225B80B3B004B0767 /* Global_Hwndmsg.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E22064E58F00DF2F76 /* Global_Hwndmsg.cpp */; };
		2B4CB06325B80B3B004B0767 /* Sel_Esca_Traffic_Light.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07923C2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.cpp */; };
		2B4CB06425B80B3B004B0767 /* EscaAccessOverVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07918F2134E46000A65EE1 /* EscaAccessOverVisitor.cpp */; };
		2B4CB06525B80B3B004B0767 /* EscaDeckingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07919F2134E46100A65EE1 /* EscaDeckingVisitor.cpp */; };
		2B4CB06625B80B3B004B0767 /* Sel_Esca_Step.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B0792382134E47D00A65EE1 /* Sel_Esca_Step.cpp */; };
		2B4CB06725B80B3B004B0767 /* esca_elevator_part_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B07929F2134E5AE00A65EE1 /* esca_elevator_part_factory.cpp */; };
		2B4CB06825B80B3B004B0767 /* ElevatorConfigManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1E12064E70300DF2F76 /* ElevatorConfigManager.cpp */; };
		2B4CB06925B80B3B004B0767 /* VrCarShellVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC7723C57A3400682713 /* VrCarShellVisitor.cpp */; };
		2B4CB06D25B80B3B004B0767 /* VrLanternDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D13A2064E58F00DF2F76 /* VrLanternDisplayVisitor.h */; };
		2B4CB06E25B80B3B004B0767 /* VrLanternVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D13C2064E58F00DF2F76 /* VrLanternVisitor.h */; };
		2B4CB06F25B80B3B004B0767 /* ModelCarWallElem.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F12064E58F00DF2F76 /* ModelCarWallElem.h */; };
		2B4CB07025B80B3B004B0767 /* GlobalInfoDataCommon.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E52064E70400DF2F76 /* GlobalInfoDataCommon.h */; };
		2B4CB07125B80B3B004B0767 /* Sel_Esca_Skirt.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792332134E47D00A65EE1 /* Sel_Esca_Skirt.h */; };
		2B4CB07225B80B3B004B0767 /* Sel_CopLcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0FB2064E58F00DF2F76 /* Sel_CopLcd.h */; };
		2B4CB07325B80B3B004B0767 /* EscaHandrailEnterVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A22134E46100A65EE1 /* EscaHandrailEnterVisitor.h */; };
		2B4CB07425B80B3B004B0767 /* VrCopVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1282064E58F00DF2F76 /* VrCopVisitor.h */; };
		2B4CB07525B80B3B004B0767 /* skirting_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D02064E6FF00DF2F76 /* skirting_factory.h */; };
		2B4CB07625B80B3B004B0767 /* VrMirrorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1422064E58F00DF2F76 /* VrMirrorVisitor.h */; };
		2B4CB07725B80B3B004B0767 /* VrLopVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1402064E58F00DF2F76 /* VrLopVisitor.h */; };
		2B4CB07825B80B3B004B0767 /* EscaHandrailGuidVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A42134E46100A65EE1 /* EscaHandrailGuidVisitor.h */; };
		2B4CB07925B80B3B004B0767 /* VrJambVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1382064E58F00DF2F76 /* VrJambVisitor.h */; };
		2B4CB07A25B80B3B004B0767 /* EscaTrussLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791BE2134E46100A65EE1 /* EscaTrussLightingVisitor.h */; };
		2B4CB07B25B80B3B004B0767 /* Sel_Esca_Background.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792172134E47D00A65EE1 /* Sel_Esca_Background.h */; };
		2B4CB07C25B80B3B004B0767 /* EscaConfigVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07919C2134E46100A65EE1 /* EscaConfigVisitor.h */; };
		2B4CB07D25B80B3B004B0767 /* esca_elevator_part_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A02134E5AE00A65EE1 /* esca_elevator_part_factory.h */; };
		2B4CB07E25B80B3B004B0767 /* Sel_Esca_Balustrade.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792192134E47D00A65EE1 /* Sel_Esca_Balustrade.h */; };
		2B4CB07F25B80B3B004B0767 /* svr_wall_element.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E64207465D900126085 /* svr_wall_element.h */; };
		2B4CB08025B80B3B004B0767 /* EscaDeckingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A02134E46100A65EE1 /* EscaDeckingVisitor.h */; };
		2B4CB08125B80B3B004B0767 /* stdafx.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EF2064E70600DF2F76 /* stdafx.h */; };
		2B4CB08225B80B3B004B0767 /* VrLopDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D13E2064E58F00DF2F76 /* VrLopDisplayVisitor.h */; };
		2B4CB08325B80B3B004B0767 /* Sel_Esca_Handrail_Enter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792252134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.h */; };
		2B4CB08425B80B3B004B0767 /* EscaPhotoelectricVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791AA2134E46100A65EE1 /* EscaPhotoelectricVisitor.h */; };
		2B4CB08525B80B3B004B0767 /* Util.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2082064E70C00DF2F76 /* Util.h */; };
		2B4CB08625B80B3B004B0767 /* db_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F92064E70800DF2F76 /* db_visitor.h */; };
		2B4CB08725B80B3B004B0767 /* ConstValueMap.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E02064E70200DF2F76 /* ConstValueMap.h */; };
		2B4CB08825B80B3B004B0767 /* Sel_HILcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1032064E58F00DF2F76 /* Sel_HILcd.h */; };
		2B4CB08925B80B3B004B0767 /* svr_part_model_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E61207465D800126085 /* svr_part_model_info.h */; };
		2B4CB08A25B80B3B004B0767 /* Sel_Hall.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0FD2064E58F00DF2F76 /* Sel_Hall.h */; };
		2B4CB08B25B80B3B004B0767 /* Sel_Cop.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F92064E58F00DF2F76 /* Sel_Cop.h */; };
		2B4CB08C25B80B3B004B0767 /* skirting_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1B62064E6F900DF2F76 /* skirting_part.h */; };
		2B4CB08D25B80B3B004B0767 /* download_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CC2064E6FE00DF2F76 /* download_visitor.h */; };
		2B4CB08E25B80B3B004B0767 /* Sel_RGB_Share.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792412134E47D00A65EE1 /* Sel_RGB_Share.h */; };
		2B4CB08F25B80B3B004B0767 /* IElevatorPart.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CB2064E6FD00DF2F76 /* IElevatorPart.h */; };
		2B4CB09025B80B3B004B0767 /* Sel_Door_Imported.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6B23C57A1700682713 /* Sel_Door_Imported.h */; };
		2B4CB09125B80B3B004B0767 /* assertions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0742064E4E800DF2F76 /* assertions.h */; };
		2B4CB09225B80B3B004B0767 /* Sel_Mirror.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D10F2064E58F00DF2F76 /* Sel_Mirror.h */; };
		2B4CB09325B80B3B004B0767 /* EscaCombLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791982134E46100A65EE1 /* EscaCombLightingVisitor.h */; };
		2B4CB09425B80B3B004B0767 /* common_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2092064E70C00DF2F76 /* common_part.h */; };
		2B4CB09525B80B3B004B0767 /* hall_room_part_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC8323C57A7400682713 /* hall_room_part_factory.h */; };
		2B4CB09625B80B3B004B0767 /* autolink.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0752064E4E800DF2F76 /* autolink.h */; };
		2B4CB09725B80B3B004B0767 /* Global_Setting_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0ED2064E58F00DF2F76 /* Global_Setting_Parameter.h */; };
		2B4CB09825B80B3B004B0767 /* ElevatorPartOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20B2064E70D00DF2F76 /* ElevatorPartOperator.h */; };
		2B4CB09925B80B3B004B0767 /* EscaSkirtBrushVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B22134E46100A65EE1 /* EscaSkirtBrushVisitor.h */; };
		2B4CB09A25B80B3B004B0767 /* VrSightseeingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1442064E58F00DF2F76 /* VrSightseeingVisitor.h */; };
		2B4CB09B25B80B3B004B0767 /* json.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0792064E4E800DF2F76 /* json.h */; };
		2B4CB09C25B80B3B004B0767 /* Global_Escalators_Rung_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791FF2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.h */; };
		2B4CB09D25B80B3B004B0767 /* i_decoration_vr_interface.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0642064E4CA00DF2F76 /* i_decoration_vr_interface.h */; };
		2B4CB09E25B80B3B004B0767 /* json_tool.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0862064E4E800DF2F76 /* json_tool.h */; };
		2B4CB09F25B80B3B004B0767 /* VrHallIndicatorDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1322064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.h */; };
		2B4CB0A025B80B3B004B0767 /* Sel_Bottom_Accessory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6A23C57A1700682713 /* Sel_Bottom_Accessory.h */; };
		2B4CB0A125B80B3B004B0767 /* Sel_Esca_Photoelectric.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07922B2134E47D00A65EE1 /* Sel_Esca_Photoelectric.h */; };
		2B4CB0A225B80B3B004B0767 /* esca_elevator_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07929E2134E5AE00A65EE1 /* esca_elevator_part.h */; };
		2B4CB0A325B80B3B004B0767 /* Global_Script_Floor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792032134E47D00A65EE1 /* Global_Script_Floor.h */; };
		2B4CB0A425B80B3B004B0767 /* EscaSideCladdingFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A82134E5AE00A65EE1 /* EscaSideCladdingFactory.h */; };
		2B4CB0A525B80B3B004B0767 /* VrCarConfigVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D11A2064E58F00DF2F76 /* VrCarConfigVisitor.h */; };
		2B4CB0A625B80B3B004B0767 /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07C2064E4E800DF2F76 /* version.h */; };
		2B4CB0A725B80B3B004B0767 /* EscaAcessOverExpendTypeVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791922134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.h */; };
		2B4CB0A825B80B3B004B0767 /* VrCarWallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1202064E58F00DF2F76 /* VrCarWallVisitor.h */; };
		2B4CB0A925B80B3B004B0767 /* EscaSkirtLightingFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792AA2134E5AE00A65EE1 /* EscaSkirtLightingFactory.h */; };
		2B4CB0AA25B80B3B004B0767 /* IDownloadVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C22064E6FC00DF2F76 /* IDownloadVisitor.h */; };
		2B4CB0AB25B80B3B004B0767 /* Sel_Car_Shell.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6C23C57A1700682713 /* Sel_Car_Shell.h */; };
		2B4CB0AC25B80B3B004B0767 /* svr_part_material.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5F207465D800126085 /* svr_part_material.h */; };
		2B4CB0AD25B80B3B004B0767 /* elevator_size_parser.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1FE2064E70A00DF2F76 /* elevator_size_parser.h */; };
		2B4CB0AE25B80B3B004B0767 /* VrCarIndicatorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D11E2064E58F00DF2F76 /* VrCarIndicatorVisitor.h */; };
		2B4CB0AF25B80B3B004B0767 /* VrFrontWallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D12A2064E58F00DF2F76 /* VrFrontWallVisitor.h */; };
		2B4CB0B025B80B3B004B0767 /* Global_Camera_Position.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F32134E47D00A65EE1 /* Global_Camera_Position.h */; };
		2B4CB0B125B80B3B004B0767 /* EscaDeckingCtrlBoxVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07919E2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.h */; };
		2B4CB0B225B80B3B004B0767 /* Sel_HL.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1052064E58F00DF2F76 /* Sel_HL.h */; };
		2B4CB0B325B80B3B004B0767 /* Sel_Esca_Handrail_Guid.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792272134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.h */; };
		2B4CB0B425B80B3B004B0767 /* EscaAcessOverFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A22134E5AE00A65EE1 /* EscaAcessOverFactory.h */; };
		2B4CB0B525B80B3B004B0767 /* Global_Step_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792072134E47D00A65EE1 /* Global_Step_Parameter.h */; };
		2B4CB0B625B80B3B004B0767 /* VrBottomVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1182064E58F00DF2F76 /* VrBottomVisitor.h */; };
		2B4CB0B725B80B3B004B0767 /* VrHallConfigVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D12E2064E58F00DF2F76 /* VrHallConfigVisitor.h */; };
		2B4CB0B825B80B3B004B0767 /* vr_controller.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1122064E58F00DF2F76 /* vr_controller.h */; };
		2B4CB0B925B80B3B004B0767 /* EscaSkirtVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B62134E46100A65EE1 /* EscaSkirtVisitor.h */; };
		2B4CB0BA25B80B3B004B0767 /* EscaStepLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B82134E46100A65EE1 /* EscaStepLightingVisitor.h */; };
		2B4CB0BB25B80B3B004B0767 /* part_type.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0672064E4CA00DF2F76 /* part_type.h */; };
		2B4CB0BC25B80B3B004B0767 /* svr_part_basic_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E50207465D600126085 /* svr_part_basic_info.h */; };
		2B4CB0BD25B80B3B004B0767 /* Sel_Esca_Access_Cover_Flag.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792152134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.h */; };
		2B4CB0BE25B80B3B004B0767 /* PartTypeManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E32064E70300DF2F76 /* PartTypeManager.h */; };
		2B4CB0BF25B80B3B004B0767 /* Global_Loading_Scence.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E52064E58F00DF2F76 /* Global_Loading_Scence.h */; };
		2B4CB0C025B80B3B004B0767 /* EscaHandrailLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A62134E46100A65EE1 /* EscaHandrailLightingVisitor.h */; };
		2B4CB0C125B80B3B004B0767 /* file_pool.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CE2064E6FE00DF2F76 /* file_pool.h */; };
		2B4CB0C225B80B3B004B0767 /* VrCarDoorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D11C2064E58F00DF2F76 /* VrCarDoorVisitor.h */; };
		2B4CB0C325B80B3B004B0767 /* Sel_Esca_Step_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07923B2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.h */; };
		2B4CB0C425B80B3B004B0767 /* IDbVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D22064E6FF00DF2F76 /* IDbVisitor.h */; };
		2B4CB0C525B80B3B004B0767 /* Sel_HallDoor_Imported.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6723C57A1600682713 /* Sel_HallDoor_Imported.h */; };
		2B4CB0C625B80B3B004B0767 /* BaseFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E89207466BE00126085 /* BaseFactory.h */; };
		2B4CB0C725B80B3B004B0767 /* sha256.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E8B207466E200126085 /* sha256.h */; };
		2B4CB0C825B80B3B004B0767 /* svr_base_data.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E68207465DA00126085 /* svr_base_data.h */; };
		2B4CB0C925B80B3B004B0767 /* ConfigOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D92064E70100DF2F76 /* ConfigOperator.h */; };
		2B4CB0CA25B80B3B004B0767 /* Global_StepLight0_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792092134E47D00A65EE1 /* Global_StepLight0_Parameter.h */; };
		2B4CB0CB25B80B3B004B0767 /* Sel_Esca_Truss_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07923F2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.h */; };
		2B4CB0CC25B80B3B004B0767 /* allocator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0732064E4E800DF2F76 /* allocator.h */; };
		2B4CB0CD25B80B3B004B0767 /* VrCopDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1262064E58F00DF2F76 /* VrCopDisplayVisitor.h */; };
		2B4CB0CE25B80B3B004B0767 /* Global_Camera_EffectParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F12134E47D00A65EE1 /* Global_Camera_EffectParameter.h */; };
		2B4CB0CF25B80B3B004B0767 /* Sel_Esca_Access_Cover.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792112134E47D00A65EE1 /* Sel_Esca_Access_Cover.h */; };
		2B4CB0D025B80B3B004B0767 /* arg_cache.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F22064E70700DF2F76 /* arg_cache.h */; };
		2B4CB0D125B80B3B004B0767 /* decoration_vr_interface_lib.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0612064E4CA00DF2F76 /* decoration_vr_interface_lib.h */; };
		2B4CB0D225B80B3B004B0767 /* VrChangeArg.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1222064E58F00DF2F76 /* VrChangeArg.h */; };
		2B4CB0D325B80B3B004B0767 /* VrHallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC7C23C57A3500682713 /* VrHallVisitor.h */; };
		2B4CB0D425B80B3B004B0767 /* config_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F12064E70600DF2F76 /* config_factory.h */; };
		2B4CB0D525B80B3B004B0767 /* VrCarShellVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC7923C57A3400682713 /* VrCarShellVisitor.h */; };
		2B4CB0D625B80B3B004B0767 /* Sel_Esca_Side_Cladding_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792312134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.h */; };
		2B4CB0D725B80B3B004B0767 /* ElevatorConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20A2064E70D00DF2F76 /* ElevatorConfig.h */; };
		2B4CB0D825B80B3B004B0767 /* VrConfigInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1242064E58F00DF2F76 /* VrConfigInfo.h */; };
		2B4CB0D925B80B3B004B0767 /* decoration_vr_array_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EE2064E70600DF2F76 /* decoration_vr_array_visitor.h */; };
		2B4CB0DA25B80B3B004B0767 /* svr_material.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E63207465D900126085 /* svr_material.h */; };
		2B4CB0DB25B80B3B004B0767 /* EscaAcessOverFlagVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791942134E46000A65EE1 /* EscaAcessOverFlagVisitor.h */; };
		2B4CB0DC25B80B3B004B0767 /* Sel_Esca_Skirt_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792372134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.h */; };
		2B4CB0DD25B80B3B004B0767 /* Sel_Esca_Comb.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07921B2134E47D00A65EE1 /* Sel_Esca_Comb.h */; };
		2B4CB0DE25B80B3B004B0767 /* VrHallShaftVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC7A23C57A3400682713 /* VrHallShaftVisitor.h */; };
		2B4CB0DF25B80B3B004B0767 /* ElevatorPartFactoryManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C32064E6FC00DF2F76 /* ElevatorPartFactoryManager.h */; };
		2B4CB0E025B80B3B004B0767 /* svr_json_helper.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E58207465D700126085 /* svr_json_helper.h */; };
		2B4CB0E125B80B3B004B0767 /* ConfigAnalyzer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F52064E70700DF2F76 /* ConfigAnalyzer.h */; };
		2B4CB0E225B80B3B004B0767 /* VrHandrailVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1362064E58F00DF2F76 /* VrHandrailVisitor.h */; };
		2B4CB0E325B80B3B004B0767 /* car_wall_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C52064E6FC00DF2F76 /* car_wall_part.h */; };
		2B4CB0E425B80B3B004B0767 /* VrGlobalInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D12C2064E58F00DF2F76 /* VrGlobalInfo.h */; };
		2B4CB0E525B80B3B004B0767 /* Sel_Jamb.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1092064E58F00DF2F76 /* Sel_Jamb.h */; };
		2B4CB0E625B80B3B004B0767 /* BasePartOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F82064E70800DF2F76 /* BasePartOperator.h */; };
		2B4CB0E725B80B3B004B0767 /* Sel_Lop.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D10B2064E58F00DF2F76 /* Sel_Lop.h */; };
		2B4CB0E825B80B3B004B0767 /* IVrVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1102064E58F00DF2F76 /* IVrVisitor.h */; };
		2B4CB0E925B80B3B004B0767 /* Sel_LopLcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D10D2064E58F00DF2F76 /* Sel_LopLcd.h */; };
		2B4CB0EA25B80B3B004B0767 /* Global_Esca_Setting_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791FB2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.h */; };
		2B4CB0EB25B80B3B004B0767 /* ConstPartInnerParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1ED2064E70600DF2F76 /* ConstPartInnerParams.h */; };
		2B4CB0EC25B80B3B004B0767 /* DGBaseVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E12064E58F00DF2F76 /* DGBaseVisitor.h */; };
		2B4CB0ED25B80B3B004B0767 /* Sel_HandRail.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0FF2064E58F00DF2F76 /* Sel_HandRail.h */; };
		2B4CB0EE25B80B3B004B0767 /* MsgParaValue.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0662064E4CA00DF2F76 /* MsgParaValue.h */; };
		2B4CB0EF25B80B3B004B0767 /* EscaHandrailVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791A82134E46100A65EE1 /* EscaHandrailVisitor.h */; };
		2B4CB0F025B80B3B004B0767 /* HttpAsyncManage.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0A52064E54C00DF2F76 /* HttpAsyncManage.h */; };
		2B4CB0F125B80B3B004B0767 /* electric_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E62064E70400DF2F76 /* electric_part.h */; };
		2B4CB0F225B80B3B004B0767 /* Global_PartType_Info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792012134E47D00A65EE1 /* Global_PartType_Info.h */; };
		2B4CB0F325B80B3B004B0767 /* Sel_Esca_Step.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792392134E47D00A65EE1 /* Sel_Esca_Step.h */; };
		2B4CB0F425B80B3B004B0767 /* ElevatorSize.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1DF2064E70200DF2F76 /* ElevatorSize.h */; };
		2B4CB0F525B80B3B004B0767 /* Sel_HLLcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1072064E58F00DF2F76 /* Sel_HLLcd.h */; };
		2B4CB0F625B80B3B004B0767 /* VrHallDoorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1302064E58F00DF2F76 /* VrHallDoorVisitor.h */; };
		2B4CB0F725B80B3B004B0767 /* Sel_Shaft.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6923C57A1700682713 /* Sel_Shaft.h */; };
		2B4CB0F825B80B3B004B0767 /* Global_Setting_Car.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E92064E58F00DF2F76 /* Global_Setting_Car.h */; };
		2B4CB0F925B80B3B004B0767 /* Global_Step0_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792052134E47D00A65EE1 /* Global_Step0_Parameter.h */; };
		2B4CB0FA25B80B3B004B0767 /* reader.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07A2064E4E800DF2F76 /* reader.h */; };
		2B4CB0FB25B80B3B004B0767 /* decoration_vr_interface.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1B72064E6F900DF2F76 /* decoration_vr_interface.h */; };
		2B4CB0FC25B80B3B004B0767 /* config_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C12064E6FB00DF2F76 /* config_part.h */; };
		2B4CB0FD25B80B3B004B0767 /* EscaHandrailEnterFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A62134E5AE00A65EE1 /* EscaHandrailEnterFactory.h */; };
		2B4CB0FE25B80B3B004B0767 /* svr_material_special_rule.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5A207465D700126085 /* svr_material_special_rule.h */; };
		2B4CB0FF25B80B3B004B0767 /* EscaSideCladdingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B02134E46100A65EE1 /* EscaSideCladdingVisitor.h */; };
		2B4CB10025B80B3B004B0767 /* vr_visitor_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1142064E58F00DF2F76 /* vr_visitor_factory.h */; };
		2B4CB10125B80B3B004B0767 /* svr_car_wall.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E66207465D900126085 /* svr_car_wall.h */; };
		2B4CB10225B80B3B004B0767 /* PartOperatorManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C42064E6FC00DF2F76 /* PartOperatorManager.h */; };
		2B4CB10325B80B3B004B0767 /* resource.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1B52064E6F900DF2F76 /* resource.h */; };
		2B4CB10425B80B3B004B0767 /* common_part_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EC2064E70500DF2F76 /* common_part_factory.h */; };
		2B4CB10525B80B3B004B0767 /* ElevatorConfigManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1FA2064E70900DF2F76 /* ElevatorConfigManager.h */; };
		2B4CB10625B80B3B004B0767 /* VrAccessoryVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1162064E58F00DF2F76 /* VrAccessoryVisitor.h */; };
		2B4CB10725B80B3B004B0767 /* EscalatorSize.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792942134E51400A65EE1 /* EscalatorSize.h */; };
		2B4CB10825B80B3B004B0767 /* ElectricOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1BC2064E6FA00DF2F76 /* ElectricOperator.h */; };
		2B4CB10925B80B3B004B0767 /* Global_Esca_PartMark.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F92134E47D00A65EE1 /* Global_Esca_PartMark.h */; };
		2B4CB10A25B80B3B004B0767 /* IPartTypeManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20C2064E70D00DF2F76 /* IPartTypeManager.h */; };
		2B4CB10B25B80B3B004B0767 /* Sel_CarIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F52064E58F00DF2F76 /* Sel_CarIndicator.h */; };
		2B4CB10C25B80B3B004B0767 /* IConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1DB2064E70100DF2F76 /* IConfig.h */; };
		2B4CB10D25B80B3B004B0767 /* svr_common_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5B207465D700126085 /* svr_common_part.h */; };
		2B4CB10E25B80B3B004B0767 /* Global_Select.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E72064E58F00DF2F76 /* Global_Select.h */; };
		2B4CB10F25B80B3B004B0767 /* EscaBalustradeVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791962134E46100A65EE1 /* EscaBalustradeVisitor.h */; };
		2B4CB11025B80B3B004B0767 /* svr_file_digital_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E52207465D600126085 /* svr_file_digital_info.h */; };
		2B4CB11125B80B3B004B0767 /* VrTopVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1462064E58F00DF2F76 /* VrTopVisitor.h */; };
		2B4CB11225B80B3B004B0767 /* Global_Setting_Hall.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0EB2064E58F00DF2F76 /* Global_Setting_Hall.h */; };
		2B4CB11325B80B3B004B0767 /* EscaAccessOverVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791902134E46000A65EE1 /* EscaAccessOverVisitor.h */; };
		2B4CB11425B80B3B004B0767 /* hall_room_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC8523C57A7400682713 /* hall_room_part.h */; };
		2B4CB11525B80B3B004B0767 /* dvi_tinyxml2.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792972134E51400A65EE1 /* dvi_tinyxml2.h */; };
		2B4CB11625B80B3B004B0767 /* Global_Control_Flag.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F52134E47D00A65EE1 /* Global_Control_Flag.h */; };
		2B4CB11725B80B3B004B0767 /* Global_Escalators_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791FD2134E47D00A65EE1 /* Global_Escalators_Parameter.h */; };
		2B4CB11825B80B3B004B0767 /* VrHallIndicatorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1342064E58F00DF2F76 /* VrHallIndicatorVisitor.h */; };
		2B4CB11925B80B3B004B0767 /* Sel_Esca_Handrail.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792232134E47D00A65EE1 /* Sel_Esca_Handrail.h */; };
		2B4CB11A25B80B3B004B0767 /* features.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0772064E4E800DF2F76 /* features.h */; };
		2B4CB11B25B80B3B004B0767 /* BaseVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20D2064E70D00DF2F76 /* BaseVisitor.h */; };
		2B4CB11C25B80B3B004B0767 /* svr_material_channel.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E54207465D600126085 /* svr_material_channel.h */; };
		2B4CB11D25B80B3B004B0767 /* EscaCombVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07919A2134E46100A65EE1 /* EscaCombVisitor.h */; };
		2B4CB11E25B80B3B004B0767 /* EscaStepVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791BA2134E46100A65EE1 /* EscaStepVisitor.h */; };
		2B4CB11F25B80B3B004B0767 /* Sel_Esca_Access_Cover_ExpendType.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792132134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.h */; };
		2B4CB12025B80B3B004B0767 /* car_top.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D42064E70000DF2F76 /* car_top.h */; };
		2B4CB12125B80B3B004B0767 /* EscaSideCladdingLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791AE2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.h */; };
		2B4CB12225B80B3B004B0767 /* car_wall_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E82064E70400DF2F76 /* car_wall_factory.h */; };
		2B4CB12325B80B3B004B0767 /* EscaBalustradeFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792A42134E5AE00A65EE1 /* EscaBalustradeFactory.h */; };
		2B4CB12425B80B3B004B0767 /* Sel_Esca_Comb_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07921D2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.h */; };
		2B4CB12525B80B3B004B0767 /* writer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07D2064E4E800DF2F76 /* writer.h */; };
		2B4CB12625B80B3B004B0767 /* Sel_Esca_Skirt_Brush.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792352134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.h */; };
		2B4CB12725B80B3B004B0767 /* lru_cache.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E8E207466E200126085 /* lru_cache.h */; };
		2B4CB12825B80B3B004B0767 /* material_channel_pool.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CA2064E6FD00DF2F76 /* material_channel_pool.h */; };
		2B4CB12925B80B3B004B0767 /* car_top_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2042064E70B00DF2F76 /* car_top_factory.h */; };
		2B4CB12A25B80B3B004B0767 /* value.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07B2064E4E800DF2F76 /* value.h */; };
		2B4CB12B25B80B3B004B0767 /* Sel_Esca_Traffic_Light.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07923D2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.h */; };
		2B4CB12C25B80B3B004B0767 /* i_decoration_vr_interface_extend.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0652064E4CA00DF2F76 /* i_decoration_vr_interface_extend.h */; };
		2B4CB12D25B80B3B004B0767 /* Global_TestNote.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07920B2134E47D00A65EE1 /* Global_TestNote.h */; };
		2B4CB12E25B80B3B004B0767 /* Sel_Esca_Side_Cladding.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07922F2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.h */; };
		2B4CB12F25B80B3B004B0767 /* IElevatorPartFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D72064E70000DF2F76 /* IElevatorPartFactory.h */; };
		2B4CB13025B80B3B004B0767 /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0762064E4E800DF2F76 /* config.h */; };
		2B4CB13125B80B3B004B0767 /* svr_wall_size_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E65207465D900126085 /* svr_wall_size_info.h */; };
		2B4CB13225B80B3B004B0767 /* svr_config.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5C207465D800126085 /* svr_config.h */; };
		2B4CB13325B80B3B004B0767 /* Sel_Esca_Decking_Ctrl_Box.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792212134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.h */; };
		2B4CB13425B80B3B004B0767 /* IElevatorConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1BB2064E6FA00DF2F76 /* IElevatorConfig.h */; };
		2B4CB13525B80B3B004B0767 /* Global_Work1_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07920F2134E47D00A65EE1 /* Global_Work1_Parameter.h */; };
		2B4CB13625B80B3B004B0767 /* EscaTrafficLightVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791BC2134E46100A65EE1 /* EscaTrafficLightVisitor.h */; };
		2B4CB13725B80B3B004B0767 /* download_assist.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BED8C0C228BE75400AEF4D0 /* download_assist.h */; };
		2B4CB13825B80B3B004B0767 /* Global_Work0_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07920D2134E47D00A65EE1 /* Global_Work0_Parameter.h */; };
		2B4CB13925B80B3B004B0767 /* Sel_Esca_Handrail_Lighting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0792292134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.h */; };
		2B4CB13A25B80B3B004B0767 /* i_decoration_vr_array_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0622064E4CA00DF2F76 /* i_decoration_vr_array_visitor.h */; };
		2B4CB13B25B80B3B004B0767 /* decoration_vr_interface_include.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EA2064E70500DF2F76 /* decoration_vr_interface_include.h */; };
		2B4CB13C25B80B3B004B0767 /* Sel_Esca_Scene.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07922D2134E47D00A65EE1 /* Sel_Esca_Scene.h */; };
		2B4CB13D25B80B3B004B0767 /* Sel_Ceiling.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F72064E58F00DF2F76 /* Sel_Ceiling.h */; };
		2B4CB13E25B80B3B004B0767 /* Sel_Accessory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F32064E58F00DF2F76 /* Sel_Accessory.h */; };
		2B4CB13F25B80B3B004B0767 /* FrontWallTypeOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2022064E70B00DF2F76 /* FrontWallTypeOperator.h */; };
		2B4CB14025B80B3B004B0767 /* Global_Hwndmsg.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E32064E58F00DF2F76 /* Global_Hwndmsg.h */; };
		2B4CB14125B80B3B004B0767 /* electric_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1BF2064E6FB00DF2F76 /* electric_factory.h */; };
		2B4CB14225B80B3B004B0767 /* forwards.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0782064E4E800DF2F76 /* forwards.h */; };
		2B4CB14325B80B3B004B0767 /* Global_CopyObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791F72134E47D00A65EE1 /* Global_CopyObject.h */; };
		2B4CB14425B80B3B004B0767 /* EscaSkirtLightingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791B42134E46100A65EE1 /* EscaSkirtLightingVisitor.h */; };
		2B4CB14525B80B3B004B0767 /* Sel_HI.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1012064E58F00DF2F76 /* Sel_HI.h */; };
		2B4CB14625B80B3B004B0767 /* i_decoration_vr_callback.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0632064E4CA00DF2F76 /* i_decoration_vr_callback.h */; };
		2B4CB14725B80B3B004B0767 /* EscaScenesVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B0791AC2134E46100A65EE1 /* EscaScenesVisitor.h */; };
		2B4CB14825B80B3B004B0767 /* material_channel.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0EF2064E58F00DF2F76 /* material_channel.h */; };
		2B4CB14925B80B3B004B0767 /* Sel_Esca_Decking.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B07921F2134E47D00A65EE1 /* Sel_Esca_Decking.h */; };
		2B4CB15225B80D77004B0767 /* VrHallWallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B4CB14E25B80D76004B0767 /* VrHallWallVisitor.cpp */; };
		2B4CB15325B80D77004B0767 /* VrHallWallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B4CB14E25B80D76004B0767 /* VrHallWallVisitor.cpp */; };
		2B4CB15425B80D77004B0767 /* VrHallFloorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B4CB14F25B80D76004B0767 /* VrHallFloorVisitor.cpp */; };
		2B4CB15525B80D77004B0767 /* VrHallFloorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B4CB14F25B80D76004B0767 /* VrHallFloorVisitor.cpp */; };
		2B4CB15625B80D77004B0767 /* VrHallFloorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B4CB15025B80D76004B0767 /* VrHallFloorVisitor.h */; };
		2B4CB15725B80D77004B0767 /* VrHallFloorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B4CB15025B80D76004B0767 /* VrHallFloorVisitor.h */; };
		2B4CB15825B80D77004B0767 /* VrHallWallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B4CB15125B80D76004B0767 /* VrHallWallVisitor.h */; };
		2B4CB15925B80D77004B0767 /* VrHallWallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B4CB15125B80D76004B0767 /* VrHallWallVisitor.h */; };
		2B56D0682064E4CA00DF2F76 /* decoration_vr_interface_lib.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0612064E4CA00DF2F76 /* decoration_vr_interface_lib.h */; };
		2B56D0692064E4CA00DF2F76 /* i_decoration_vr_array_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0622064E4CA00DF2F76 /* i_decoration_vr_array_visitor.h */; };
		2B56D06A2064E4CA00DF2F76 /* i_decoration_vr_callback.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0632064E4CA00DF2F76 /* i_decoration_vr_callback.h */; };
		2B56D06B2064E4CA00DF2F76 /* i_decoration_vr_interface.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0642064E4CA00DF2F76 /* i_decoration_vr_interface.h */; };
		2B56D06C2064E4CA00DF2F76 /* i_decoration_vr_interface_extend.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0652064E4CA00DF2F76 /* i_decoration_vr_interface_extend.h */; };
		2B56D06D2064E4CA00DF2F76 /* MsgParaValue.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0662064E4CA00DF2F76 /* MsgParaValue.h */; };
		2B56D06E2064E4CA00DF2F76 /* part_type.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0672064E4CA00DF2F76 /* part_type.h */; };
		2B56D0902064E4E800DF2F76 /* allocator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0732064E4E800DF2F76 /* allocator.h */; };
		2B56D0912064E4E800DF2F76 /* assertions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0742064E4E800DF2F76 /* assertions.h */; };
		2B56D0922064E4E800DF2F76 /* autolink.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0752064E4E800DF2F76 /* autolink.h */; };
		2B56D0932064E4E800DF2F76 /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0762064E4E800DF2F76 /* config.h */; };
		2B56D0942064E4E800DF2F76 /* features.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0772064E4E800DF2F76 /* features.h */; };
		2B56D0952064E4E800DF2F76 /* forwards.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0782064E4E800DF2F76 /* forwards.h */; };
		2B56D0962064E4E800DF2F76 /* json.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0792064E4E800DF2F76 /* json.h */; };
		2B56D0972064E4E800DF2F76 /* reader.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07A2064E4E800DF2F76 /* reader.h */; };
		2B56D0982064E4E800DF2F76 /* value.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07B2064E4E800DF2F76 /* value.h */; };
		2B56D0992064E4E800DF2F76 /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07C2064E4E800DF2F76 /* version.h */; };
		2B56D09A2064E4E800DF2F76 /* writer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D07D2064E4E800DF2F76 /* writer.h */; };
		2B56D09C2064E4E800DF2F76 /* json_reader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0852064E4E800DF2F76 /* json_reader.cpp */; };
		2B56D09D2064E4E800DF2F76 /* json_tool.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0862064E4E800DF2F76 /* json_tool.h */; };
		2B56D09E2064E4E800DF2F76 /* json_value.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0872064E4E800DF2F76 /* json_value.cpp */; };
		2B56D09F2064E4E800DF2F76 /* json_writer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0892064E4E800DF2F76 /* json_writer.cpp */; };
		2B56D0A62064E54C00DF2F76 /* HttpAsyncManage.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0A42064E54C00DF2F76 /* HttpAsyncManage.cpp */; };
		2B56D0A72064E54C00DF2F76 /* HttpAsyncManage.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0A52064E54C00DF2F76 /* HttpAsyncManage.h */; };
		2B56D1472064E58F00DF2F76 /* DGBaseVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E02064E58F00DF2F76 /* DGBaseVisitor.cpp */; };
		2B56D1482064E58F00DF2F76 /* DGBaseVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E12064E58F00DF2F76 /* DGBaseVisitor.h */; };
		2B56D1492064E58F00DF2F76 /* Global_Hwndmsg.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E22064E58F00DF2F76 /* Global_Hwndmsg.cpp */; };
		2B56D14A2064E58F00DF2F76 /* Global_Hwndmsg.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E32064E58F00DF2F76 /* Global_Hwndmsg.h */; };
		2B56D14B2064E58F00DF2F76 /* Global_Loading_Scence.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E42064E58F00DF2F76 /* Global_Loading_Scence.cpp */; };
		2B56D14C2064E58F00DF2F76 /* Global_Loading_Scence.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E52064E58F00DF2F76 /* Global_Loading_Scence.h */; };
		2B56D14D2064E58F00DF2F76 /* Global_Select.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E62064E58F00DF2F76 /* Global_Select.cpp */; };
		2B56D14E2064E58F00DF2F76 /* Global_Select.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E72064E58F00DF2F76 /* Global_Select.h */; };
		2B56D14F2064E58F00DF2F76 /* Global_Setting_Car.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0E82064E58F00DF2F76 /* Global_Setting_Car.cpp */; };
		2B56D1502064E58F00DF2F76 /* Global_Setting_Car.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0E92064E58F00DF2F76 /* Global_Setting_Car.h */; };
		2B56D1512064E58F00DF2F76 /* Global_Setting_Hall.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0EA2064E58F00DF2F76 /* Global_Setting_Hall.cpp */; };
		2B56D1522064E58F00DF2F76 /* Global_Setting_Hall.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0EB2064E58F00DF2F76 /* Global_Setting_Hall.h */; };
		2B56D1532064E58F00DF2F76 /* Global_Setting_Parameter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0EC2064E58F00DF2F76 /* Global_Setting_Parameter.cpp */; };
		2B56D1542064E58F00DF2F76 /* Global_Setting_Parameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0ED2064E58F00DF2F76 /* Global_Setting_Parameter.h */; };
		2B56D1552064E58F00DF2F76 /* material_channel.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0EE2064E58F00DF2F76 /* material_channel.cpp */; };
		2B56D1562064E58F00DF2F76 /* material_channel.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0EF2064E58F00DF2F76 /* material_channel.h */; };
		2B56D1572064E58F00DF2F76 /* ModelCarWallElem.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F02064E58F00DF2F76 /* ModelCarWallElem.cpp */; };
		2B56D1582064E58F00DF2F76 /* ModelCarWallElem.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F12064E58F00DF2F76 /* ModelCarWallElem.h */; };
		2B56D1592064E58F00DF2F76 /* Sel_Accessory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F22064E58F00DF2F76 /* Sel_Accessory.cpp */; };
		2B56D15A2064E58F00DF2F76 /* Sel_Accessory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F32064E58F00DF2F76 /* Sel_Accessory.h */; };
		2B56D15B2064E58F00DF2F76 /* Sel_CarIndicator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F42064E58F00DF2F76 /* Sel_CarIndicator.cpp */; };
		2B56D15C2064E58F00DF2F76 /* Sel_CarIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F52064E58F00DF2F76 /* Sel_CarIndicator.h */; };
		2B56D15D2064E58F00DF2F76 /* Sel_Ceiling.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F62064E58F00DF2F76 /* Sel_Ceiling.cpp */; };
		2B56D15E2064E58F00DF2F76 /* Sel_Ceiling.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F72064E58F00DF2F76 /* Sel_Ceiling.h */; };
		2B56D15F2064E58F00DF2F76 /* Sel_Cop.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0F82064E58F00DF2F76 /* Sel_Cop.cpp */; };
		2B56D1602064E58F00DF2F76 /* Sel_Cop.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0F92064E58F00DF2F76 /* Sel_Cop.h */; };
		2B56D1612064E58F00DF2F76 /* Sel_CopLcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0FA2064E58F00DF2F76 /* Sel_CopLcd.cpp */; };
		2B56D1622064E58F00DF2F76 /* Sel_CopLcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0FB2064E58F00DF2F76 /* Sel_CopLcd.h */; };
		2B56D1632064E58F00DF2F76 /* Sel_Hall.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0FC2064E58F00DF2F76 /* Sel_Hall.cpp */; };
		2B56D1642064E58F00DF2F76 /* Sel_Hall.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0FD2064E58F00DF2F76 /* Sel_Hall.h */; };
		2B56D1652064E58F00DF2F76 /* Sel_HandRail.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D0FE2064E58F00DF2F76 /* Sel_HandRail.cpp */; };
		2B56D1662064E58F00DF2F76 /* Sel_HandRail.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D0FF2064E58F00DF2F76 /* Sel_HandRail.h */; };
		2B56D1672064E58F00DF2F76 /* Sel_HI.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1002064E58F00DF2F76 /* Sel_HI.cpp */; };
		2B56D1682064E58F00DF2F76 /* Sel_HI.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1012064E58F00DF2F76 /* Sel_HI.h */; };
		2B56D1692064E58F00DF2F76 /* Sel_HILcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1022064E58F00DF2F76 /* Sel_HILcd.cpp */; };
		2B56D16A2064E58F00DF2F76 /* Sel_HILcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1032064E58F00DF2F76 /* Sel_HILcd.h */; };
		2B56D16B2064E58F00DF2F76 /* Sel_HL.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1042064E58F00DF2F76 /* Sel_HL.cpp */; };
		2B56D16C2064E58F00DF2F76 /* Sel_HL.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1052064E58F00DF2F76 /* Sel_HL.h */; };
		2B56D16D2064E58F00DF2F76 /* Sel_HLLcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1062064E58F00DF2F76 /* Sel_HLLcd.cpp */; };
		2B56D16E2064E58F00DF2F76 /* Sel_HLLcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1072064E58F00DF2F76 /* Sel_HLLcd.h */; };
		2B56D16F2064E58F00DF2F76 /* Sel_Jamb.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1082064E58F00DF2F76 /* Sel_Jamb.cpp */; };
		2B56D1702064E58F00DF2F76 /* Sel_Jamb.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1092064E58F00DF2F76 /* Sel_Jamb.h */; };
		2B56D1712064E58F00DF2F76 /* Sel_Lop.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D10A2064E58F00DF2F76 /* Sel_Lop.cpp */; };
		2B56D1722064E58F00DF2F76 /* Sel_Lop.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D10B2064E58F00DF2F76 /* Sel_Lop.h */; };
		2B56D1732064E58F00DF2F76 /* Sel_LopLcd.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D10C2064E58F00DF2F76 /* Sel_LopLcd.cpp */; };
		2B56D1742064E58F00DF2F76 /* Sel_LopLcd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D10D2064E58F00DF2F76 /* Sel_LopLcd.h */; };
		2B56D1752064E58F00DF2F76 /* Sel_Mirror.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D10E2064E58F00DF2F76 /* Sel_Mirror.cpp */; };
		2B56D1762064E59000DF2F76 /* Sel_Mirror.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D10F2064E58F00DF2F76 /* Sel_Mirror.h */; };
		2B56D1772064E59000DF2F76 /* IVrVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1102064E58F00DF2F76 /* IVrVisitor.h */; };
		2B56D1782064E59000DF2F76 /* vr_controller.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1112064E58F00DF2F76 /* vr_controller.cpp */; };
		2B56D1792064E59000DF2F76 /* vr_controller.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1122064E58F00DF2F76 /* vr_controller.h */; };
		2B56D17A2064E59000DF2F76 /* vr_visitor_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1132064E58F00DF2F76 /* vr_visitor_factory.cpp */; };
		2B56D17B2064E59000DF2F76 /* vr_visitor_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1142064E58F00DF2F76 /* vr_visitor_factory.h */; };
		2B56D17C2064E59000DF2F76 /* VrAccessoryVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1152064E58F00DF2F76 /* VrAccessoryVisitor.cpp */; };
		2B56D17D2064E59000DF2F76 /* VrAccessoryVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1162064E58F00DF2F76 /* VrAccessoryVisitor.h */; };
		2B56D17E2064E59000DF2F76 /* VrBottomVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1172064E58F00DF2F76 /* VrBottomVisitor.cpp */; };
		2B56D17F2064E59000DF2F76 /* VrBottomVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1182064E58F00DF2F76 /* VrBottomVisitor.h */; };
		2B56D1802064E59000DF2F76 /* VrCarConfigVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1192064E58F00DF2F76 /* VrCarConfigVisitor.cpp */; };
		2B56D1812064E59000DF2F76 /* VrCarConfigVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D11A2064E58F00DF2F76 /* VrCarConfigVisitor.h */; };
		2B56D1822064E59000DF2F76 /* VrCarDoorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D11B2064E58F00DF2F76 /* VrCarDoorVisitor.cpp */; };
		2B56D1832064E59000DF2F76 /* VrCarDoorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D11C2064E58F00DF2F76 /* VrCarDoorVisitor.h */; };
		2B56D1842064E59000DF2F76 /* VrCarIndicatorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D11D2064E58F00DF2F76 /* VrCarIndicatorVisitor.cpp */; };
		2B56D1852064E59000DF2F76 /* VrCarIndicatorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D11E2064E58F00DF2F76 /* VrCarIndicatorVisitor.h */; };
		2B56D1862064E59000DF2F76 /* VrCarWallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D11F2064E58F00DF2F76 /* VrCarWallVisitor.cpp */; };
		2B56D1872064E59000DF2F76 /* VrCarWallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1202064E58F00DF2F76 /* VrCarWallVisitor.h */; };
		2B56D1882064E59000DF2F76 /* VrChangeArg.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1212064E58F00DF2F76 /* VrChangeArg.cpp */; };
		2B56D1892064E59000DF2F76 /* VrChangeArg.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1222064E58F00DF2F76 /* VrChangeArg.h */; };
		2B56D18A2064E59000DF2F76 /* VrConfigInfo.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1232064E58F00DF2F76 /* VrConfigInfo.cpp */; };
		2B56D18B2064E59000DF2F76 /* VrConfigInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1242064E58F00DF2F76 /* VrConfigInfo.h */; };
		2B56D18C2064E59000DF2F76 /* VrCopDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1252064E58F00DF2F76 /* VrCopDisplayVisitor.cpp */; };
		2B56D18D2064E59000DF2F76 /* VrCopDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1262064E58F00DF2F76 /* VrCopDisplayVisitor.h */; };
		2B56D18E2064E59000DF2F76 /* VrCopVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1272064E58F00DF2F76 /* VrCopVisitor.cpp */; };
		2B56D18F2064E59000DF2F76 /* VrCopVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1282064E58F00DF2F76 /* VrCopVisitor.h */; };
		2B56D1902064E59000DF2F76 /* VrFrontWallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1292064E58F00DF2F76 /* VrFrontWallVisitor.cpp */; };
		2B56D1912064E59000DF2F76 /* VrFrontWallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D12A2064E58F00DF2F76 /* VrFrontWallVisitor.h */; };
		2B56D1922064E59000DF2F76 /* VrGlobalInfo.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D12B2064E58F00DF2F76 /* VrGlobalInfo.cpp */; };
		2B56D1932064E59000DF2F76 /* VrGlobalInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D12C2064E58F00DF2F76 /* VrGlobalInfo.h */; };
		2B56D1942064E59000DF2F76 /* VrHallConfigVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D12D2064E58F00DF2F76 /* VrHallConfigVisitor.cpp */; };
		2B56D1952064E59000DF2F76 /* VrHallConfigVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D12E2064E58F00DF2F76 /* VrHallConfigVisitor.h */; };
		2B56D1962064E59000DF2F76 /* VrHallDoorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D12F2064E58F00DF2F76 /* VrHallDoorVisitor.cpp */; };
		2B56D1972064E59000DF2F76 /* VrHallDoorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1302064E58F00DF2F76 /* VrHallDoorVisitor.h */; };
		2B56D1982064E59000DF2F76 /* VrHallIndicatorDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1312064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.cpp */; };
		2B56D1992064E59000DF2F76 /* VrHallIndicatorDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1322064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.h */; };
		2B56D19A2064E59000DF2F76 /* VrHallIndicatorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1332064E58F00DF2F76 /* VrHallIndicatorVisitor.cpp */; };
		2B56D19B2064E59000DF2F76 /* VrHallIndicatorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1342064E58F00DF2F76 /* VrHallIndicatorVisitor.h */; };
		2B56D19C2064E59000DF2F76 /* VrHandrailVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1352064E58F00DF2F76 /* VrHandrailVisitor.cpp */; };
		2B56D19D2064E59000DF2F76 /* VrHandrailVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1362064E58F00DF2F76 /* VrHandrailVisitor.h */; };
		2B56D19E2064E59000DF2F76 /* VrJambVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1372064E58F00DF2F76 /* VrJambVisitor.cpp */; };
		2B56D19F2064E59000DF2F76 /* VrJambVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1382064E58F00DF2F76 /* VrJambVisitor.h */; };
		2B56D1A02064E59000DF2F76 /* VrLanternDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1392064E58F00DF2F76 /* VrLanternDisplayVisitor.cpp */; };
		2B56D1A12064E59000DF2F76 /* VrLanternDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D13A2064E58F00DF2F76 /* VrLanternDisplayVisitor.h */; };
		2B56D1A22064E59000DF2F76 /* VrLanternVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D13B2064E58F00DF2F76 /* VrLanternVisitor.cpp */; };
		2B56D1A32064E59000DF2F76 /* VrLanternVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D13C2064E58F00DF2F76 /* VrLanternVisitor.h */; };
		2B56D1A42064E59000DF2F76 /* VrLopDisplayVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D13D2064E58F00DF2F76 /* VrLopDisplayVisitor.cpp */; };
		2B56D1A52064E59000DF2F76 /* VrLopDisplayVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D13E2064E58F00DF2F76 /* VrLopDisplayVisitor.h */; };
		2B56D1A62064E59000DF2F76 /* VrLopVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D13F2064E58F00DF2F76 /* VrLopVisitor.cpp */; };
		2B56D1A72064E59000DF2F76 /* VrLopVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1402064E58F00DF2F76 /* VrLopVisitor.h */; };
		2B56D1A82064E59000DF2F76 /* VrMirrorVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1412064E58F00DF2F76 /* VrMirrorVisitor.cpp */; };
		2B56D1A92064E59000DF2F76 /* VrMirrorVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1422064E58F00DF2F76 /* VrMirrorVisitor.h */; };
		2B56D1AA2064E59000DF2F76 /* VrSightseeingVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1432064E58F00DF2F76 /* VrSightseeingVisitor.cpp */; };
		2B56D1AB2064E59000DF2F76 /* VrSightseeingVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1442064E58F00DF2F76 /* VrSightseeingVisitor.h */; };
		2B56D1AC2064E59000DF2F76 /* VrTopVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1452064E58F00DF2F76 /* VrTopVisitor.cpp */; };
		2B56D1AD2064E59000DF2F76 /* VrTopVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1462064E58F00DF2F76 /* VrTopVisitor.h */; };
		2B56D20E2064E70E00DF2F76 /* resource.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1B52064E6F900DF2F76 /* resource.h */; };
		2B56D20F2064E70E00DF2F76 /* skirting_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1B62064E6F900DF2F76 /* skirting_part.h */; };
		2B56D2102064E70E00DF2F76 /* decoration_vr_interface.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1B72064E6F900DF2F76 /* decoration_vr_interface.h */; };
		2B56D2112064E70E00DF2F76 /* electric_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1B82064E6FA00DF2F76 /* electric_factory.cpp */; };
		2B56D2132064E70E00DF2F76 /* ElevatorSize.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1BA2064E6FA00DF2F76 /* ElevatorSize.cpp */; };
		2B56D2142064E70E00DF2F76 /* IElevatorConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1BB2064E6FA00DF2F76 /* IElevatorConfig.h */; };
		2B56D2152064E70E00DF2F76 /* ElectricOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1BC2064E6FA00DF2F76 /* ElectricOperator.h */; };
		2B56D2162064E70E00DF2F76 /* config_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1BD2064E6FB00DF2F76 /* config_factory.cpp */; };
		2B56D2172064E70E00DF2F76 /* arg_cache.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1BE2064E6FB00DF2F76 /* arg_cache.cpp */; };
		2B56D2182064E70E00DF2F76 /* electric_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1BF2064E6FB00DF2F76 /* electric_factory.h */; };
		2B56D2192064E70E00DF2F76 /* ElectricOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C02064E6FB00DF2F76 /* ElectricOperator.cpp */; };
		2B56D21A2064E70E00DF2F76 /* config_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C12064E6FB00DF2F76 /* config_part.h */; };
		2B56D21B2064E70E00DF2F76 /* IDownloadVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C22064E6FC00DF2F76 /* IDownloadVisitor.h */; };
		2B56D21C2064E70E00DF2F76 /* ElevatorPartFactoryManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C32064E6FC00DF2F76 /* ElevatorPartFactoryManager.h */; };
		2B56D21D2064E70E00DF2F76 /* PartOperatorManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C42064E6FC00DF2F76 /* PartOperatorManager.h */; };
		2B56D21E2064E70E00DF2F76 /* car_wall_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1C52064E6FC00DF2F76 /* car_wall_part.h */; };
		2B56D21F2064E70E00DF2F76 /* file_pool.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C62064E6FC00DF2F76 /* file_pool.cpp */; };
		2B56D2202064E70E00DF2F76 /* ConstValueMap.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C72064E6FD00DF2F76 /* ConstValueMap.cpp */; };
		2B56D2212064E70E00DF2F76 /* stdafx.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C82064E6FD00DF2F76 /* stdafx.cpp */; };
		2B56D2222064E70E00DF2F76 /* decoration_vr_array_visitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1C92064E6FD00DF2F76 /* decoration_vr_array_visitor.cpp */; };
		2B56D2232064E70E00DF2F76 /* material_channel_pool.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CA2064E6FD00DF2F76 /* material_channel_pool.h */; };
		2B56D2242064E70E00DF2F76 /* IElevatorPart.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CB2064E6FD00DF2F76 /* IElevatorPart.h */; };
		2B56D2252064E70E00DF2F76 /* download_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CC2064E6FE00DF2F76 /* download_visitor.h */; };
		2B56D2262064E70E00DF2F76 /* BasePartOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1CD2064E6FE00DF2F76 /* BasePartOperator.cpp */; };
		2B56D2272064E70E00DF2F76 /* file_pool.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1CE2064E6FE00DF2F76 /* file_pool.h */; };
		2B56D2282064E70E00DF2F76 /* decoration_vr_interface.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1CF2064E6FE00DF2F76 /* decoration_vr_interface.cpp */; };
		2B56D2292064E70E00DF2F76 /* skirting_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D02064E6FF00DF2F76 /* skirting_factory.h */; };
		2B56D22A2064E70E00DF2F76 /* config_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D12064E6FF00DF2F76 /* config_part.cpp */; };
		2B56D22B2064E70E00DF2F76 /* IDbVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D22064E6FF00DF2F76 /* IDbVisitor.h */; };
		2B56D22C2064E70E00DF2F76 /* skirting_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D32064E6FF00DF2F76 /* skirting_factory.cpp */; };
		2B56D22D2064E70E00DF2F76 /* car_top.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D42064E70000DF2F76 /* car_top.h */; };
		2B56D22E2064E70E00DF2F76 /* common_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D52064E70000DF2F76 /* common_part.cpp */; };
		2B56D22F2064E70E00DF2F76 /* GlobalInfoDataCommon.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D62064E70000DF2F76 /* GlobalInfoDataCommon.cpp */; };
		2B56D2302064E70E00DF2F76 /* IElevatorPartFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D72064E70000DF2F76 /* IElevatorPartFactory.h */; };
		2B56D2312064E70E00DF2F76 /* ElevatorConfig.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1D82064E70000DF2F76 /* ElevatorConfig.cpp */; };
		2B56D2322064E70E00DF2F76 /* ConfigOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1D92064E70100DF2F76 /* ConfigOperator.h */; };
		2B56D2332064E70E00DF2F76 /* FrontWallTypeOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DA2064E70100DF2F76 /* FrontWallTypeOperator.cpp */; };
		2B56D2342064E70E00DF2F76 /* IConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1DB2064E70100DF2F76 /* IConfig.h */; };
		2B56D2352064E70E00DF2F76 /* ElevatorPartFactoryManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DC2064E70200DF2F76 /* ElevatorPartFactoryManager.cpp */; };
		2B56D2362064E70E00DF2F76 /* electric_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DD2064E70200DF2F76 /* electric_part.cpp */; };
		2B56D2372064E70E00DF2F76 /* skirting_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1DE2064E70200DF2F76 /* skirting_part.cpp */; };
		2B56D2382064E70E00DF2F76 /* ElevatorSize.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1DF2064E70200DF2F76 /* ElevatorSize.h */; };
		2B56D2392064E70E00DF2F76 /* ConstValueMap.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E02064E70200DF2F76 /* ConstValueMap.h */; };
		2B56D23A2064E70E00DF2F76 /* ElevatorConfigManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1E12064E70300DF2F76 /* ElevatorConfigManager.cpp */; };
		2B56D23C2064E70E00DF2F76 /* PartTypeManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E32064E70300DF2F76 /* PartTypeManager.h */; };
		2B56D23E2064E70E00DF2F76 /* GlobalInfoDataCommon.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E52064E70400DF2F76 /* GlobalInfoDataCommon.h */; };
		2B56D23F2064E70E00DF2F76 /* electric_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E62064E70400DF2F76 /* electric_part.h */; };
		2B56D2402064E70E00DF2F76 /* BaseVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1E72064E70400DF2F76 /* BaseVisitor.cpp */; };
		2B56D2412064E70E00DF2F76 /* car_wall_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1E82064E70400DF2F76 /* car_wall_factory.h */; };
		2B56D2422064E70E00DF2F76 /* elevator_size_parser.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1E92064E70500DF2F76 /* elevator_size_parser.cpp */; };
		2B56D2432064E70E00DF2F76 /* decoration_vr_interface_include.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EA2064E70500DF2F76 /* decoration_vr_interface_include.h */; };
		2B56D2442064E70E00DF2F76 /* ConfigOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1EB2064E70500DF2F76 /* ConfigOperator.cpp */; };
		2B56D2452064E70E00DF2F76 /* common_part_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EC2064E70500DF2F76 /* common_part_factory.h */; };
		2B56D2462064E70E00DF2F76 /* ConstPartInnerParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1ED2064E70600DF2F76 /* ConstPartInnerParams.h */; };
		2B56D2472064E70E00DF2F76 /* decoration_vr_array_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EE2064E70600DF2F76 /* decoration_vr_array_visitor.h */; };
		2B56D2482064E70E00DF2F76 /* stdafx.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1EF2064E70600DF2F76 /* stdafx.h */; };
		2B56D2492064E70E00DF2F76 /* car_wall_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F02064E70600DF2F76 /* car_wall_part.cpp */; };
		2B56D24A2064E70E00DF2F76 /* config_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F12064E70600DF2F76 /* config_factory.h */; };
		2B56D24B2064E70E00DF2F76 /* arg_cache.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F22064E70700DF2F76 /* arg_cache.h */; };
		2B56D24C2064E70E00DF2F76 /* ConfigAnalyzer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F32064E70700DF2F76 /* ConfigAnalyzer.cpp */; };
		2B56D24D2064E70E00DF2F76 /* db_visitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F42064E70700DF2F76 /* db_visitor.cpp */; };
		2B56D24E2064E70E00DF2F76 /* ConfigAnalyzer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F52064E70700DF2F76 /* ConfigAnalyzer.h */; };
		2B56D24F2064E70E00DF2F76 /* decoration_vr_interface_lib.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F62064E70800DF2F76 /* decoration_vr_interface_lib.cpp */; };
		2B56D2502064E70E00DF2F76 /* download_visitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1F72064E70800DF2F76 /* download_visitor.cpp */; };
		2B56D2512064E70E00DF2F76 /* BasePartOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F82064E70800DF2F76 /* BasePartOperator.h */; };
		2B56D2522064E70E00DF2F76 /* db_visitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1F92064E70800DF2F76 /* db_visitor.h */; };
		2B56D2532064E70E00DF2F76 /* ElevatorConfigManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1FA2064E70900DF2F76 /* ElevatorConfigManager.h */; };
		2B56D2542064E70E00DF2F76 /* car_top_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FB2064E70900DF2F76 /* car_top_factory.cpp */; };
		2B56D2552064E70E00DF2F76 /* car_top.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FC2064E70900DF2F76 /* car_top.cpp */; };
		2B56D2562064E70E00DF2F76 /* car_wall_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FD2064E70900DF2F76 /* car_wall_factory.cpp */; };
		2B56D2572064E70E00DF2F76 /* elevator_size_parser.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D1FE2064E70A00DF2F76 /* elevator_size_parser.h */; };
		2B56D2582064E70E00DF2F76 /* material_channel_pool.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D1FF2064E70A00DF2F76 /* material_channel_pool.cpp */; };
		2B56D2592064E70E00DF2F76 /* common_part_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2002064E70A00DF2F76 /* common_part_factory.cpp */; };
		2B56D25A2064E70E00DF2F76 /* PartOperatorManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2012064E70A00DF2F76 /* PartOperatorManager.cpp */; };
		2B56D25B2064E70E00DF2F76 /* FrontWallTypeOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2022064E70B00DF2F76 /* FrontWallTypeOperator.h */; };
		2B56D25C2064E70E00DF2F76 /* PartTypeManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2032064E70B00DF2F76 /* PartTypeManager.cpp */; };
		2B56D25D2064E70E00DF2F76 /* car_top_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2042064E70B00DF2F76 /* car_top_factory.h */; };
		2B56D25E2064E70E00DF2F76 /* ElevatorPartOperator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2052064E70B00DF2F76 /* ElevatorPartOperator.cpp */; };
		2B56D2602064E70E00DF2F76 /* Util.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B56D2072064E70C00DF2F76 /* Util.cpp */; };
		2B56D2612064E70E00DF2F76 /* Util.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2082064E70C00DF2F76 /* Util.h */; };
		2B56D2622064E70E00DF2F76 /* common_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D2092064E70C00DF2F76 /* common_part.h */; };
		2B56D2632064E70E00DF2F76 /* ElevatorConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20A2064E70D00DF2F76 /* ElevatorConfig.h */; };
		2B56D2642064E70E00DF2F76 /* ElevatorPartOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20B2064E70D00DF2F76 /* ElevatorPartOperator.h */; };
		2B56D2652064E70E00DF2F76 /* IPartTypeManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20C2064E70D00DF2F76 /* IPartTypeManager.h */; };
		2B56D2662064E70E00DF2F76 /* BaseVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B56D20D2064E70D00DF2F76 /* BaseVisitor.h */; };
		2B72CC6E23C57A1700682713 /* Sel_Door_Imported.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6523C57A1600682713 /* Sel_Door_Imported.cpp */; };
		2B72CC6F23C57A1700682713 /* Sel_HallDoor_Imported.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6623C57A1600682713 /* Sel_HallDoor_Imported.cpp */; };
		2B72CC7023C57A1700682713 /* Sel_HallDoor_Imported.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6723C57A1600682713 /* Sel_HallDoor_Imported.h */; };
		2B72CC7123C57A1700682713 /* Sel_Shaft.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6823C57A1700682713 /* Sel_Shaft.cpp */; };
		2B72CC7223C57A1700682713 /* Sel_Shaft.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6923C57A1700682713 /* Sel_Shaft.h */; };
		2B72CC7323C57A1700682713 /* Sel_Bottom_Accessory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6A23C57A1700682713 /* Sel_Bottom_Accessory.h */; };
		2B72CC7423C57A1700682713 /* Sel_Door_Imported.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6B23C57A1700682713 /* Sel_Door_Imported.h */; };
		2B72CC7523C57A1700682713 /* Sel_Car_Shell.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC6C23C57A1700682713 /* Sel_Car_Shell.h */; };
		2B72CC7623C57A1700682713 /* Sel_Car_Shell.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC6D23C57A1700682713 /* Sel_Car_Shell.cpp */; };
		2B72CC7D23C57A3500682713 /* VrCarShellVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC7723C57A3400682713 /* VrCarShellVisitor.cpp */; };
		2B72CC7E23C57A3500682713 /* VrHallShaftVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC7823C57A3400682713 /* VrHallShaftVisitor.cpp */; };
		2B72CC7F23C57A3500682713 /* VrCarShellVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC7923C57A3400682713 /* VrCarShellVisitor.h */; };
		2B72CC8023C57A3500682713 /* VrHallShaftVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC7A23C57A3400682713 /* VrHallShaftVisitor.h */; };
		2B72CC8123C57A3500682713 /* VrHallVisitor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC7B23C57A3500682713 /* VrHallVisitor.cpp */; };
		2B72CC8223C57A3500682713 /* VrHallVisitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC7C23C57A3500682713 /* VrHallVisitor.h */; };
		2B72CC8723C57A7400682713 /* hall_room_part_factory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC8323C57A7400682713 /* hall_room_part_factory.h */; };
		2B72CC8823C57A7400682713 /* hall_room_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC8423C57A7400682713 /* hall_room_part.cpp */; };
		2B72CC8923C57A7400682713 /* hall_room_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B72CC8523C57A7400682713 /* hall_room_part.h */; };
		2B72CC8A23C57A7400682713 /* hall_room_part_factory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2B72CC8623C57A7400682713 /* hall_room_part_factory.cpp */; };
		2BBE12D3222F633D0003D940 /* Sel_Bottom_Accessory.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BBE12D2222F633D0003D940 /* Sel_Bottom_Accessory.cpp */; };
		2BC10E6A207465DA00126085 /* svr_common_part.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E4E207465D500126085 /* svr_common_part.cpp */; };
		2BC10E6B207465DA00126085 /* svr_material_channel.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E4F207465D500126085 /* svr_material_channel.cpp */; };
		2BC10E6C207465DA00126085 /* svr_part_basic_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E50207465D600126085 /* svr_part_basic_info.h */; };
		2BC10E6D207465DA00126085 /* svr_car_wall.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E51207465D600126085 /* svr_car_wall.cpp */; };
		2BC10E6E207465DA00126085 /* svr_file_digital_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E52207465D600126085 /* svr_file_digital_info.h */; };
		2BC10E6F207465DA00126085 /* svr_file_digital_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E53207465D600126085 /* svr_file_digital_info.cpp */; };
		2BC10E70207465DA00126085 /* svr_material_channel.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E54207465D600126085 /* svr_material_channel.h */; };
		2BC10E71207465DA00126085 /* svr_json_helper.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E55207465D600126085 /* svr_json_helper.cpp */; };
		2BC10E72207465DA00126085 /* svr_wall_size_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E56207465D700126085 /* svr_wall_size_info.cpp */; };
		2BC10E73207465DA00126085 /* svr_material.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E57207465D700126085 /* svr_material.cpp */; };
		2BC10E74207465DA00126085 /* svr_json_helper.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E58207465D700126085 /* svr_json_helper.h */; };
		2BC10E75207465DA00126085 /* svr_config.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E59207465D700126085 /* svr_config.cpp */; };
		2BC10E76207465DA00126085 /* svr_material_special_rule.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5A207465D700126085 /* svr_material_special_rule.h */; };
		2BC10E77207465DA00126085 /* svr_common_part.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5B207465D700126085 /* svr_common_part.h */; };
		2BC10E78207465DA00126085 /* svr_config.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5C207465D800126085 /* svr_config.h */; };
		2BC10E7A207465DA00126085 /* svr_part_basic_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E5E207465D800126085 /* svr_part_basic_info.cpp */; };
		2BC10E7B207465DA00126085 /* svr_part_material.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E5F207465D800126085 /* svr_part_material.h */; };
		2BC10E7C207465DA00126085 /* svr_part_model_info.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E60207465D800126085 /* svr_part_model_info.cpp */; };
		2BC10E7D207465DA00126085 /* svr_part_model_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E61207465D800126085 /* svr_part_model_info.h */; };
		2BC10E7E207465DA00126085 /* svr_material_special_rule.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E62207465D900126085 /* svr_material_special_rule.cpp */; };
		2BC10E7F207465DA00126085 /* svr_material.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E63207465D900126085 /* svr_material.h */; };
		2BC10E80207465DA00126085 /* svr_wall_element.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E64207465D900126085 /* svr_wall_element.h */; };
		2BC10E81207465DA00126085 /* svr_wall_size_info.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E65207465D900126085 /* svr_wall_size_info.h */; };
		2BC10E82207465DA00126085 /* svr_car_wall.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E66207465D900126085 /* svr_car_wall.h */; };
		2BC10E83207465DA00126085 /* svr_wall_element.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E67207465DA00126085 /* svr_wall_element.cpp */; };
		2BC10E84207465DA00126085 /* svr_base_data.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E68207465DA00126085 /* svr_base_data.h */; };
		2BC10E85207465DA00126085 /* svr_part_material.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E69207465DA00126085 /* svr_part_material.cpp */; };
		2BC10E8A207466BE00126085 /* BaseFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E89207466BE00126085 /* BaseFactory.h */; };
		2BC10E8F207466E200126085 /* sha256.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E8B207466E200126085 /* sha256.h */; };
		2BC10E90207466E200126085 /* sha256.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E8C207466E200126085 /* sha256.cpp */; };
		2BC10E91207466E200126085 /* lru_cache.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BC10E8D207466E200126085 /* lru_cache.cpp */; };
		2BC10E92207466E200126085 /* lru_cache.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BC10E8E207466E200126085 /* lru_cache.h */; };
		2BED8C0F228BE75400AEF4D0 /* download_assist.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2BED8C0B228BE75400AEF4D0 /* download_assist.cpp */; };
		2BED8C10228BE75400AEF4D0 /* download_assist.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BED8C0C228BE75400AEF4D0 /* download_assist.h */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2B56D0122064D14600DF2F76 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = D049E5251ADF6B7C004DC3D1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2B56CF492064C90800DF2F76;
			remoteInfo = DecorationVrInterfaceDll_mbcs;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		2B4CB06B25B80B3B004B0767 /* Copy Files */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			name = "Copy Files";
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B56CF482064C90800DF2F76 /* Copy Files */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			name = "Copy Files";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2B07918F2134E46000A65EE1 /* EscaAccessOverVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaAccessOverVisitor.cpp; sourceTree = "<group>"; };
		2B0791902134E46000A65EE1 /* EscaAccessOverVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaAccessOverVisitor.h; sourceTree = "<group>"; };
		2B0791912134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaAcessOverExpendTypeVisitor.cpp; sourceTree = "<group>"; };
		2B0791922134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaAcessOverExpendTypeVisitor.h; sourceTree = "<group>"; };
		2B0791932134E46000A65EE1 /* EscaAcessOverFlagVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaAcessOverFlagVisitor.cpp; sourceTree = "<group>"; };
		2B0791942134E46000A65EE1 /* EscaAcessOverFlagVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaAcessOverFlagVisitor.h; sourceTree = "<group>"; };
		2B0791952134E46000A65EE1 /* EscaBalustradeVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaBalustradeVisitor.cpp; sourceTree = "<group>"; };
		2B0791962134E46100A65EE1 /* EscaBalustradeVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaBalustradeVisitor.h; sourceTree = "<group>"; };
		2B0791972134E46100A65EE1 /* EscaCombLightingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaCombLightingVisitor.cpp; sourceTree = "<group>"; };
		2B0791982134E46100A65EE1 /* EscaCombLightingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaCombLightingVisitor.h; sourceTree = "<group>"; };
		2B0791992134E46100A65EE1 /* EscaCombVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaCombVisitor.cpp; sourceTree = "<group>"; };
		2B07919A2134E46100A65EE1 /* EscaCombVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaCombVisitor.h; sourceTree = "<group>"; };
		2B07919B2134E46100A65EE1 /* EscaConfigVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaConfigVisitor.cpp; sourceTree = "<group>"; };
		2B07919C2134E46100A65EE1 /* EscaConfigVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaConfigVisitor.h; sourceTree = "<group>"; };
		2B07919D2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaDeckingCtrlBoxVisitor.cpp; sourceTree = "<group>"; };
		2B07919E2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaDeckingCtrlBoxVisitor.h; sourceTree = "<group>"; };
		2B07919F2134E46100A65EE1 /* EscaDeckingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaDeckingVisitor.cpp; sourceTree = "<group>"; };
		2B0791A02134E46100A65EE1 /* EscaDeckingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaDeckingVisitor.h; sourceTree = "<group>"; };
		2B0791A12134E46100A65EE1 /* EscaHandrailEnterVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaHandrailEnterVisitor.cpp; sourceTree = "<group>"; };
		2B0791A22134E46100A65EE1 /* EscaHandrailEnterVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaHandrailEnterVisitor.h; sourceTree = "<group>"; };
		2B0791A32134E46100A65EE1 /* EscaHandrailGuidVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaHandrailGuidVisitor.cpp; sourceTree = "<group>"; };
		2B0791A42134E46100A65EE1 /* EscaHandrailGuidVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaHandrailGuidVisitor.h; sourceTree = "<group>"; };
		2B0791A52134E46100A65EE1 /* EscaHandrailLightingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaHandrailLightingVisitor.cpp; sourceTree = "<group>"; };
		2B0791A62134E46100A65EE1 /* EscaHandrailLightingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaHandrailLightingVisitor.h; sourceTree = "<group>"; };
		2B0791A72134E46100A65EE1 /* EscaHandrailVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaHandrailVisitor.cpp; sourceTree = "<group>"; };
		2B0791A82134E46100A65EE1 /* EscaHandrailVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaHandrailVisitor.h; sourceTree = "<group>"; };
		2B0791A92134E46100A65EE1 /* EscaPhotoelectricVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaPhotoelectricVisitor.cpp; sourceTree = "<group>"; };
		2B0791AA2134E46100A65EE1 /* EscaPhotoelectricVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaPhotoelectricVisitor.h; sourceTree = "<group>"; };
		2B0791AB2134E46100A65EE1 /* EscaScenesVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaScenesVisitor.cpp; sourceTree = "<group>"; };
		2B0791AC2134E46100A65EE1 /* EscaScenesVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaScenesVisitor.h; sourceTree = "<group>"; };
		2B0791AD2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSideCladdingLightingVisitor.cpp; sourceTree = "<group>"; };
		2B0791AE2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSideCladdingLightingVisitor.h; sourceTree = "<group>"; };
		2B0791AF2134E46100A65EE1 /* EscaSideCladdingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSideCladdingVisitor.cpp; sourceTree = "<group>"; };
		2B0791B02134E46100A65EE1 /* EscaSideCladdingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSideCladdingVisitor.h; sourceTree = "<group>"; };
		2B0791B12134E46100A65EE1 /* EscaSkirtBrushVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSkirtBrushVisitor.cpp; sourceTree = "<group>"; };
		2B0791B22134E46100A65EE1 /* EscaSkirtBrushVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSkirtBrushVisitor.h; sourceTree = "<group>"; };
		2B0791B32134E46100A65EE1 /* EscaSkirtLightingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSkirtLightingVisitor.cpp; sourceTree = "<group>"; };
		2B0791B42134E46100A65EE1 /* EscaSkirtLightingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSkirtLightingVisitor.h; sourceTree = "<group>"; };
		2B0791B52134E46100A65EE1 /* EscaSkirtVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSkirtVisitor.cpp; sourceTree = "<group>"; };
		2B0791B62134E46100A65EE1 /* EscaSkirtVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSkirtVisitor.h; sourceTree = "<group>"; };
		2B0791B72134E46100A65EE1 /* EscaStepLightingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaStepLightingVisitor.cpp; sourceTree = "<group>"; };
		2B0791B82134E46100A65EE1 /* EscaStepLightingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaStepLightingVisitor.h; sourceTree = "<group>"; };
		2B0791B92134E46100A65EE1 /* EscaStepVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaStepVisitor.cpp; sourceTree = "<group>"; };
		2B0791BA2134E46100A65EE1 /* EscaStepVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaStepVisitor.h; sourceTree = "<group>"; };
		2B0791BB2134E46100A65EE1 /* EscaTrafficLightVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaTrafficLightVisitor.cpp; sourceTree = "<group>"; };
		2B0791BC2134E46100A65EE1 /* EscaTrafficLightVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaTrafficLightVisitor.h; sourceTree = "<group>"; };
		2B0791BD2134E46100A65EE1 /* EscaTrussLightingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaTrussLightingVisitor.cpp; sourceTree = "<group>"; };
		2B0791BE2134E46100A65EE1 /* EscaTrussLightingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaTrussLightingVisitor.h; sourceTree = "<group>"; };
		2B0791F02134E47D00A65EE1 /* Global_Camera_EffectParameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Camera_EffectParameter.cpp; sourceTree = "<group>"; };
		2B0791F12134E47D00A65EE1 /* Global_Camera_EffectParameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Camera_EffectParameter.h; sourceTree = "<group>"; };
		2B0791F22134E47D00A65EE1 /* Global_Camera_Position.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Camera_Position.cpp; sourceTree = "<group>"; };
		2B0791F32134E47D00A65EE1 /* Global_Camera_Position.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Camera_Position.h; sourceTree = "<group>"; };
		2B0791F42134E47D00A65EE1 /* Global_Control_Flag.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Control_Flag.cpp; sourceTree = "<group>"; };
		2B0791F52134E47D00A65EE1 /* Global_Control_Flag.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Control_Flag.h; sourceTree = "<group>"; };
		2B0791F62134E47D00A65EE1 /* Global_CopyObject.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_CopyObject.cpp; sourceTree = "<group>"; };
		2B0791F72134E47D00A65EE1 /* Global_CopyObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_CopyObject.h; sourceTree = "<group>"; };
		2B0791F82134E47D00A65EE1 /* Global_Esca_PartMark.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Esca_PartMark.cpp; sourceTree = "<group>"; };
		2B0791F92134E47D00A65EE1 /* Global_Esca_PartMark.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Esca_PartMark.h; sourceTree = "<group>"; };
		2B0791FA2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Esca_Setting_Parameter.cpp; sourceTree = "<group>"; };
		2B0791FB2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Esca_Setting_Parameter.h; sourceTree = "<group>"; };
		2B0791FC2134E47D00A65EE1 /* Global_Escalators_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Escalators_Parameter.cpp; sourceTree = "<group>"; };
		2B0791FD2134E47D00A65EE1 /* Global_Escalators_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Escalators_Parameter.h; sourceTree = "<group>"; };
		2B0791FE2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Escalators_Rung_Parameter.cpp; sourceTree = "<group>"; };
		2B0791FF2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Escalators_Rung_Parameter.h; sourceTree = "<group>"; };
		2B0792002134E47D00A65EE1 /* Global_PartType_Info.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_PartType_Info.cpp; sourceTree = "<group>"; };
		2B0792012134E47D00A65EE1 /* Global_PartType_Info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_PartType_Info.h; sourceTree = "<group>"; };
		2B0792022134E47D00A65EE1 /* Global_Script_Floor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Script_Floor.cpp; sourceTree = "<group>"; };
		2B0792032134E47D00A65EE1 /* Global_Script_Floor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Script_Floor.h; sourceTree = "<group>"; };
		2B0792042134E47D00A65EE1 /* Global_Step0_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Step0_Parameter.cpp; sourceTree = "<group>"; };
		2B0792052134E47D00A65EE1 /* Global_Step0_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Step0_Parameter.h; sourceTree = "<group>"; };
		2B0792062134E47D00A65EE1 /* Global_Step_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Step_Parameter.cpp; sourceTree = "<group>"; };
		2B0792072134E47D00A65EE1 /* Global_Step_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Step_Parameter.h; sourceTree = "<group>"; };
		2B0792082134E47D00A65EE1 /* Global_StepLight0_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_StepLight0_Parameter.cpp; sourceTree = "<group>"; };
		2B0792092134E47D00A65EE1 /* Global_StepLight0_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_StepLight0_Parameter.h; sourceTree = "<group>"; };
		2B07920A2134E47D00A65EE1 /* Global_TestNote.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_TestNote.cpp; sourceTree = "<group>"; };
		2B07920B2134E47D00A65EE1 /* Global_TestNote.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_TestNote.h; sourceTree = "<group>"; };
		2B07920C2134E47D00A65EE1 /* Global_Work0_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Work0_Parameter.cpp; sourceTree = "<group>"; };
		2B07920D2134E47D00A65EE1 /* Global_Work0_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Work0_Parameter.h; sourceTree = "<group>"; };
		2B07920E2134E47D00A65EE1 /* Global_Work1_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Work1_Parameter.cpp; sourceTree = "<group>"; };
		2B07920F2134E47D00A65EE1 /* Global_Work1_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Work1_Parameter.h; sourceTree = "<group>"; };
		2B0792102134E47D00A65EE1 /* Sel_Esca_Access_Cover.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Access_Cover.cpp; sourceTree = "<group>"; };
		2B0792112134E47D00A65EE1 /* Sel_Esca_Access_Cover.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Access_Cover.h; sourceTree = "<group>"; };
		2B0792122134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Access_Cover_ExpendType.cpp; sourceTree = "<group>"; };
		2B0792132134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Access_Cover_ExpendType.h; sourceTree = "<group>"; };
		2B0792142134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Access_Cover_Flag.cpp; sourceTree = "<group>"; };
		2B0792152134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Access_Cover_Flag.h; sourceTree = "<group>"; };
		2B0792162134E47D00A65EE1 /* Sel_Esca_Background.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Background.cpp; sourceTree = "<group>"; };
		2B0792172134E47D00A65EE1 /* Sel_Esca_Background.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Background.h; sourceTree = "<group>"; };
		2B0792182134E47D00A65EE1 /* Sel_Esca_Balustrade.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Balustrade.cpp; sourceTree = "<group>"; };
		2B0792192134E47D00A65EE1 /* Sel_Esca_Balustrade.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Balustrade.h; sourceTree = "<group>"; };
		2B07921A2134E47D00A65EE1 /* Sel_Esca_Comb.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Comb.cpp; sourceTree = "<group>"; };
		2B07921B2134E47D00A65EE1 /* Sel_Esca_Comb.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Comb.h; sourceTree = "<group>"; };
		2B07921C2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Comb_Lighting.cpp; sourceTree = "<group>"; };
		2B07921D2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Comb_Lighting.h; sourceTree = "<group>"; };
		2B07921E2134E47D00A65EE1 /* Sel_Esca_Decking.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Decking.cpp; sourceTree = "<group>"; };
		2B07921F2134E47D00A65EE1 /* Sel_Esca_Decking.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Decking.h; sourceTree = "<group>"; };
		2B0792202134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Decking_Ctrl_Box.cpp; sourceTree = "<group>"; };
		2B0792212134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Decking_Ctrl_Box.h; sourceTree = "<group>"; };
		2B0792222134E47D00A65EE1 /* Sel_Esca_Handrail.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Handrail.cpp; sourceTree = "<group>"; };
		2B0792232134E47D00A65EE1 /* Sel_Esca_Handrail.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Handrail.h; sourceTree = "<group>"; };
		2B0792242134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Handrail_Enter.cpp; sourceTree = "<group>"; };
		2B0792252134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Handrail_Enter.h; sourceTree = "<group>"; };
		2B0792262134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Handrail_Guid.cpp; sourceTree = "<group>"; };
		2B0792272134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Handrail_Guid.h; sourceTree = "<group>"; };
		2B0792282134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Handrail_Lighting.cpp; sourceTree = "<group>"; };
		2B0792292134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Handrail_Lighting.h; sourceTree = "<group>"; };
		2B07922A2134E47D00A65EE1 /* Sel_Esca_Photoelectric.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Photoelectric.cpp; sourceTree = "<group>"; };
		2B07922B2134E47D00A65EE1 /* Sel_Esca_Photoelectric.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Photoelectric.h; sourceTree = "<group>"; };
		2B07922C2134E47D00A65EE1 /* Sel_Esca_Scene.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Scene.cpp; sourceTree = "<group>"; };
		2B07922D2134E47D00A65EE1 /* Sel_Esca_Scene.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Scene.h; sourceTree = "<group>"; };
		2B07922E2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Side_Cladding.cpp; sourceTree = "<group>"; };
		2B07922F2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Side_Cladding.h; sourceTree = "<group>"; };
		2B0792302134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Side_Cladding_Lighting.cpp; sourceTree = "<group>"; };
		2B0792312134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Side_Cladding_Lighting.h; sourceTree = "<group>"; };
		2B0792322134E47D00A65EE1 /* Sel_Esca_Skirt.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Skirt.cpp; sourceTree = "<group>"; };
		2B0792332134E47D00A65EE1 /* Sel_Esca_Skirt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Skirt.h; sourceTree = "<group>"; };
		2B0792342134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Skirt_Brush.cpp; sourceTree = "<group>"; };
		2B0792352134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Skirt_Brush.h; sourceTree = "<group>"; };
		2B0792362134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Skirt_Lighting.cpp; sourceTree = "<group>"; };
		2B0792372134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Skirt_Lighting.h; sourceTree = "<group>"; };
		2B0792382134E47D00A65EE1 /* Sel_Esca_Step.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Step.cpp; sourceTree = "<group>"; };
		2B0792392134E47D00A65EE1 /* Sel_Esca_Step.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Step.h; sourceTree = "<group>"; };
		2B07923A2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Step_Lighting.cpp; sourceTree = "<group>"; };
		2B07923B2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Step_Lighting.h; sourceTree = "<group>"; };
		2B07923C2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Traffic_Light.cpp; sourceTree = "<group>"; };
		2B07923D2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Traffic_Light.h; sourceTree = "<group>"; };
		2B07923E2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Esca_Truss_Lighting.cpp; sourceTree = "<group>"; };
		2B07923F2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Esca_Truss_Lighting.h; sourceTree = "<group>"; };
		2B0792402134E47D00A65EE1 /* Sel_RGB_Share.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_RGB_Share.cpp; sourceTree = "<group>"; };
		2B0792412134E47D00A65EE1 /* Sel_RGB_Share.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_RGB_Share.h; sourceTree = "<group>"; };
		2B0792942134E51400A65EE1 /* EscalatorSize.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscalatorSize.h; sourceTree = "<group>"; };
		2B0792952134E51400A65EE1 /* EscalatorSize.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscalatorSize.cpp; sourceTree = "<group>"; };
		2B0792962134E51400A65EE1 /* dvi_tinyxml2.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = dvi_tinyxml2.cpp; sourceTree = "<group>"; };
		2B0792972134E51400A65EE1 /* dvi_tinyxml2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dvi_tinyxml2.h; sourceTree = "<group>"; };
		2B07929D2134E5AE00A65EE1 /* esca_elevator_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = esca_elevator_part.cpp; sourceTree = "<group>"; };
		2B07929E2134E5AE00A65EE1 /* esca_elevator_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = esca_elevator_part.h; sourceTree = "<group>"; };
		2B07929F2134E5AE00A65EE1 /* esca_elevator_part_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = esca_elevator_part_factory.cpp; sourceTree = "<group>"; };
		2B0792A02134E5AE00A65EE1 /* esca_elevator_part_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = esca_elevator_part_factory.h; sourceTree = "<group>"; };
		2B0792A12134E5AE00A65EE1 /* EscaAcessOverFactory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaAcessOverFactory.cpp; sourceTree = "<group>"; };
		2B0792A22134E5AE00A65EE1 /* EscaAcessOverFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaAcessOverFactory.h; sourceTree = "<group>"; };
		2B0792A32134E5AE00A65EE1 /* EscaBalustradeFactory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaBalustradeFactory.cpp; sourceTree = "<group>"; };
		2B0792A42134E5AE00A65EE1 /* EscaBalustradeFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaBalustradeFactory.h; sourceTree = "<group>"; };
		2B0792A52134E5AE00A65EE1 /* EscaHandrailEnterFactory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaHandrailEnterFactory.cpp; sourceTree = "<group>"; };
		2B0792A62134E5AE00A65EE1 /* EscaHandrailEnterFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaHandrailEnterFactory.h; sourceTree = "<group>"; };
		2B0792A72134E5AE00A65EE1 /* EscaSideCladdingFactory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSideCladdingFactory.cpp; sourceTree = "<group>"; };
		2B0792A82134E5AE00A65EE1 /* EscaSideCladdingFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSideCladdingFactory.h; sourceTree = "<group>"; };
		2B0792A92134E5AE00A65EE1 /* EscaSkirtLightingFactory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = EscaSkirtLightingFactory.cpp; sourceTree = "<group>"; };
		2B0792AA2134E5AE00A65EE1 /* EscaSkirtLightingFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EscaSkirtLightingFactory.h; sourceTree = "<group>"; };
		2B4CB14D25B80B3B004B0767 /* libDecorationVrInterfaceDll_mbcs_xio.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDecorationVrInterfaceDll_mbcs_xio.a; sourceTree = BUILT_PRODUCTS_DIR; };
		2B4CB14E25B80D76004B0767 /* VrHallWallVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallWallVisitor.cpp; sourceTree = "<group>"; };
		2B4CB14F25B80D76004B0767 /* VrHallFloorVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallFloorVisitor.cpp; sourceTree = "<group>"; };
		2B4CB15025B80D76004B0767 /* VrHallFloorVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallFloorVisitor.h; sourceTree = "<group>"; };
		2B4CB15125B80D76004B0767 /* VrHallWallVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallWallVisitor.h; sourceTree = "<group>"; };
		2B56CF4A2064C90800DF2F76 /* libDecorationVrInterfaceDll_mbcs_x6b64asd.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libDecorationVrInterfaceDll_mbcs_x6b64asd.a; sourceTree = BUILT_PRODUCTS_DIR; };
		2B56D0612064E4CA00DF2F76 /* decoration_vr_interface_lib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = decoration_vr_interface_lib.h; sourceTree = "<group>"; };
		2B56D0622064E4CA00DF2F76 /* i_decoration_vr_array_visitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = i_decoration_vr_array_visitor.h; sourceTree = "<group>"; };
		2B56D0632064E4CA00DF2F76 /* i_decoration_vr_callback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = i_decoration_vr_callback.h; sourceTree = "<group>"; };
		2B56D0642064E4CA00DF2F76 /* i_decoration_vr_interface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = i_decoration_vr_interface.h; sourceTree = "<group>"; };
		2B56D0652064E4CA00DF2F76 /* i_decoration_vr_interface_extend.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = i_decoration_vr_interface_extend.h; sourceTree = "<group>"; };
		2B56D0662064E4CA00DF2F76 /* MsgParaValue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MsgParaValue.h; sourceTree = "<group>"; };
		2B56D0672064E4CA00DF2F76 /* part_type.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = part_type.h; sourceTree = "<group>"; };
		2B56D0732064E4E800DF2F76 /* allocator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = allocator.h; sourceTree = "<group>"; };
		2B56D0742064E4E800DF2F76 /* assertions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = assertions.h; sourceTree = "<group>"; };
		2B56D0752064E4E800DF2F76 /* autolink.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = autolink.h; sourceTree = "<group>"; };
		2B56D0762064E4E800DF2F76 /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		2B56D0772064E4E800DF2F76 /* features.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = features.h; sourceTree = "<group>"; };
		2B56D0782064E4E800DF2F76 /* forwards.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = forwards.h; sourceTree = "<group>"; };
		2B56D0792064E4E800DF2F76 /* json.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = json.h; sourceTree = "<group>"; };
		2B56D07A2064E4E800DF2F76 /* reader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = reader.h; sourceTree = "<group>"; };
		2B56D07B2064E4E800DF2F76 /* value.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = value.h; sourceTree = "<group>"; };
		2B56D07C2064E4E800DF2F76 /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; };
		2B56D07D2064E4E800DF2F76 /* writer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = writer.h; sourceTree = "<group>"; };
		2B56D0852064E4E800DF2F76 /* json_reader.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = json_reader.cpp; sourceTree = "<group>"; };
		2B56D0862064E4E800DF2F76 /* json_tool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = json_tool.h; sourceTree = "<group>"; };
		2B56D0872064E4E800DF2F76 /* json_value.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = json_value.cpp; sourceTree = "<group>"; };
		2B56D0882064E4E800DF2F76 /* json_valueiterator.inl */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = json_valueiterator.inl; sourceTree = "<group>"; };
		2B56D0892064E4E800DF2F76 /* json_writer.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = json_writer.cpp; sourceTree = "<group>"; };
		2B56D0A42064E54C00DF2F76 /* HttpAsyncManage.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = HttpAsyncManage.cpp; sourceTree = "<group>"; };
		2B56D0A52064E54C00DF2F76 /* HttpAsyncManage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HttpAsyncManage.h; sourceTree = "<group>"; };
		2B56D0E02064E58F00DF2F76 /* DGBaseVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = DGBaseVisitor.cpp; sourceTree = "<group>"; };
		2B56D0E12064E58F00DF2F76 /* DGBaseVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DGBaseVisitor.h; sourceTree = "<group>"; };
		2B56D0E22064E58F00DF2F76 /* Global_Hwndmsg.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Hwndmsg.cpp; sourceTree = "<group>"; };
		2B56D0E32064E58F00DF2F76 /* Global_Hwndmsg.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Hwndmsg.h; sourceTree = "<group>"; };
		2B56D0E42064E58F00DF2F76 /* Global_Loading_Scence.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Loading_Scence.cpp; sourceTree = "<group>"; };
		2B56D0E52064E58F00DF2F76 /* Global_Loading_Scence.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Loading_Scence.h; sourceTree = "<group>"; };
		2B56D0E62064E58F00DF2F76 /* Global_Select.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Select.cpp; sourceTree = "<group>"; };
		2B56D0E72064E58F00DF2F76 /* Global_Select.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Select.h; sourceTree = "<group>"; };
		2B56D0E82064E58F00DF2F76 /* Global_Setting_Car.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Setting_Car.cpp; sourceTree = "<group>"; };
		2B56D0E92064E58F00DF2F76 /* Global_Setting_Car.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Setting_Car.h; sourceTree = "<group>"; };
		2B56D0EA2064E58F00DF2F76 /* Global_Setting_Hall.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Setting_Hall.cpp; sourceTree = "<group>"; };
		2B56D0EB2064E58F00DF2F76 /* Global_Setting_Hall.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Setting_Hall.h; sourceTree = "<group>"; };
		2B56D0EC2064E58F00DF2F76 /* Global_Setting_Parameter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Global_Setting_Parameter.cpp; sourceTree = "<group>"; };
		2B56D0ED2064E58F00DF2F76 /* Global_Setting_Parameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global_Setting_Parameter.h; sourceTree = "<group>"; };
		2B56D0EE2064E58F00DF2F76 /* material_channel.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = material_channel.cpp; sourceTree = "<group>"; };
		2B56D0EF2064E58F00DF2F76 /* material_channel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = material_channel.h; sourceTree = "<group>"; };
		2B56D0F02064E58F00DF2F76 /* ModelCarWallElem.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ModelCarWallElem.cpp; sourceTree = "<group>"; };
		2B56D0F12064E58F00DF2F76 /* ModelCarWallElem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ModelCarWallElem.h; sourceTree = "<group>"; };
		2B56D0F22064E58F00DF2F76 /* Sel_Accessory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Accessory.cpp; sourceTree = "<group>"; };
		2B56D0F32064E58F00DF2F76 /* Sel_Accessory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Accessory.h; sourceTree = "<group>"; };
		2B56D0F42064E58F00DF2F76 /* Sel_CarIndicator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_CarIndicator.cpp; sourceTree = "<group>"; };
		2B56D0F52064E58F00DF2F76 /* Sel_CarIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_CarIndicator.h; sourceTree = "<group>"; };
		2B56D0F62064E58F00DF2F76 /* Sel_Ceiling.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Ceiling.cpp; sourceTree = "<group>"; };
		2B56D0F72064E58F00DF2F76 /* Sel_Ceiling.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Ceiling.h; sourceTree = "<group>"; };
		2B56D0F82064E58F00DF2F76 /* Sel_Cop.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Cop.cpp; sourceTree = "<group>"; };
		2B56D0F92064E58F00DF2F76 /* Sel_Cop.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Cop.h; sourceTree = "<group>"; };
		2B56D0FA2064E58F00DF2F76 /* Sel_CopLcd.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_CopLcd.cpp; sourceTree = "<group>"; };
		2B56D0FB2064E58F00DF2F76 /* Sel_CopLcd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_CopLcd.h; sourceTree = "<group>"; };
		2B56D0FC2064E58F00DF2F76 /* Sel_Hall.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Hall.cpp; sourceTree = "<group>"; };
		2B56D0FD2064E58F00DF2F76 /* Sel_Hall.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Hall.h; sourceTree = "<group>"; };
		2B56D0FE2064E58F00DF2F76 /* Sel_HandRail.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_HandRail.cpp; sourceTree = "<group>"; };
		2B56D0FF2064E58F00DF2F76 /* Sel_HandRail.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_HandRail.h; sourceTree = "<group>"; };
		2B56D1002064E58F00DF2F76 /* Sel_HI.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_HI.cpp; sourceTree = "<group>"; };
		2B56D1012064E58F00DF2F76 /* Sel_HI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_HI.h; sourceTree = "<group>"; };
		2B56D1022064E58F00DF2F76 /* Sel_HILcd.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_HILcd.cpp; sourceTree = "<group>"; };
		2B56D1032064E58F00DF2F76 /* Sel_HILcd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_HILcd.h; sourceTree = "<group>"; };
		2B56D1042064E58F00DF2F76 /* Sel_HL.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_HL.cpp; sourceTree = "<group>"; };
		2B56D1052064E58F00DF2F76 /* Sel_HL.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_HL.h; sourceTree = "<group>"; };
		2B56D1062064E58F00DF2F76 /* Sel_HLLcd.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_HLLcd.cpp; sourceTree = "<group>"; };
		2B56D1072064E58F00DF2F76 /* Sel_HLLcd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_HLLcd.h; sourceTree = "<group>"; };
		2B56D1082064E58F00DF2F76 /* Sel_Jamb.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Jamb.cpp; sourceTree = "<group>"; };
		2B56D1092064E58F00DF2F76 /* Sel_Jamb.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Jamb.h; sourceTree = "<group>"; };
		2B56D10A2064E58F00DF2F76 /* Sel_Lop.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Lop.cpp; sourceTree = "<group>"; };
		2B56D10B2064E58F00DF2F76 /* Sel_Lop.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Lop.h; sourceTree = "<group>"; };
		2B56D10C2064E58F00DF2F76 /* Sel_LopLcd.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_LopLcd.cpp; sourceTree = "<group>"; };
		2B56D10D2064E58F00DF2F76 /* Sel_LopLcd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_LopLcd.h; sourceTree = "<group>"; };
		2B56D10E2064E58F00DF2F76 /* Sel_Mirror.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Mirror.cpp; sourceTree = "<group>"; };
		2B56D10F2064E58F00DF2F76 /* Sel_Mirror.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Mirror.h; sourceTree = "<group>"; };
		2B56D1102064E58F00DF2F76 /* IVrVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IVrVisitor.h; sourceTree = "<group>"; };
		2B56D1112064E58F00DF2F76 /* vr_controller.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = vr_controller.cpp; sourceTree = "<group>"; };
		2B56D1122064E58F00DF2F76 /* vr_controller.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vr_controller.h; sourceTree = "<group>"; };
		2B56D1132064E58F00DF2F76 /* vr_visitor_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = vr_visitor_factory.cpp; sourceTree = "<group>"; };
		2B56D1142064E58F00DF2F76 /* vr_visitor_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vr_visitor_factory.h; sourceTree = "<group>"; };
		2B56D1152064E58F00DF2F76 /* VrAccessoryVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrAccessoryVisitor.cpp; sourceTree = "<group>"; };
		2B56D1162064E58F00DF2F76 /* VrAccessoryVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrAccessoryVisitor.h; sourceTree = "<group>"; };
		2B56D1172064E58F00DF2F76 /* VrBottomVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrBottomVisitor.cpp; sourceTree = "<group>"; };
		2B56D1182064E58F00DF2F76 /* VrBottomVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrBottomVisitor.h; sourceTree = "<group>"; };
		2B56D1192064E58F00DF2F76 /* VrCarConfigVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCarConfigVisitor.cpp; sourceTree = "<group>"; };
		2B56D11A2064E58F00DF2F76 /* VrCarConfigVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCarConfigVisitor.h; sourceTree = "<group>"; };
		2B56D11B2064E58F00DF2F76 /* VrCarDoorVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCarDoorVisitor.cpp; sourceTree = "<group>"; };
		2B56D11C2064E58F00DF2F76 /* VrCarDoorVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCarDoorVisitor.h; sourceTree = "<group>"; };
		2B56D11D2064E58F00DF2F76 /* VrCarIndicatorVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCarIndicatorVisitor.cpp; sourceTree = "<group>"; };
		2B56D11E2064E58F00DF2F76 /* VrCarIndicatorVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCarIndicatorVisitor.h; sourceTree = "<group>"; };
		2B56D11F2064E58F00DF2F76 /* VrCarWallVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCarWallVisitor.cpp; sourceTree = "<group>"; };
		2B56D1202064E58F00DF2F76 /* VrCarWallVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCarWallVisitor.h; sourceTree = "<group>"; };
		2B56D1212064E58F00DF2F76 /* VrChangeArg.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrChangeArg.cpp; sourceTree = "<group>"; };
		2B56D1222064E58F00DF2F76 /* VrChangeArg.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrChangeArg.h; sourceTree = "<group>"; };
		2B56D1232064E58F00DF2F76 /* VrConfigInfo.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrConfigInfo.cpp; sourceTree = "<group>"; };
		2B56D1242064E58F00DF2F76 /* VrConfigInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrConfigInfo.h; sourceTree = "<group>"; };
		2B56D1252064E58F00DF2F76 /* VrCopDisplayVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCopDisplayVisitor.cpp; sourceTree = "<group>"; };
		2B56D1262064E58F00DF2F76 /* VrCopDisplayVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCopDisplayVisitor.h; sourceTree = "<group>"; };
		2B56D1272064E58F00DF2F76 /* VrCopVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCopVisitor.cpp; sourceTree = "<group>"; };
		2B56D1282064E58F00DF2F76 /* VrCopVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCopVisitor.h; sourceTree = "<group>"; };
		2B56D1292064E58F00DF2F76 /* VrFrontWallVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrFrontWallVisitor.cpp; sourceTree = "<group>"; };
		2B56D12A2064E58F00DF2F76 /* VrFrontWallVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrFrontWallVisitor.h; sourceTree = "<group>"; };
		2B56D12B2064E58F00DF2F76 /* VrGlobalInfo.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrGlobalInfo.cpp; sourceTree = "<group>"; };
		2B56D12C2064E58F00DF2F76 /* VrGlobalInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrGlobalInfo.h; sourceTree = "<group>"; };
		2B56D12D2064E58F00DF2F76 /* VrHallConfigVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallConfigVisitor.cpp; sourceTree = "<group>"; };
		2B56D12E2064E58F00DF2F76 /* VrHallConfigVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallConfigVisitor.h; sourceTree = "<group>"; };
		2B56D12F2064E58F00DF2F76 /* VrHallDoorVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallDoorVisitor.cpp; sourceTree = "<group>"; };
		2B56D1302064E58F00DF2F76 /* VrHallDoorVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallDoorVisitor.h; sourceTree = "<group>"; };
		2B56D1312064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallIndicatorDisplayVisitor.cpp; sourceTree = "<group>"; };
		2B56D1322064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallIndicatorDisplayVisitor.h; sourceTree = "<group>"; };
		2B56D1332064E58F00DF2F76 /* VrHallIndicatorVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallIndicatorVisitor.cpp; sourceTree = "<group>"; };
		2B56D1342064E58F00DF2F76 /* VrHallIndicatorVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallIndicatorVisitor.h; sourceTree = "<group>"; };
		2B56D1352064E58F00DF2F76 /* VrHandrailVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHandrailVisitor.cpp; sourceTree = "<group>"; };
		2B56D1362064E58F00DF2F76 /* VrHandrailVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHandrailVisitor.h; sourceTree = "<group>"; };
		2B56D1372064E58F00DF2F76 /* VrJambVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrJambVisitor.cpp; sourceTree = "<group>"; };
		2B56D1382064E58F00DF2F76 /* VrJambVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrJambVisitor.h; sourceTree = "<group>"; };
		2B56D1392064E58F00DF2F76 /* VrLanternDisplayVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrLanternDisplayVisitor.cpp; sourceTree = "<group>"; };
		2B56D13A2064E58F00DF2F76 /* VrLanternDisplayVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrLanternDisplayVisitor.h; sourceTree = "<group>"; };
		2B56D13B2064E58F00DF2F76 /* VrLanternVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrLanternVisitor.cpp; sourceTree = "<group>"; };
		2B56D13C2064E58F00DF2F76 /* VrLanternVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrLanternVisitor.h; sourceTree = "<group>"; };
		2B56D13D2064E58F00DF2F76 /* VrLopDisplayVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrLopDisplayVisitor.cpp; sourceTree = "<group>"; };
		2B56D13E2064E58F00DF2F76 /* VrLopDisplayVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrLopDisplayVisitor.h; sourceTree = "<group>"; };
		2B56D13F2064E58F00DF2F76 /* VrLopVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrLopVisitor.cpp; sourceTree = "<group>"; };
		2B56D1402064E58F00DF2F76 /* VrLopVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrLopVisitor.h; sourceTree = "<group>"; };
		2B56D1412064E58F00DF2F76 /* VrMirrorVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrMirrorVisitor.cpp; sourceTree = "<group>"; };
		2B56D1422064E58F00DF2F76 /* VrMirrorVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrMirrorVisitor.h; sourceTree = "<group>"; };
		2B56D1432064E58F00DF2F76 /* VrSightseeingVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrSightseeingVisitor.cpp; sourceTree = "<group>"; };
		2B56D1442064E58F00DF2F76 /* VrSightseeingVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrSightseeingVisitor.h; sourceTree = "<group>"; };
		2B56D1452064E58F00DF2F76 /* VrTopVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrTopVisitor.cpp; sourceTree = "<group>"; };
		2B56D1462064E58F00DF2F76 /* VrTopVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrTopVisitor.h; sourceTree = "<group>"; };
		2B56D1B52064E6F900DF2F76 /* resource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = resource.h; sourceTree = "<group>"; };
		2B56D1B62064E6F900DF2F76 /* skirting_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = skirting_part.h; sourceTree = "<group>"; };
		2B56D1B72064E6F900DF2F76 /* decoration_vr_interface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = decoration_vr_interface.h; sourceTree = "<group>"; };
		2B56D1B82064E6FA00DF2F76 /* electric_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = electric_factory.cpp; sourceTree = "<group>"; };
		2B56D1BA2064E6FA00DF2F76 /* ElevatorSize.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ElevatorSize.cpp; sourceTree = "<group>"; };
		2B56D1BB2064E6FA00DF2F76 /* IElevatorConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IElevatorConfig.h; sourceTree = "<group>"; };
		2B56D1BC2064E6FA00DF2F76 /* ElectricOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ElectricOperator.h; sourceTree = "<group>"; };
		2B56D1BD2064E6FB00DF2F76 /* config_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = config_factory.cpp; sourceTree = "<group>"; };
		2B56D1BE2064E6FB00DF2F76 /* arg_cache.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = arg_cache.cpp; sourceTree = "<group>"; };
		2B56D1BF2064E6FB00DF2F76 /* electric_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = electric_factory.h; sourceTree = "<group>"; };
		2B56D1C02064E6FB00DF2F76 /* ElectricOperator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ElectricOperator.cpp; sourceTree = "<group>"; };
		2B56D1C12064E6FB00DF2F76 /* config_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config_part.h; sourceTree = "<group>"; };
		2B56D1C22064E6FC00DF2F76 /* IDownloadVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IDownloadVisitor.h; sourceTree = "<group>"; };
		2B56D1C32064E6FC00DF2F76 /* ElevatorPartFactoryManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ElevatorPartFactoryManager.h; sourceTree = "<group>"; };
		2B56D1C42064E6FC00DF2F76 /* PartOperatorManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PartOperatorManager.h; sourceTree = "<group>"; };
		2B56D1C52064E6FC00DF2F76 /* car_wall_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = car_wall_part.h; sourceTree = "<group>"; };
		2B56D1C62064E6FC00DF2F76 /* file_pool.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = file_pool.cpp; sourceTree = "<group>"; };
		2B56D1C72064E6FD00DF2F76 /* ConstValueMap.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ConstValueMap.cpp; sourceTree = "<group>"; };
		2B56D1C82064E6FD00DF2F76 /* stdafx.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = stdafx.cpp; sourceTree = "<group>"; };
		2B56D1C92064E6FD00DF2F76 /* decoration_vr_array_visitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = decoration_vr_array_visitor.cpp; sourceTree = "<group>"; };
		2B56D1CA2064E6FD00DF2F76 /* material_channel_pool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = material_channel_pool.h; sourceTree = "<group>"; };
		2B56D1CB2064E6FD00DF2F76 /* IElevatorPart.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IElevatorPart.h; sourceTree = "<group>"; };
		2B56D1CC2064E6FE00DF2F76 /* download_visitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = download_visitor.h; sourceTree = "<group>"; };
		2B56D1CD2064E6FE00DF2F76 /* BasePartOperator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = BasePartOperator.cpp; sourceTree = "<group>"; };
		2B56D1CE2064E6FE00DF2F76 /* file_pool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = file_pool.h; sourceTree = "<group>"; };
		2B56D1CF2064E6FE00DF2F76 /* decoration_vr_interface.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = decoration_vr_interface.cpp; sourceTree = "<group>"; };
		2B56D1D02064E6FF00DF2F76 /* skirting_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = skirting_factory.h; sourceTree = "<group>"; };
		2B56D1D12064E6FF00DF2F76 /* config_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = config_part.cpp; sourceTree = "<group>"; };
		2B56D1D22064E6FF00DF2F76 /* IDbVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IDbVisitor.h; sourceTree = "<group>"; };
		2B56D1D32064E6FF00DF2F76 /* skirting_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = skirting_factory.cpp; sourceTree = "<group>"; };
		2B56D1D42064E70000DF2F76 /* car_top.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = car_top.h; sourceTree = "<group>"; };
		2B56D1D52064E70000DF2F76 /* common_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = common_part.cpp; sourceTree = "<group>"; };
		2B56D1D62064E70000DF2F76 /* GlobalInfoDataCommon.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = GlobalInfoDataCommon.cpp; sourceTree = "<group>"; };
		2B56D1D72064E70000DF2F76 /* IElevatorPartFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IElevatorPartFactory.h; sourceTree = "<group>"; };
		2B56D1D82064E70000DF2F76 /* ElevatorConfig.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ElevatorConfig.cpp; sourceTree = "<group>"; };
		2B56D1D92064E70100DF2F76 /* ConfigOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ConfigOperator.h; sourceTree = "<group>"; };
		2B56D1DA2064E70100DF2F76 /* FrontWallTypeOperator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = FrontWallTypeOperator.cpp; sourceTree = "<group>"; };
		2B56D1DB2064E70100DF2F76 /* IConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IConfig.h; sourceTree = "<group>"; };
		2B56D1DC2064E70200DF2F76 /* ElevatorPartFactoryManager.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ElevatorPartFactoryManager.cpp; sourceTree = "<group>"; };
		2B56D1DD2064E70200DF2F76 /* electric_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = electric_part.cpp; sourceTree = "<group>"; };
		2B56D1DE2064E70200DF2F76 /* skirting_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = skirting_part.cpp; sourceTree = "<group>"; };
		2B56D1DF2064E70200DF2F76 /* ElevatorSize.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ElevatorSize.h; sourceTree = "<group>"; };
		2B56D1E02064E70200DF2F76 /* ConstValueMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ConstValueMap.h; sourceTree = "<group>"; };
		2B56D1E12064E70300DF2F76 /* ElevatorConfigManager.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ElevatorConfigManager.cpp; sourceTree = "<group>"; };
		2B56D1E32064E70300DF2F76 /* PartTypeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PartTypeManager.h; sourceTree = "<group>"; };
		2B56D1E52064E70400DF2F76 /* GlobalInfoDataCommon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GlobalInfoDataCommon.h; sourceTree = "<group>"; };
		2B56D1E62064E70400DF2F76 /* electric_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = electric_part.h; sourceTree = "<group>"; };
		2B56D1E72064E70400DF2F76 /* BaseVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = BaseVisitor.cpp; sourceTree = "<group>"; };
		2B56D1E82064E70400DF2F76 /* car_wall_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = car_wall_factory.h; sourceTree = "<group>"; };
		2B56D1E92064E70500DF2F76 /* elevator_size_parser.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = elevator_size_parser.cpp; sourceTree = "<group>"; };
		2B56D1EA2064E70500DF2F76 /* decoration_vr_interface_include.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = decoration_vr_interface_include.h; sourceTree = "<group>"; };
		2B56D1EB2064E70500DF2F76 /* ConfigOperator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ConfigOperator.cpp; sourceTree = "<group>"; };
		2B56D1EC2064E70500DF2F76 /* common_part_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = common_part_factory.h; sourceTree = "<group>"; };
		2B56D1ED2064E70600DF2F76 /* ConstPartInnerParams.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ConstPartInnerParams.h; sourceTree = "<group>"; };
		2B56D1EE2064E70600DF2F76 /* decoration_vr_array_visitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = decoration_vr_array_visitor.h; sourceTree = "<group>"; };
		2B56D1EF2064E70600DF2F76 /* stdafx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stdafx.h; sourceTree = "<group>"; };
		2B56D1F02064E70600DF2F76 /* car_wall_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = car_wall_part.cpp; sourceTree = "<group>"; };
		2B56D1F12064E70600DF2F76 /* config_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config_factory.h; sourceTree = "<group>"; };
		2B56D1F22064E70700DF2F76 /* arg_cache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = arg_cache.h; sourceTree = "<group>"; };
		2B56D1F32064E70700DF2F76 /* ConfigAnalyzer.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ConfigAnalyzer.cpp; sourceTree = "<group>"; };
		2B56D1F42064E70700DF2F76 /* db_visitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = db_visitor.cpp; sourceTree = "<group>"; };
		2B56D1F52064E70700DF2F76 /* ConfigAnalyzer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ConfigAnalyzer.h; sourceTree = "<group>"; };
		2B56D1F62064E70800DF2F76 /* decoration_vr_interface_lib.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = decoration_vr_interface_lib.cpp; sourceTree = "<group>"; };
		2B56D1F72064E70800DF2F76 /* download_visitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = download_visitor.cpp; sourceTree = "<group>"; };
		2B56D1F82064E70800DF2F76 /* BasePartOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BasePartOperator.h; sourceTree = "<group>"; };
		2B56D1F92064E70800DF2F76 /* db_visitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = db_visitor.h; sourceTree = "<group>"; };
		2B56D1FA2064E70900DF2F76 /* ElevatorConfigManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ElevatorConfigManager.h; sourceTree = "<group>"; };
		2B56D1FB2064E70900DF2F76 /* car_top_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = car_top_factory.cpp; sourceTree = "<group>"; };
		2B56D1FC2064E70900DF2F76 /* car_top.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = car_top.cpp; sourceTree = "<group>"; };
		2B56D1FD2064E70900DF2F76 /* car_wall_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = car_wall_factory.cpp; sourceTree = "<group>"; };
		2B56D1FE2064E70A00DF2F76 /* elevator_size_parser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = elevator_size_parser.h; sourceTree = "<group>"; };
		2B56D1FF2064E70A00DF2F76 /* material_channel_pool.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = material_channel_pool.cpp; sourceTree = "<group>"; };
		2B56D2002064E70A00DF2F76 /* common_part_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = common_part_factory.cpp; sourceTree = "<group>"; };
		2B56D2012064E70A00DF2F76 /* PartOperatorManager.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = PartOperatorManager.cpp; sourceTree = "<group>"; };
		2B56D2022064E70B00DF2F76 /* FrontWallTypeOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FrontWallTypeOperator.h; sourceTree = "<group>"; };
		2B56D2032064E70B00DF2F76 /* PartTypeManager.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = PartTypeManager.cpp; sourceTree = "<group>"; };
		2B56D2042064E70B00DF2F76 /* car_top_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = car_top_factory.h; sourceTree = "<group>"; };
		2B56D2052064E70B00DF2F76 /* ElevatorPartOperator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ElevatorPartOperator.cpp; sourceTree = "<group>"; };
		2B56D2072064E70C00DF2F76 /* Util.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Util.cpp; sourceTree = "<group>"; };
		2B56D2082064E70C00DF2F76 /* Util.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Util.h; sourceTree = "<group>"; };
		2B56D2092064E70C00DF2F76 /* common_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = common_part.h; sourceTree = "<group>"; };
		2B56D20A2064E70D00DF2F76 /* ElevatorConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ElevatorConfig.h; sourceTree = "<group>"; };
		2B56D20B2064E70D00DF2F76 /* ElevatorPartOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ElevatorPartOperator.h; sourceTree = "<group>"; };
		2B56D20C2064E70D00DF2F76 /* IPartTypeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IPartTypeManager.h; sourceTree = "<group>"; };
		2B56D20D2064E70D00DF2F76 /* BaseVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseVisitor.h; sourceTree = "<group>"; };
		2B72CC6523C57A1600682713 /* Sel_Door_Imported.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Door_Imported.cpp; sourceTree = "<group>"; };
		2B72CC6623C57A1600682713 /* Sel_HallDoor_Imported.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_HallDoor_Imported.cpp; sourceTree = "<group>"; };
		2B72CC6723C57A1600682713 /* Sel_HallDoor_Imported.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_HallDoor_Imported.h; sourceTree = "<group>"; };
		2B72CC6823C57A1700682713 /* Sel_Shaft.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Shaft.cpp; sourceTree = "<group>"; };
		2B72CC6923C57A1700682713 /* Sel_Shaft.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Shaft.h; sourceTree = "<group>"; };
		2B72CC6A23C57A1700682713 /* Sel_Bottom_Accessory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Bottom_Accessory.h; sourceTree = "<group>"; };
		2B72CC6B23C57A1700682713 /* Sel_Door_Imported.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Door_Imported.h; sourceTree = "<group>"; };
		2B72CC6C23C57A1700682713 /* Sel_Car_Shell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Sel_Car_Shell.h; sourceTree = "<group>"; };
		2B72CC6D23C57A1700682713 /* Sel_Car_Shell.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Car_Shell.cpp; sourceTree = "<group>"; };
		2B72CC7723C57A3400682713 /* VrCarShellVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrCarShellVisitor.cpp; sourceTree = "<group>"; };
		2B72CC7823C57A3400682713 /* VrHallShaftVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallShaftVisitor.cpp; sourceTree = "<group>"; };
		2B72CC7923C57A3400682713 /* VrCarShellVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrCarShellVisitor.h; sourceTree = "<group>"; };
		2B72CC7A23C57A3400682713 /* VrHallShaftVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallShaftVisitor.h; sourceTree = "<group>"; };
		2B72CC7B23C57A3500682713 /* VrHallVisitor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = VrHallVisitor.cpp; sourceTree = "<group>"; };
		2B72CC7C23C57A3500682713 /* VrHallVisitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VrHallVisitor.h; sourceTree = "<group>"; };
		2B72CC8323C57A7400682713 /* hall_room_part_factory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hall_room_part_factory.h; sourceTree = "<group>"; };
		2B72CC8423C57A7400682713 /* hall_room_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = hall_room_part.cpp; sourceTree = "<group>"; };
		2B72CC8523C57A7400682713 /* hall_room_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hall_room_part.h; sourceTree = "<group>"; };
		2B72CC8623C57A7400682713 /* hall_room_part_factory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = hall_room_part_factory.cpp; sourceTree = "<group>"; };
		2BBE12D2222F633D0003D940 /* Sel_Bottom_Accessory.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Sel_Bottom_Accessory.cpp; sourceTree = "<group>"; };
		2BC10E4E207465D500126085 /* svr_common_part.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_common_part.cpp; path = parse_json/svr_common_part.cpp; sourceTree = "<group>"; };
		2BC10E4F207465D500126085 /* svr_material_channel.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_material_channel.cpp; path = parse_json/svr_material_channel.cpp; sourceTree = "<group>"; };
		2BC10E50207465D600126085 /* svr_part_basic_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_part_basic_info.h; path = parse_json/svr_part_basic_info.h; sourceTree = "<group>"; };
		2BC10E51207465D600126085 /* svr_car_wall.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_car_wall.cpp; path = parse_json/svr_car_wall.cpp; sourceTree = "<group>"; };
		2BC10E52207465D600126085 /* svr_file_digital_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_file_digital_info.h; path = parse_json/svr_file_digital_info.h; sourceTree = "<group>"; };
		2BC10E53207465D600126085 /* svr_file_digital_info.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_file_digital_info.cpp; path = parse_json/svr_file_digital_info.cpp; sourceTree = "<group>"; };
		2BC10E54207465D600126085 /* svr_material_channel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_material_channel.h; path = parse_json/svr_material_channel.h; sourceTree = "<group>"; };
		2BC10E55207465D600126085 /* svr_json_helper.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_json_helper.cpp; path = parse_json/svr_json_helper.cpp; sourceTree = "<group>"; };
		2BC10E56207465D700126085 /* svr_wall_size_info.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_wall_size_info.cpp; path = parse_json/svr_wall_size_info.cpp; sourceTree = "<group>"; };
		2BC10E57207465D700126085 /* svr_material.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_material.cpp; path = parse_json/svr_material.cpp; sourceTree = "<group>"; };
		2BC10E58207465D700126085 /* svr_json_helper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_json_helper.h; path = parse_json/svr_json_helper.h; sourceTree = "<group>"; };
		2BC10E59207465D700126085 /* svr_config.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_config.cpp; path = parse_json/svr_config.cpp; sourceTree = "<group>"; };
		2BC10E5A207465D700126085 /* svr_material_special_rule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_material_special_rule.h; path = parse_json/svr_material_special_rule.h; sourceTree = "<group>"; };
		2BC10E5B207465D700126085 /* svr_common_part.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_common_part.h; path = parse_json/svr_common_part.h; sourceTree = "<group>"; };
		2BC10E5C207465D800126085 /* svr_config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_config.h; path = parse_json/svr_config.h; sourceTree = "<group>"; };
		2BC10E5E207465D800126085 /* svr_part_basic_info.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_part_basic_info.cpp; path = parse_json/svr_part_basic_info.cpp; sourceTree = "<group>"; };
		2BC10E5F207465D800126085 /* svr_part_material.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_part_material.h; path = parse_json/svr_part_material.h; sourceTree = "<group>"; };
		2BC10E60207465D800126085 /* svr_part_model_info.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_part_model_info.cpp; path = parse_json/svr_part_model_info.cpp; sourceTree = "<group>"; };
		2BC10E61207465D800126085 /* svr_part_model_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_part_model_info.h; path = parse_json/svr_part_model_info.h; sourceTree = "<group>"; };
		2BC10E62207465D900126085 /* svr_material_special_rule.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_material_special_rule.cpp; path = parse_json/svr_material_special_rule.cpp; sourceTree = "<group>"; };
		2BC10E63207465D900126085 /* svr_material.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_material.h; path = parse_json/svr_material.h; sourceTree = "<group>"; };
		2BC10E64207465D900126085 /* svr_wall_element.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_wall_element.h; path = parse_json/svr_wall_element.h; sourceTree = "<group>"; };
		2BC10E65207465D900126085 /* svr_wall_size_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_wall_size_info.h; path = parse_json/svr_wall_size_info.h; sourceTree = "<group>"; };
		2BC10E66207465D900126085 /* svr_car_wall.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_car_wall.h; path = parse_json/svr_car_wall.h; sourceTree = "<group>"; };
		2BC10E67207465DA00126085 /* svr_wall_element.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_wall_element.cpp; path = parse_json/svr_wall_element.cpp; sourceTree = "<group>"; };
		2BC10E68207465DA00126085 /* svr_base_data.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svr_base_data.h; path = parse_json/svr_base_data.h; sourceTree = "<group>"; };
		2BC10E69207465DA00126085 /* svr_part_material.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = svr_part_material.cpp; path = parse_json/svr_part_material.cpp; sourceTree = "<group>"; };
		2BC10E89207466BE00126085 /* BaseFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BaseFactory.h; path = parse_json/BaseFactory.h; sourceTree = "<group>"; };
		2BC10E8B207466E200126085 /* sha256.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = sha256.h; path = util/sha256.h; sourceTree = "<group>"; };
		2BC10E8C207466E200126085 /* sha256.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = sha256.cpp; path = util/sha256.cpp; sourceTree = "<group>"; };
		2BC10E8D207466E200126085 /* lru_cache.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = lru_cache.cpp; path = util/lru_cache.cpp; sourceTree = "<group>"; };
		2BC10E8E207466E200126085 /* lru_cache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lru_cache.h; path = util/lru_cache.h; sourceTree = "<group>"; };
		2BED8C0B228BE75400AEF4D0 /* download_assist.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = download_assist.cpp; sourceTree = "<group>"; };
		2BED8C0C228BE75400AEF4D0 /* download_assist.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = download_assist.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2B4CB06A25B80B3B004B0767 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B56CF472064C90800DF2F76 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2B07918E2134E46000A65EE1 /* esca */ = {
			isa = PBXGroup;
			children = (
				2B07918F2134E46000A65EE1 /* EscaAccessOverVisitor.cpp */,
				2B0791902134E46000A65EE1 /* EscaAccessOverVisitor.h */,
				2B0791912134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.cpp */,
				2B0791922134E46000A65EE1 /* EscaAcessOverExpendTypeVisitor.h */,
				2B0791932134E46000A65EE1 /* EscaAcessOverFlagVisitor.cpp */,
				2B0791942134E46000A65EE1 /* EscaAcessOverFlagVisitor.h */,
				2B0791952134E46000A65EE1 /* EscaBalustradeVisitor.cpp */,
				2B0791962134E46100A65EE1 /* EscaBalustradeVisitor.h */,
				2B0791972134E46100A65EE1 /* EscaCombLightingVisitor.cpp */,
				2B0791982134E46100A65EE1 /* EscaCombLightingVisitor.h */,
				2B0791992134E46100A65EE1 /* EscaCombVisitor.cpp */,
				2B07919A2134E46100A65EE1 /* EscaCombVisitor.h */,
				2B07919B2134E46100A65EE1 /* EscaConfigVisitor.cpp */,
				2B07919C2134E46100A65EE1 /* EscaConfigVisitor.h */,
				2B07919D2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.cpp */,
				2B07919E2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.h */,
				2B07919F2134E46100A65EE1 /* EscaDeckingVisitor.cpp */,
				2B0791A02134E46100A65EE1 /* EscaDeckingVisitor.h */,
				2B0791A12134E46100A65EE1 /* EscaHandrailEnterVisitor.cpp */,
				2B0791A22134E46100A65EE1 /* EscaHandrailEnterVisitor.h */,
				2B0791A32134E46100A65EE1 /* EscaHandrailGuidVisitor.cpp */,
				2B0791A42134E46100A65EE1 /* EscaHandrailGuidVisitor.h */,
				2B0791A52134E46100A65EE1 /* EscaHandrailLightingVisitor.cpp */,
				2B0791A62134E46100A65EE1 /* EscaHandrailLightingVisitor.h */,
				2B0791A72134E46100A65EE1 /* EscaHandrailVisitor.cpp */,
				2B0791A82134E46100A65EE1 /* EscaHandrailVisitor.h */,
				2B0791A92134E46100A65EE1 /* EscaPhotoelectricVisitor.cpp */,
				2B0791AA2134E46100A65EE1 /* EscaPhotoelectricVisitor.h */,
				2B0791AB2134E46100A65EE1 /* EscaScenesVisitor.cpp */,
				2B0791AC2134E46100A65EE1 /* EscaScenesVisitor.h */,
				2B0791AD2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.cpp */,
				2B0791AE2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.h */,
				2B0791AF2134E46100A65EE1 /* EscaSideCladdingVisitor.cpp */,
				2B0791B02134E46100A65EE1 /* EscaSideCladdingVisitor.h */,
				2B0791B12134E46100A65EE1 /* EscaSkirtBrushVisitor.cpp */,
				2B0791B22134E46100A65EE1 /* EscaSkirtBrushVisitor.h */,
				2B0791B32134E46100A65EE1 /* EscaSkirtLightingVisitor.cpp */,
				2B0791B42134E46100A65EE1 /* EscaSkirtLightingVisitor.h */,
				2B0791B52134E46100A65EE1 /* EscaSkirtVisitor.cpp */,
				2B0791B62134E46100A65EE1 /* EscaSkirtVisitor.h */,
				2B0791B72134E46100A65EE1 /* EscaStepLightingVisitor.cpp */,
				2B0791B82134E46100A65EE1 /* EscaStepLightingVisitor.h */,
				2B0791B92134E46100A65EE1 /* EscaStepVisitor.cpp */,
				2B0791BA2134E46100A65EE1 /* EscaStepVisitor.h */,
				2B0791BB2134E46100A65EE1 /* EscaTrafficLightVisitor.cpp */,
				2B0791BC2134E46100A65EE1 /* EscaTrafficLightVisitor.h */,
				2B0791BD2134E46100A65EE1 /* EscaTrussLightingVisitor.cpp */,
				2B0791BE2134E46100A65EE1 /* EscaTrussLightingVisitor.h */,
			);
			path = esca;
			sourceTree = "<group>";
		};
		2B0791EF2134E47C00A65EE1 /* esca */ = {
			isa = PBXGroup;
			children = (
				2B0791F02134E47D00A65EE1 /* Global_Camera_EffectParameter.cpp */,
				2B0791F12134E47D00A65EE1 /* Global_Camera_EffectParameter.h */,
				2B0791F22134E47D00A65EE1 /* Global_Camera_Position.cpp */,
				2B0791F32134E47D00A65EE1 /* Global_Camera_Position.h */,
				2B0791F42134E47D00A65EE1 /* Global_Control_Flag.cpp */,
				2B0791F52134E47D00A65EE1 /* Global_Control_Flag.h */,
				2B0791F62134E47D00A65EE1 /* Global_CopyObject.cpp */,
				2B0791F72134E47D00A65EE1 /* Global_CopyObject.h */,
				2B0791F82134E47D00A65EE1 /* Global_Esca_PartMark.cpp */,
				2B0791F92134E47D00A65EE1 /* Global_Esca_PartMark.h */,
				2B0791FA2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.cpp */,
				2B0791FB2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.h */,
				2B0791FC2134E47D00A65EE1 /* Global_Escalators_Parameter.cpp */,
				2B0791FD2134E47D00A65EE1 /* Global_Escalators_Parameter.h */,
				2B0791FE2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.cpp */,
				2B0791FF2134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.h */,
				2B0792002134E47D00A65EE1 /* Global_PartType_Info.cpp */,
				2B0792012134E47D00A65EE1 /* Global_PartType_Info.h */,
				2B0792022134E47D00A65EE1 /* Global_Script_Floor.cpp */,
				2B0792032134E47D00A65EE1 /* Global_Script_Floor.h */,
				2B0792042134E47D00A65EE1 /* Global_Step0_Parameter.cpp */,
				2B0792052134E47D00A65EE1 /* Global_Step0_Parameter.h */,
				2B0792062134E47D00A65EE1 /* Global_Step_Parameter.cpp */,
				2B0792072134E47D00A65EE1 /* Global_Step_Parameter.h */,
				2B0792082134E47D00A65EE1 /* Global_StepLight0_Parameter.cpp */,
				2B0792092134E47D00A65EE1 /* Global_StepLight0_Parameter.h */,
				2B07920A2134E47D00A65EE1 /* Global_TestNote.cpp */,
				2B07920B2134E47D00A65EE1 /* Global_TestNote.h */,
				2B07920C2134E47D00A65EE1 /* Global_Work0_Parameter.cpp */,
				2B07920D2134E47D00A65EE1 /* Global_Work0_Parameter.h */,
				2B07920E2134E47D00A65EE1 /* Global_Work1_Parameter.cpp */,
				2B07920F2134E47D00A65EE1 /* Global_Work1_Parameter.h */,
				2B0792102134E47D00A65EE1 /* Sel_Esca_Access_Cover.cpp */,
				2B0792112134E47D00A65EE1 /* Sel_Esca_Access_Cover.h */,
				2B0792122134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.cpp */,
				2B0792132134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.h */,
				2B0792142134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.cpp */,
				2B0792152134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.h */,
				2B0792162134E47D00A65EE1 /* Sel_Esca_Background.cpp */,
				2B0792172134E47D00A65EE1 /* Sel_Esca_Background.h */,
				2B0792182134E47D00A65EE1 /* Sel_Esca_Balustrade.cpp */,
				2B0792192134E47D00A65EE1 /* Sel_Esca_Balustrade.h */,
				2B07921A2134E47D00A65EE1 /* Sel_Esca_Comb.cpp */,
				2B07921B2134E47D00A65EE1 /* Sel_Esca_Comb.h */,
				2B07921C2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.cpp */,
				2B07921D2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.h */,
				2B07921E2134E47D00A65EE1 /* Sel_Esca_Decking.cpp */,
				2B07921F2134E47D00A65EE1 /* Sel_Esca_Decking.h */,
				2B0792202134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.cpp */,
				2B0792212134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.h */,
				2B0792222134E47D00A65EE1 /* Sel_Esca_Handrail.cpp */,
				2B0792232134E47D00A65EE1 /* Sel_Esca_Handrail.h */,
				2B0792242134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.cpp */,
				2B0792252134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.h */,
				2B0792262134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.cpp */,
				2B0792272134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.h */,
				2B0792282134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.cpp */,
				2B0792292134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.h */,
				2B07922A2134E47D00A65EE1 /* Sel_Esca_Photoelectric.cpp */,
				2B07922B2134E47D00A65EE1 /* Sel_Esca_Photoelectric.h */,
				2B07922C2134E47D00A65EE1 /* Sel_Esca_Scene.cpp */,
				2B07922D2134E47D00A65EE1 /* Sel_Esca_Scene.h */,
				2B07922E2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.cpp */,
				2B07922F2134E47D00A65EE1 /* Sel_Esca_Side_Cladding.h */,
				2B0792302134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.cpp */,
				2B0792312134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.h */,
				2B0792322134E47D00A65EE1 /* Sel_Esca_Skirt.cpp */,
				2B0792332134E47D00A65EE1 /* Sel_Esca_Skirt.h */,
				2B0792342134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.cpp */,
				2B0792352134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.h */,
				2B0792362134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.cpp */,
				2B0792372134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.h */,
				2B0792382134E47D00A65EE1 /* Sel_Esca_Step.cpp */,
				2B0792392134E47D00A65EE1 /* Sel_Esca_Step.h */,
				2B07923A2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.cpp */,
				2B07923B2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.h */,
				2B07923C2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.cpp */,
				2B07923D2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.h */,
				2B07923E2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.cpp */,
				2B07923F2134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.h */,
				2B0792402134E47D00A65EE1 /* Sel_RGB_Share.cpp */,
				2B0792412134E47D00A65EE1 /* Sel_RGB_Share.h */,
			);
			path = esca;
			sourceTree = "<group>";
		};
		2B07929C2134E5AE00A65EE1 /* esca */ = {
			isa = PBXGroup;
			children = (
				2B07929D2134E5AE00A65EE1 /* esca_elevator_part.cpp */,
				2B07929E2134E5AE00A65EE1 /* esca_elevator_part.h */,
				2B07929F2134E5AE00A65EE1 /* esca_elevator_part_factory.cpp */,
				2B0792A02134E5AE00A65EE1 /* esca_elevator_part_factory.h */,
				2B0792A12134E5AE00A65EE1 /* EscaAcessOverFactory.cpp */,
				2B0792A22134E5AE00A65EE1 /* EscaAcessOverFactory.h */,
				2B0792A32134E5AE00A65EE1 /* EscaBalustradeFactory.cpp */,
				2B0792A42134E5AE00A65EE1 /* EscaBalustradeFactory.h */,
				2B0792A52134E5AE00A65EE1 /* EscaHandrailEnterFactory.cpp */,
				2B0792A62134E5AE00A65EE1 /* EscaHandrailEnterFactory.h */,
				2B0792A72134E5AE00A65EE1 /* EscaSideCladdingFactory.cpp */,
				2B0792A82134E5AE00A65EE1 /* EscaSideCladdingFactory.h */,
				2B0792A92134E5AE00A65EE1 /* EscaSkirtLightingFactory.cpp */,
				2B0792AA2134E5AE00A65EE1 /* EscaSkirtLightingFactory.h */,
			);
			path = esca;
			sourceTree = "<group>";
		};
		2B56D0602064E4CA00DF2F76 /* include */ = {
			isa = PBXGroup;
			children = (
				2B56D0612064E4CA00DF2F76 /* decoration_vr_interface_lib.h */,
				2B56D0622064E4CA00DF2F76 /* i_decoration_vr_array_visitor.h */,
				2B56D0632064E4CA00DF2F76 /* i_decoration_vr_callback.h */,
				2B56D0642064E4CA00DF2F76 /* i_decoration_vr_interface.h */,
				2B56D0652064E4CA00DF2F76 /* i_decoration_vr_interface_extend.h */,
				2B56D0662064E4CA00DF2F76 /* MsgParaValue.h */,
				2B56D0672064E4CA00DF2F76 /* part_type.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		2B56D06F2064E4E800DF2F76 /* jsoncpp */ = {
			isa = PBXGroup;
			children = (
				2B56D0702064E4E800DF2F76 /* include */,
				2B56D07E2064E4E800DF2F76 /* src */,
			);
			path = jsoncpp;
			sourceTree = "<group>";
		};
		2B56D0702064E4E800DF2F76 /* include */ = {
			isa = PBXGroup;
			children = (
				2B56D0722064E4E800DF2F76 /* json */,
			);
			path = include;
			sourceTree = "<group>";
		};
		2B56D0722064E4E800DF2F76 /* json */ = {
			isa = PBXGroup;
			children = (
				2B56D0732064E4E800DF2F76 /* allocator.h */,
				2B56D0742064E4E800DF2F76 /* assertions.h */,
				2B56D0752064E4E800DF2F76 /* autolink.h */,
				2B56D0762064E4E800DF2F76 /* config.h */,
				2B56D0772064E4E800DF2F76 /* features.h */,
				2B56D0782064E4E800DF2F76 /* forwards.h */,
				2B56D0792064E4E800DF2F76 /* json.h */,
				2B56D07A2064E4E800DF2F76 /* reader.h */,
				2B56D07B2064E4E800DF2F76 /* value.h */,
				2B56D07C2064E4E800DF2F76 /* version.h */,
				2B56D07D2064E4E800DF2F76 /* writer.h */,
			);
			path = json;
			sourceTree = "<group>";
		};
		2B56D07E2064E4E800DF2F76 /* src */ = {
			isa = PBXGroup;
			children = (
				2B56D0832064E4E800DF2F76 /* lib_json */,
			);
			path = src;
			sourceTree = "<group>";
		};
		2B56D0832064E4E800DF2F76 /* lib_json */ = {
			isa = PBXGroup;
			children = (
				2B56D0852064E4E800DF2F76 /* json_reader.cpp */,
				2B56D0862064E4E800DF2F76 /* json_tool.h */,
				2B56D0872064E4E800DF2F76 /* json_value.cpp */,
				2B56D0882064E4E800DF2F76 /* json_valueiterator.inl */,
				2B56D0892064E4E800DF2F76 /* json_writer.cpp */,
			);
			path = lib_json;
			sourceTree = "<group>";
		};
		2B56D0A32064E54C00DF2F76 /* netWork */ = {
			isa = PBXGroup;
			children = (
				2B56D0A42064E54C00DF2F76 /* HttpAsyncManage.cpp */,
				2B56D0A52064E54C00DF2F76 /* HttpAsyncManage.h */,
			);
			path = netWork;
			sourceTree = "<group>";
		};
		2B56D0DE2064E58F00DF2F76 /* vr_visitor */ = {
			isa = PBXGroup;
			children = (
				2B4CB14F25B80D76004B0767 /* VrHallFloorVisitor.cpp */,
				2B4CB15025B80D76004B0767 /* VrHallFloorVisitor.h */,
				2B4CB14E25B80D76004B0767 /* VrHallWallVisitor.cpp */,
				2B4CB15125B80D76004B0767 /* VrHallWallVisitor.h */,
				2B72CC7723C57A3400682713 /* VrCarShellVisitor.cpp */,
				2B72CC7923C57A3400682713 /* VrCarShellVisitor.h */,
				2B72CC7823C57A3400682713 /* VrHallShaftVisitor.cpp */,
				2B72CC7A23C57A3400682713 /* VrHallShaftVisitor.h */,
				2B72CC7B23C57A3500682713 /* VrHallVisitor.cpp */,
				2B72CC7C23C57A3500682713 /* VrHallVisitor.h */,
				2B07918E2134E46000A65EE1 /* esca */,
				2B56D0DF2064E58F00DF2F76 /* array_visitor */,
				2B56D1102064E58F00DF2F76 /* IVrVisitor.h */,
				2B56D1112064E58F00DF2F76 /* vr_controller.cpp */,
				2B56D1122064E58F00DF2F76 /* vr_controller.h */,
				2B56D1132064E58F00DF2F76 /* vr_visitor_factory.cpp */,
				2B56D1142064E58F00DF2F76 /* vr_visitor_factory.h */,
				2B56D1152064E58F00DF2F76 /* VrAccessoryVisitor.cpp */,
				2B56D1162064E58F00DF2F76 /* VrAccessoryVisitor.h */,
				2B56D1172064E58F00DF2F76 /* VrBottomVisitor.cpp */,
				2B56D1182064E58F00DF2F76 /* VrBottomVisitor.h */,
				2B56D1192064E58F00DF2F76 /* VrCarConfigVisitor.cpp */,
				2B56D11A2064E58F00DF2F76 /* VrCarConfigVisitor.h */,
				2B56D11B2064E58F00DF2F76 /* VrCarDoorVisitor.cpp */,
				2B56D11C2064E58F00DF2F76 /* VrCarDoorVisitor.h */,
				2B56D11D2064E58F00DF2F76 /* VrCarIndicatorVisitor.cpp */,
				2B56D11E2064E58F00DF2F76 /* VrCarIndicatorVisitor.h */,
				2B56D11F2064E58F00DF2F76 /* VrCarWallVisitor.cpp */,
				2B56D1202064E58F00DF2F76 /* VrCarWallVisitor.h */,
				2B56D1212064E58F00DF2F76 /* VrChangeArg.cpp */,
				2B56D1222064E58F00DF2F76 /* VrChangeArg.h */,
				2B56D1232064E58F00DF2F76 /* VrConfigInfo.cpp */,
				2B56D1242064E58F00DF2F76 /* VrConfigInfo.h */,
				2B56D1252064E58F00DF2F76 /* VrCopDisplayVisitor.cpp */,
				2B56D1262064E58F00DF2F76 /* VrCopDisplayVisitor.h */,
				2B56D1272064E58F00DF2F76 /* VrCopVisitor.cpp */,
				2B56D1282064E58F00DF2F76 /* VrCopVisitor.h */,
				2B56D1292064E58F00DF2F76 /* VrFrontWallVisitor.cpp */,
				2B56D12A2064E58F00DF2F76 /* VrFrontWallVisitor.h */,
				2B56D12B2064E58F00DF2F76 /* VrGlobalInfo.cpp */,
				2B56D12C2064E58F00DF2F76 /* VrGlobalInfo.h */,
				2B56D12D2064E58F00DF2F76 /* VrHallConfigVisitor.cpp */,
				2B56D12E2064E58F00DF2F76 /* VrHallConfigVisitor.h */,
				2B56D12F2064E58F00DF2F76 /* VrHallDoorVisitor.cpp */,
				2B56D1302064E58F00DF2F76 /* VrHallDoorVisitor.h */,
				2B56D1312064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.cpp */,
				2B56D1322064E58F00DF2F76 /* VrHallIndicatorDisplayVisitor.h */,
				2B56D1332064E58F00DF2F76 /* VrHallIndicatorVisitor.cpp */,
				2B56D1342064E58F00DF2F76 /* VrHallIndicatorVisitor.h */,
				2B56D1352064E58F00DF2F76 /* VrHandrailVisitor.cpp */,
				2B56D1362064E58F00DF2F76 /* VrHandrailVisitor.h */,
				2B56D1372064E58F00DF2F76 /* VrJambVisitor.cpp */,
				2B56D1382064E58F00DF2F76 /* VrJambVisitor.h */,
				2B56D1392064E58F00DF2F76 /* VrLanternDisplayVisitor.cpp */,
				2B56D13A2064E58F00DF2F76 /* VrLanternDisplayVisitor.h */,
				2B56D13B2064E58F00DF2F76 /* VrLanternVisitor.cpp */,
				2B56D13C2064E58F00DF2F76 /* VrLanternVisitor.h */,
				2B56D13D2064E58F00DF2F76 /* VrLopDisplayVisitor.cpp */,
				2B56D13E2064E58F00DF2F76 /* VrLopDisplayVisitor.h */,
				2B56D13F2064E58F00DF2F76 /* VrLopVisitor.cpp */,
				2B56D1402064E58F00DF2F76 /* VrLopVisitor.h */,
				2B56D1412064E58F00DF2F76 /* VrMirrorVisitor.cpp */,
				2B56D1422064E58F00DF2F76 /* VrMirrorVisitor.h */,
				2B56D1432064E58F00DF2F76 /* VrSightseeingVisitor.cpp */,
				2B56D1442064E58F00DF2F76 /* VrSightseeingVisitor.h */,
				2B56D1452064E58F00DF2F76 /* VrTopVisitor.cpp */,
				2B56D1462064E58F00DF2F76 /* VrTopVisitor.h */,
			);
			path = vr_visitor;
			sourceTree = "<group>";
		};
		2B56D0DF2064E58F00DF2F76 /* array_visitor */ = {
			isa = PBXGroup;
			children = (
				2B72CC6A23C57A1700682713 /* Sel_Bottom_Accessory.h */,
				2B72CC6D23C57A1700682713 /* Sel_Car_Shell.cpp */,
				2B72CC6C23C57A1700682713 /* Sel_Car_Shell.h */,
				2B72CC6523C57A1600682713 /* Sel_Door_Imported.cpp */,
				2B72CC6B23C57A1700682713 /* Sel_Door_Imported.h */,
				2B72CC6623C57A1600682713 /* Sel_HallDoor_Imported.cpp */,
				2B72CC6723C57A1600682713 /* Sel_HallDoor_Imported.h */,
				2B72CC6823C57A1700682713 /* Sel_Shaft.cpp */,
				2B72CC6923C57A1700682713 /* Sel_Shaft.h */,
				2BBE12D2222F633D0003D940 /* Sel_Bottom_Accessory.cpp */,
				2B0791EF2134E47C00A65EE1 /* esca */,
				2B56D0E02064E58F00DF2F76 /* DGBaseVisitor.cpp */,
				2B56D0E12064E58F00DF2F76 /* DGBaseVisitor.h */,
				2B56D0E22064E58F00DF2F76 /* Global_Hwndmsg.cpp */,
				2B56D0E32064E58F00DF2F76 /* Global_Hwndmsg.h */,
				2B56D0E42064E58F00DF2F76 /* Global_Loading_Scence.cpp */,
				2B56D0E52064E58F00DF2F76 /* Global_Loading_Scence.h */,
				2B56D0E62064E58F00DF2F76 /* Global_Select.cpp */,
				2B56D0E72064E58F00DF2F76 /* Global_Select.h */,
				2B56D0E82064E58F00DF2F76 /* Global_Setting_Car.cpp */,
				2B56D0E92064E58F00DF2F76 /* Global_Setting_Car.h */,
				2B56D0EA2064E58F00DF2F76 /* Global_Setting_Hall.cpp */,
				2B56D0EB2064E58F00DF2F76 /* Global_Setting_Hall.h */,
				2B56D0EC2064E58F00DF2F76 /* Global_Setting_Parameter.cpp */,
				2B56D0ED2064E58F00DF2F76 /* Global_Setting_Parameter.h */,
				2B56D0EE2064E58F00DF2F76 /* material_channel.cpp */,
				2B56D0EF2064E58F00DF2F76 /* material_channel.h */,
				2B56D0F02064E58F00DF2F76 /* ModelCarWallElem.cpp */,
				2B56D0F12064E58F00DF2F76 /* ModelCarWallElem.h */,
				2B56D0F22064E58F00DF2F76 /* Sel_Accessory.cpp */,
				2B56D0F32064E58F00DF2F76 /* Sel_Accessory.h */,
				2B56D0F42064E58F00DF2F76 /* Sel_CarIndicator.cpp */,
				2B56D0F52064E58F00DF2F76 /* Sel_CarIndicator.h */,
				2B56D0F62064E58F00DF2F76 /* Sel_Ceiling.cpp */,
				2B56D0F72064E58F00DF2F76 /* Sel_Ceiling.h */,
				2B56D0F82064E58F00DF2F76 /* Sel_Cop.cpp */,
				2B56D0F92064E58F00DF2F76 /* Sel_Cop.h */,
				2B56D0FA2064E58F00DF2F76 /* Sel_CopLcd.cpp */,
				2B56D0FB2064E58F00DF2F76 /* Sel_CopLcd.h */,
				2B56D0FC2064E58F00DF2F76 /* Sel_Hall.cpp */,
				2B56D0FD2064E58F00DF2F76 /* Sel_Hall.h */,
				2B56D0FE2064E58F00DF2F76 /* Sel_HandRail.cpp */,
				2B56D0FF2064E58F00DF2F76 /* Sel_HandRail.h */,
				2B56D1002064E58F00DF2F76 /* Sel_HI.cpp */,
				2B56D1012064E58F00DF2F76 /* Sel_HI.h */,
				2B56D1022064E58F00DF2F76 /* Sel_HILcd.cpp */,
				2B56D1032064E58F00DF2F76 /* Sel_HILcd.h */,
				2B56D1042064E58F00DF2F76 /* Sel_HL.cpp */,
				2B56D1052064E58F00DF2F76 /* Sel_HL.h */,
				2B56D1062064E58F00DF2F76 /* Sel_HLLcd.cpp */,
				2B56D1072064E58F00DF2F76 /* Sel_HLLcd.h */,
				2B56D1082064E58F00DF2F76 /* Sel_Jamb.cpp */,
				2B56D1092064E58F00DF2F76 /* Sel_Jamb.h */,
				2B56D10A2064E58F00DF2F76 /* Sel_Lop.cpp */,
				2B56D10B2064E58F00DF2F76 /* Sel_Lop.h */,
				2B56D10C2064E58F00DF2F76 /* Sel_LopLcd.cpp */,
				2B56D10D2064E58F00DF2F76 /* Sel_LopLcd.h */,
				2B56D10E2064E58F00DF2F76 /* Sel_Mirror.cpp */,
				2B56D10F2064E58F00DF2F76 /* Sel_Mirror.h */,
			);
			path = array_visitor;
			sourceTree = "<group>";
		};
		2BC10E4C207460FD00126085 /* impl */ = {
			isa = PBXGroup;
			children = (
				2B72CC8623C57A7400682713 /* hall_room_part_factory.cpp */,
				2B72CC8323C57A7400682713 /* hall_room_part_factory.h */,
				2B72CC8423C57A7400682713 /* hall_room_part.cpp */,
				2B72CC8523C57A7400682713 /* hall_room_part.h */,
				2BED8C0B228BE75400AEF4D0 /* download_assist.cpp */,
				2BED8C0C228BE75400AEF4D0 /* download_assist.h */,
				2B0792962134E51400A65EE1 /* dvi_tinyxml2.cpp */,
				2B0792972134E51400A65EE1 /* dvi_tinyxml2.h */,
				2B0792952134E51400A65EE1 /* EscalatorSize.cpp */,
				2B0792942134E51400A65EE1 /* EscalatorSize.h */,
				2B56D1CD2064E6FE00DF2F76 /* BasePartOperator.cpp */,
				2B56D1F82064E70800DF2F76 /* BasePartOperator.h */,
				2B56D1E72064E70400DF2F76 /* BaseVisitor.cpp */,
				2B56D20D2064E70D00DF2F76 /* BaseVisitor.h */,
				2B56D1FB2064E70900DF2F76 /* car_top_factory.cpp */,
				2B56D2042064E70B00DF2F76 /* car_top_factory.h */,
				2B56D1FC2064E70900DF2F76 /* car_top.cpp */,
				2B56D1D42064E70000DF2F76 /* car_top.h */,
				2B56D1FD2064E70900DF2F76 /* car_wall_factory.cpp */,
				2B56D1E82064E70400DF2F76 /* car_wall_factory.h */,
				2B56D1F02064E70600DF2F76 /* car_wall_part.cpp */,
				2B56D1C52064E6FC00DF2F76 /* car_wall_part.h */,
				2B56D2002064E70A00DF2F76 /* common_part_factory.cpp */,
				2B56D1EC2064E70500DF2F76 /* common_part_factory.h */,
				2B56D1D52064E70000DF2F76 /* common_part.cpp */,
				2B56D2092064E70C00DF2F76 /* common_part.h */,
				2B56D1BD2064E6FB00DF2F76 /* config_factory.cpp */,
				2B56D1F12064E70600DF2F76 /* config_factory.h */,
				2B56D1D12064E6FF00DF2F76 /* config_part.cpp */,
				2B56D1C12064E6FB00DF2F76 /* config_part.h */,
				2B56D1F32064E70700DF2F76 /* ConfigAnalyzer.cpp */,
				2B56D1F52064E70700DF2F76 /* ConfigAnalyzer.h */,
				2B56D1EB2064E70500DF2F76 /* ConfigOperator.cpp */,
				2B56D1D92064E70100DF2F76 /* ConfigOperator.h */,
				2B56D1ED2064E70600DF2F76 /* ConstPartInnerParams.h */,
				2B56D1C72064E6FD00DF2F76 /* ConstValueMap.cpp */,
				2B56D1E02064E70200DF2F76 /* ConstValueMap.h */,
				2B56D1F42064E70700DF2F76 /* db_visitor.cpp */,
				2B56D1F92064E70800DF2F76 /* db_visitor.h */,
				2B56D1C92064E6FD00DF2F76 /* decoration_vr_array_visitor.cpp */,
				2B56D1EE2064E70600DF2F76 /* decoration_vr_array_visitor.h */,
				2B56D1EA2064E70500DF2F76 /* decoration_vr_interface_include.h */,
				2B56D1F62064E70800DF2F76 /* decoration_vr_interface_lib.cpp */,
				2B56D1CF2064E6FE00DF2F76 /* decoration_vr_interface.cpp */,
				2B56D1B72064E6F900DF2F76 /* decoration_vr_interface.h */,
				2B56D1F72064E70800DF2F76 /* download_visitor.cpp */,
				2B56D1CC2064E6FE00DF2F76 /* download_visitor.h */,
				2B56D1B82064E6FA00DF2F76 /* electric_factory.cpp */,
				2B56D1BF2064E6FB00DF2F76 /* electric_factory.h */,
				2B56D1DD2064E70200DF2F76 /* electric_part.cpp */,
				2B56D1E62064E70400DF2F76 /* electric_part.h */,
				2B56D1C02064E6FB00DF2F76 /* ElectricOperator.cpp */,
				2B56D1BC2064E6FA00DF2F76 /* ElectricOperator.h */,
				2B56D1E92064E70500DF2F76 /* elevator_size_parser.cpp */,
				2B56D1FE2064E70A00DF2F76 /* elevator_size_parser.h */,
				2B56D1D82064E70000DF2F76 /* ElevatorConfig.cpp */,
				2B56D20A2064E70D00DF2F76 /* ElevatorConfig.h */,
				2B56D1E12064E70300DF2F76 /* ElevatorConfigManager.cpp */,
				2B56D1FA2064E70900DF2F76 /* ElevatorConfigManager.h */,
				2B56D1DC2064E70200DF2F76 /* ElevatorPartFactoryManager.cpp */,
				2B56D1C32064E6FC00DF2F76 /* ElevatorPartFactoryManager.h */,
				2B56D2052064E70B00DF2F76 /* ElevatorPartOperator.cpp */,
				2B56D20B2064E70D00DF2F76 /* ElevatorPartOperator.h */,
				2B56D1BA2064E6FA00DF2F76 /* ElevatorSize.cpp */,
				2B56D1DF2064E70200DF2F76 /* ElevatorSize.h */,
				2B56D1C62064E6FC00DF2F76 /* file_pool.cpp */,
				2B56D1CE2064E6FE00DF2F76 /* file_pool.h */,
				2B56D1DA2064E70100DF2F76 /* FrontWallTypeOperator.cpp */,
				2B56D2022064E70B00DF2F76 /* FrontWallTypeOperator.h */,
				2B56D1D62064E70000DF2F76 /* GlobalInfoDataCommon.cpp */,
				2B56D1E52064E70400DF2F76 /* GlobalInfoDataCommon.h */,
				2B56D1DB2064E70100DF2F76 /* IConfig.h */,
				2B56D1D22064E6FF00DF2F76 /* IDbVisitor.h */,
				2B56D1C22064E6FC00DF2F76 /* IDownloadVisitor.h */,
				2B56D1BB2064E6FA00DF2F76 /* IElevatorConfig.h */,
				2B56D1CB2064E6FD00DF2F76 /* IElevatorPart.h */,
				2B56D1D72064E70000DF2F76 /* IElevatorPartFactory.h */,
				2B56D20C2064E70D00DF2F76 /* IPartTypeManager.h */,
				2B56D1FF2064E70A00DF2F76 /* material_channel_pool.cpp */,
				2B56D1CA2064E6FD00DF2F76 /* material_channel_pool.h */,
				2B56D2012064E70A00DF2F76 /* PartOperatorManager.cpp */,
				2B56D1C42064E6FC00DF2F76 /* PartOperatorManager.h */,
				2B56D2032064E70B00DF2F76 /* PartTypeManager.cpp */,
				2B56D1E32064E70300DF2F76 /* PartTypeManager.h */,
				2B56D1B52064E6F900DF2F76 /* resource.h */,
				2B56D1D32064E6FF00DF2F76 /* skirting_factory.cpp */,
				2B56D1D02064E6FF00DF2F76 /* skirting_factory.h */,
				2B56D1DE2064E70200DF2F76 /* skirting_part.cpp */,
				2B56D1B62064E6F900DF2F76 /* skirting_part.h */,
				2B56D1C82064E6FD00DF2F76 /* stdafx.cpp */,
				2B56D1EF2064E70600DF2F76 /* stdafx.h */,
				2B56D2072064E70C00DF2F76 /* Util.cpp */,
				2B56D2082064E70C00DF2F76 /* Util.h */,
				2B56D1BE2064E6FB00DF2F76 /* arg_cache.cpp */,
				2B56D1F22064E70700DF2F76 /* arg_cache.h */,
			);
			name = impl;
			sourceTree = "<group>";
		};
		D049E5241ADF6B7C004DC3D1 = {
			isa = PBXGroup;
			children = (
				2B07929C2134E5AE00A65EE1 /* esca */,
				2BC10E8D207466E200126085 /* lru_cache.cpp */,
				2BC10E8E207466E200126085 /* lru_cache.h */,
				2BC10E8C207466E200126085 /* sha256.cpp */,
				2BC10E8B207466E200126085 /* sha256.h */,
				2BC10E89207466BE00126085 /* BaseFactory.h */,
				2BC10E68207465DA00126085 /* svr_base_data.h */,
				2BC10E51207465D600126085 /* svr_car_wall.cpp */,
				2BC10E66207465D900126085 /* svr_car_wall.h */,
				2BC10E4E207465D500126085 /* svr_common_part.cpp */,
				2BC10E5B207465D700126085 /* svr_common_part.h */,
				2BC10E59207465D700126085 /* svr_config.cpp */,
				2BC10E5C207465D800126085 /* svr_config.h */,
				2BC10E53207465D600126085 /* svr_file_digital_info.cpp */,
				2BC10E52207465D600126085 /* svr_file_digital_info.h */,
				2BC10E55207465D600126085 /* svr_json_helper.cpp */,
				2BC10E58207465D700126085 /* svr_json_helper.h */,
				2BC10E4F207465D500126085 /* svr_material_channel.cpp */,
				2BC10E54207465D600126085 /* svr_material_channel.h */,
				2BC10E62207465D900126085 /* svr_material_special_rule.cpp */,
				2BC10E5A207465D700126085 /* svr_material_special_rule.h */,
				2BC10E57207465D700126085 /* svr_material.cpp */,
				2BC10E63207465D900126085 /* svr_material.h */,
				2BC10E5E207465D800126085 /* svr_part_basic_info.cpp */,
				2BC10E50207465D600126085 /* svr_part_basic_info.h */,
				2BC10E69207465DA00126085 /* svr_part_material.cpp */,
				2BC10E5F207465D800126085 /* svr_part_material.h */,
				2BC10E60207465D800126085 /* svr_part_model_info.cpp */,
				2BC10E61207465D800126085 /* svr_part_model_info.h */,
				2BC10E67207465DA00126085 /* svr_wall_element.cpp */,
				2BC10E64207465D900126085 /* svr_wall_element.h */,
				2BC10E56207465D700126085 /* svr_wall_size_info.cpp */,
				2BC10E65207465D900126085 /* svr_wall_size_info.h */,
				2BC10E4C207460FD00126085 /* impl */,
				2B56D0602064E4CA00DF2F76 /* include */,
				2B56D06F2064E4E800DF2F76 /* jsoncpp */,
				2B56D0DE2064E58F00DF2F76 /* vr_visitor */,
				2B56D0A32064E54C00DF2F76 /* netWork */,
				D049E52E1ADF6B7C004DC3D1 /* Products */,
			);
			sourceTree = "<group>";
		};
		D049E52E1ADF6B7C004DC3D1 /* Products */ = {
			isa = PBXGroup;
			children = (
				2B56CF4A2064C90800DF2F76 /* libDecorationVrInterfaceDll_mbcs_x6b64asd.a */,
				2B4CB14D25B80B3B004B0767 /* libDecorationVrInterfaceDll_mbcs_xio.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		2B4CB06C25B80B3B004B0767 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B4CB06D25B80B3B004B0767 /* VrLanternDisplayVisitor.h in Headers */,
				2B4CB06E25B80B3B004B0767 /* VrLanternVisitor.h in Headers */,
				2B4CB06F25B80B3B004B0767 /* ModelCarWallElem.h in Headers */,
				2B4CB07025B80B3B004B0767 /* GlobalInfoDataCommon.h in Headers */,
				2B4CB07125B80B3B004B0767 /* Sel_Esca_Skirt.h in Headers */,
				2B4CB07225B80B3B004B0767 /* Sel_CopLcd.h in Headers */,
				2B4CB07325B80B3B004B0767 /* EscaHandrailEnterVisitor.h in Headers */,
				2B4CB07425B80B3B004B0767 /* VrCopVisitor.h in Headers */,
				2B4CB07525B80B3B004B0767 /* skirting_factory.h in Headers */,
				2B4CB07625B80B3B004B0767 /* VrMirrorVisitor.h in Headers */,
				2B4CB07725B80B3B004B0767 /* VrLopVisitor.h in Headers */,
				2B4CB07825B80B3B004B0767 /* EscaHandrailGuidVisitor.h in Headers */,
				2B4CB07925B80B3B004B0767 /* VrJambVisitor.h in Headers */,
				2B4CB07A25B80B3B004B0767 /* EscaTrussLightingVisitor.h in Headers */,
				2B4CB07B25B80B3B004B0767 /* Sel_Esca_Background.h in Headers */,
				2B4CB07C25B80B3B004B0767 /* EscaConfigVisitor.h in Headers */,
				2B4CB07D25B80B3B004B0767 /* esca_elevator_part_factory.h in Headers */,
				2B4CB07E25B80B3B004B0767 /* Sel_Esca_Balustrade.h in Headers */,
				2B4CB07F25B80B3B004B0767 /* svr_wall_element.h in Headers */,
				2B4CB08025B80B3B004B0767 /* EscaDeckingVisitor.h in Headers */,
				2B4CB08125B80B3B004B0767 /* stdafx.h in Headers */,
				2B4CB08225B80B3B004B0767 /* VrLopDisplayVisitor.h in Headers */,
				2B4CB08325B80B3B004B0767 /* Sel_Esca_Handrail_Enter.h in Headers */,
				2B4CB08425B80B3B004B0767 /* EscaPhotoelectricVisitor.h in Headers */,
				2B4CB08525B80B3B004B0767 /* Util.h in Headers */,
				2B4CB08625B80B3B004B0767 /* db_visitor.h in Headers */,
				2B4CB08725B80B3B004B0767 /* ConstValueMap.h in Headers */,
				2B4CB08825B80B3B004B0767 /* Sel_HILcd.h in Headers */,
				2B4CB08925B80B3B004B0767 /* svr_part_model_info.h in Headers */,
				2B4CB08A25B80B3B004B0767 /* Sel_Hall.h in Headers */,
				2B4CB08B25B80B3B004B0767 /* Sel_Cop.h in Headers */,
				2B4CB08C25B80B3B004B0767 /* skirting_part.h in Headers */,
				2B4CB08D25B80B3B004B0767 /* download_visitor.h in Headers */,
				2B4CB08E25B80B3B004B0767 /* Sel_RGB_Share.h in Headers */,
				2B4CB08F25B80B3B004B0767 /* IElevatorPart.h in Headers */,
				2B4CB09025B80B3B004B0767 /* Sel_Door_Imported.h in Headers */,
				2B4CB09125B80B3B004B0767 /* assertions.h in Headers */,
				2B4CB09225B80B3B004B0767 /* Sel_Mirror.h in Headers */,
				2B4CB09325B80B3B004B0767 /* EscaCombLightingVisitor.h in Headers */,
				2B4CB09425B80B3B004B0767 /* common_part.h in Headers */,
				2B4CB09525B80B3B004B0767 /* hall_room_part_factory.h in Headers */,
				2B4CB09625B80B3B004B0767 /* autolink.h in Headers */,
				2B4CB09725B80B3B004B0767 /* Global_Setting_Parameter.h in Headers */,
				2B4CB09825B80B3B004B0767 /* ElevatorPartOperator.h in Headers */,
				2B4CB09925B80B3B004B0767 /* EscaSkirtBrushVisitor.h in Headers */,
				2B4CB09A25B80B3B004B0767 /* VrSightseeingVisitor.h in Headers */,
				2B4CB09B25B80B3B004B0767 /* json.h in Headers */,
				2B4CB09C25B80B3B004B0767 /* Global_Escalators_Rung_Parameter.h in Headers */,
				2B4CB09D25B80B3B004B0767 /* i_decoration_vr_interface.h in Headers */,
				2B4CB09E25B80B3B004B0767 /* json_tool.h in Headers */,
				2B4CB09F25B80B3B004B0767 /* VrHallIndicatorDisplayVisitor.h in Headers */,
				2B4CB0A025B80B3B004B0767 /* Sel_Bottom_Accessory.h in Headers */,
				2B4CB0A125B80B3B004B0767 /* Sel_Esca_Photoelectric.h in Headers */,
				2B4CB0A225B80B3B004B0767 /* esca_elevator_part.h in Headers */,
				2B4CB0A325B80B3B004B0767 /* Global_Script_Floor.h in Headers */,
				2B4CB0A425B80B3B004B0767 /* EscaSideCladdingFactory.h in Headers */,
				2B4CB0A525B80B3B004B0767 /* VrCarConfigVisitor.h in Headers */,
				2B4CB0A625B80B3B004B0767 /* version.h in Headers */,
				2B4CB0A725B80B3B004B0767 /* EscaAcessOverExpendTypeVisitor.h in Headers */,
				2B4CB0A825B80B3B004B0767 /* VrCarWallVisitor.h in Headers */,
				2B4CB0A925B80B3B004B0767 /* EscaSkirtLightingFactory.h in Headers */,
				2B4CB0AA25B80B3B004B0767 /* IDownloadVisitor.h in Headers */,
				2B4CB0AB25B80B3B004B0767 /* Sel_Car_Shell.h in Headers */,
				2B4CB15725B80D77004B0767 /* VrHallFloorVisitor.h in Headers */,
				2B4CB0AC25B80B3B004B0767 /* svr_part_material.h in Headers */,
				2B4CB0AD25B80B3B004B0767 /* elevator_size_parser.h in Headers */,
				2B4CB0AE25B80B3B004B0767 /* VrCarIndicatorVisitor.h in Headers */,
				2B4CB0AF25B80B3B004B0767 /* VrFrontWallVisitor.h in Headers */,
				2B4CB0B025B80B3B004B0767 /* Global_Camera_Position.h in Headers */,
				2B4CB0B125B80B3B004B0767 /* EscaDeckingCtrlBoxVisitor.h in Headers */,
				2B4CB0B225B80B3B004B0767 /* Sel_HL.h in Headers */,
				2B4CB0B325B80B3B004B0767 /* Sel_Esca_Handrail_Guid.h in Headers */,
				2B4CB0B425B80B3B004B0767 /* EscaAcessOverFactory.h in Headers */,
				2B4CB0B525B80B3B004B0767 /* Global_Step_Parameter.h in Headers */,
				2B4CB0B625B80B3B004B0767 /* VrBottomVisitor.h in Headers */,
				2B4CB0B725B80B3B004B0767 /* VrHallConfigVisitor.h in Headers */,
				2B4CB0B825B80B3B004B0767 /* vr_controller.h in Headers */,
				2B4CB0B925B80B3B004B0767 /* EscaSkirtVisitor.h in Headers */,
				2B4CB15925B80D77004B0767 /* VrHallWallVisitor.h in Headers */,
				2B4CB0BA25B80B3B004B0767 /* EscaStepLightingVisitor.h in Headers */,
				2B4CB0BB25B80B3B004B0767 /* part_type.h in Headers */,
				2B4CB0BC25B80B3B004B0767 /* svr_part_basic_info.h in Headers */,
				2B4CB0BD25B80B3B004B0767 /* Sel_Esca_Access_Cover_Flag.h in Headers */,
				2B4CB0BE25B80B3B004B0767 /* PartTypeManager.h in Headers */,
				2B4CB0BF25B80B3B004B0767 /* Global_Loading_Scence.h in Headers */,
				2B4CB0C025B80B3B004B0767 /* EscaHandrailLightingVisitor.h in Headers */,
				2B4CB0C125B80B3B004B0767 /* file_pool.h in Headers */,
				2B4CB0C225B80B3B004B0767 /* VrCarDoorVisitor.h in Headers */,
				2B4CB0C325B80B3B004B0767 /* Sel_Esca_Step_Lighting.h in Headers */,
				2B4CB0C425B80B3B004B0767 /* IDbVisitor.h in Headers */,
				2B4CB0C525B80B3B004B0767 /* Sel_HallDoor_Imported.h in Headers */,
				2B4CB0C625B80B3B004B0767 /* BaseFactory.h in Headers */,
				2B4CB0C725B80B3B004B0767 /* sha256.h in Headers */,
				2B4CB0C825B80B3B004B0767 /* svr_base_data.h in Headers */,
				2B4CB0C925B80B3B004B0767 /* ConfigOperator.h in Headers */,
				2B4CB0CA25B80B3B004B0767 /* Global_StepLight0_Parameter.h in Headers */,
				2B4CB0CB25B80B3B004B0767 /* Sel_Esca_Truss_Lighting.h in Headers */,
				2B4CB0CC25B80B3B004B0767 /* allocator.h in Headers */,
				2B4CB0CD25B80B3B004B0767 /* VrCopDisplayVisitor.h in Headers */,
				2B4CB0CE25B80B3B004B0767 /* Global_Camera_EffectParameter.h in Headers */,
				2B4CB0CF25B80B3B004B0767 /* Sel_Esca_Access_Cover.h in Headers */,
				2B4CB0D025B80B3B004B0767 /* arg_cache.h in Headers */,
				2B4CB0D125B80B3B004B0767 /* decoration_vr_interface_lib.h in Headers */,
				2B4CB0D225B80B3B004B0767 /* VrChangeArg.h in Headers */,
				2B4CB0D325B80B3B004B0767 /* VrHallVisitor.h in Headers */,
				2B4CB0D425B80B3B004B0767 /* config_factory.h in Headers */,
				2B4CB0D525B80B3B004B0767 /* VrCarShellVisitor.h in Headers */,
				2B4CB0D625B80B3B004B0767 /* Sel_Esca_Side_Cladding_Lighting.h in Headers */,
				2B4CB0D725B80B3B004B0767 /* ElevatorConfig.h in Headers */,
				2B4CB0D825B80B3B004B0767 /* VrConfigInfo.h in Headers */,
				2B4CB0D925B80B3B004B0767 /* decoration_vr_array_visitor.h in Headers */,
				2B4CB0DA25B80B3B004B0767 /* svr_material.h in Headers */,
				2B4CB0DB25B80B3B004B0767 /* EscaAcessOverFlagVisitor.h in Headers */,
				2B4CB0DC25B80B3B004B0767 /* Sel_Esca_Skirt_Lighting.h in Headers */,
				2B4CB0DD25B80B3B004B0767 /* Sel_Esca_Comb.h in Headers */,
				2B4CB0DE25B80B3B004B0767 /* VrHallShaftVisitor.h in Headers */,
				2B4CB0DF25B80B3B004B0767 /* ElevatorPartFactoryManager.h in Headers */,
				2B4CB0E025B80B3B004B0767 /* svr_json_helper.h in Headers */,
				2B4CB0E125B80B3B004B0767 /* ConfigAnalyzer.h in Headers */,
				2B4CB0E225B80B3B004B0767 /* VrHandrailVisitor.h in Headers */,
				2B4CB0E325B80B3B004B0767 /* car_wall_part.h in Headers */,
				2B4CB0E425B80B3B004B0767 /* VrGlobalInfo.h in Headers */,
				2B4CB0E525B80B3B004B0767 /* Sel_Jamb.h in Headers */,
				2B4CB0E625B80B3B004B0767 /* BasePartOperator.h in Headers */,
				2B4CB0E725B80B3B004B0767 /* Sel_Lop.h in Headers */,
				2B4CB0E825B80B3B004B0767 /* IVrVisitor.h in Headers */,
				2B4CB0E925B80B3B004B0767 /* Sel_LopLcd.h in Headers */,
				2B4CB0EA25B80B3B004B0767 /* Global_Esca_Setting_Parameter.h in Headers */,
				2B4CB0EB25B80B3B004B0767 /* ConstPartInnerParams.h in Headers */,
				2B4CB0EC25B80B3B004B0767 /* DGBaseVisitor.h in Headers */,
				2B4CB0ED25B80B3B004B0767 /* Sel_HandRail.h in Headers */,
				2B4CB0EE25B80B3B004B0767 /* MsgParaValue.h in Headers */,
				2B4CB0EF25B80B3B004B0767 /* EscaHandrailVisitor.h in Headers */,
				2B4CB0F025B80B3B004B0767 /* HttpAsyncManage.h in Headers */,
				2B4CB0F125B80B3B004B0767 /* electric_part.h in Headers */,
				2B4CB0F225B80B3B004B0767 /* Global_PartType_Info.h in Headers */,
				2B4CB0F325B80B3B004B0767 /* Sel_Esca_Step.h in Headers */,
				2B4CB0F425B80B3B004B0767 /* ElevatorSize.h in Headers */,
				2B4CB0F525B80B3B004B0767 /* Sel_HLLcd.h in Headers */,
				2B4CB0F625B80B3B004B0767 /* VrHallDoorVisitor.h in Headers */,
				2B4CB0F725B80B3B004B0767 /* Sel_Shaft.h in Headers */,
				2B4CB0F825B80B3B004B0767 /* Global_Setting_Car.h in Headers */,
				2B4CB0F925B80B3B004B0767 /* Global_Step0_Parameter.h in Headers */,
				2B4CB0FA25B80B3B004B0767 /* reader.h in Headers */,
				2B4CB0FB25B80B3B004B0767 /* decoration_vr_interface.h in Headers */,
				2B4CB0FC25B80B3B004B0767 /* config_part.h in Headers */,
				2B4CB0FD25B80B3B004B0767 /* EscaHandrailEnterFactory.h in Headers */,
				2B4CB0FE25B80B3B004B0767 /* svr_material_special_rule.h in Headers */,
				2B4CB0FF25B80B3B004B0767 /* EscaSideCladdingVisitor.h in Headers */,
				2B4CB10025B80B3B004B0767 /* vr_visitor_factory.h in Headers */,
				2B4CB10125B80B3B004B0767 /* svr_car_wall.h in Headers */,
				2B4CB10225B80B3B004B0767 /* PartOperatorManager.h in Headers */,
				2B4CB10325B80B3B004B0767 /* resource.h in Headers */,
				2B4CB10425B80B3B004B0767 /* common_part_factory.h in Headers */,
				2B4CB10525B80B3B004B0767 /* ElevatorConfigManager.h in Headers */,
				2B4CB10625B80B3B004B0767 /* VrAccessoryVisitor.h in Headers */,
				2B4CB10725B80B3B004B0767 /* EscalatorSize.h in Headers */,
				2B4CB10825B80B3B004B0767 /* ElectricOperator.h in Headers */,
				2B4CB10925B80B3B004B0767 /* Global_Esca_PartMark.h in Headers */,
				2B4CB10A25B80B3B004B0767 /* IPartTypeManager.h in Headers */,
				2B4CB10B25B80B3B004B0767 /* Sel_CarIndicator.h in Headers */,
				2B4CB10C25B80B3B004B0767 /* IConfig.h in Headers */,
				2B4CB10D25B80B3B004B0767 /* svr_common_part.h in Headers */,
				2B4CB10E25B80B3B004B0767 /* Global_Select.h in Headers */,
				2B4CB10F25B80B3B004B0767 /* EscaBalustradeVisitor.h in Headers */,
				2B4CB11025B80B3B004B0767 /* svr_file_digital_info.h in Headers */,
				2B4CB11125B80B3B004B0767 /* VrTopVisitor.h in Headers */,
				2B4CB11225B80B3B004B0767 /* Global_Setting_Hall.h in Headers */,
				2B4CB11325B80B3B004B0767 /* EscaAccessOverVisitor.h in Headers */,
				2B4CB11425B80B3B004B0767 /* hall_room_part.h in Headers */,
				2B4CB11525B80B3B004B0767 /* dvi_tinyxml2.h in Headers */,
				2B4CB11625B80B3B004B0767 /* Global_Control_Flag.h in Headers */,
				2B4CB11725B80B3B004B0767 /* Global_Escalators_Parameter.h in Headers */,
				2B4CB11825B80B3B004B0767 /* VrHallIndicatorVisitor.h in Headers */,
				2B4CB11925B80B3B004B0767 /* Sel_Esca_Handrail.h in Headers */,
				2B4CB11A25B80B3B004B0767 /* features.h in Headers */,
				2B4CB11B25B80B3B004B0767 /* BaseVisitor.h in Headers */,
				2B4CB11C25B80B3B004B0767 /* svr_material_channel.h in Headers */,
				2B4CB11D25B80B3B004B0767 /* EscaCombVisitor.h in Headers */,
				2B4CB11E25B80B3B004B0767 /* EscaStepVisitor.h in Headers */,
				2B4CB11F25B80B3B004B0767 /* Sel_Esca_Access_Cover_ExpendType.h in Headers */,
				2B4CB12025B80B3B004B0767 /* car_top.h in Headers */,
				2B4CB12125B80B3B004B0767 /* EscaSideCladdingLightingVisitor.h in Headers */,
				2B4CB12225B80B3B004B0767 /* car_wall_factory.h in Headers */,
				2B4CB12325B80B3B004B0767 /* EscaBalustradeFactory.h in Headers */,
				2B4CB12425B80B3B004B0767 /* Sel_Esca_Comb_Lighting.h in Headers */,
				2B4CB12525B80B3B004B0767 /* writer.h in Headers */,
				2B4CB12625B80B3B004B0767 /* Sel_Esca_Skirt_Brush.h in Headers */,
				2B4CB12725B80B3B004B0767 /* lru_cache.h in Headers */,
				2B4CB12825B80B3B004B0767 /* material_channel_pool.h in Headers */,
				2B4CB12925B80B3B004B0767 /* car_top_factory.h in Headers */,
				2B4CB12A25B80B3B004B0767 /* value.h in Headers */,
				2B4CB12B25B80B3B004B0767 /* Sel_Esca_Traffic_Light.h in Headers */,
				2B4CB12C25B80B3B004B0767 /* i_decoration_vr_interface_extend.h in Headers */,
				2B4CB12D25B80B3B004B0767 /* Global_TestNote.h in Headers */,
				2B4CB12E25B80B3B004B0767 /* Sel_Esca_Side_Cladding.h in Headers */,
				2B4CB12F25B80B3B004B0767 /* IElevatorPartFactory.h in Headers */,
				2B4CB13025B80B3B004B0767 /* config.h in Headers */,
				2B4CB13125B80B3B004B0767 /* svr_wall_size_info.h in Headers */,
				2B4CB13225B80B3B004B0767 /* svr_config.h in Headers */,
				2B4CB13325B80B3B004B0767 /* Sel_Esca_Decking_Ctrl_Box.h in Headers */,
				2B4CB13425B80B3B004B0767 /* IElevatorConfig.h in Headers */,
				2B4CB13525B80B3B004B0767 /* Global_Work1_Parameter.h in Headers */,
				2B4CB13625B80B3B004B0767 /* EscaTrafficLightVisitor.h in Headers */,
				2B4CB13725B80B3B004B0767 /* download_assist.h in Headers */,
				2B4CB13825B80B3B004B0767 /* Global_Work0_Parameter.h in Headers */,
				2B4CB13925B80B3B004B0767 /* Sel_Esca_Handrail_Lighting.h in Headers */,
				2B4CB13A25B80B3B004B0767 /* i_decoration_vr_array_visitor.h in Headers */,
				2B4CB13B25B80B3B004B0767 /* decoration_vr_interface_include.h in Headers */,
				2B4CB13C25B80B3B004B0767 /* Sel_Esca_Scene.h in Headers */,
				2B4CB13D25B80B3B004B0767 /* Sel_Ceiling.h in Headers */,
				2B4CB13E25B80B3B004B0767 /* Sel_Accessory.h in Headers */,
				2B4CB13F25B80B3B004B0767 /* FrontWallTypeOperator.h in Headers */,
				2B4CB14025B80B3B004B0767 /* Global_Hwndmsg.h in Headers */,
				2B4CB14125B80B3B004B0767 /* electric_factory.h in Headers */,
				2B4CB14225B80B3B004B0767 /* forwards.h in Headers */,
				2B4CB14325B80B3B004B0767 /* Global_CopyObject.h in Headers */,
				2B4CB14425B80B3B004B0767 /* EscaSkirtLightingVisitor.h in Headers */,
				2B4CB14525B80B3B004B0767 /* Sel_HI.h in Headers */,
				2B4CB14625B80B3B004B0767 /* i_decoration_vr_callback.h in Headers */,
				2B4CB14725B80B3B004B0767 /* EscaScenesVisitor.h in Headers */,
				2B4CB14825B80B3B004B0767 /* material_channel.h in Headers */,
				2B4CB14925B80B3B004B0767 /* Sel_Esca_Decking.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B56CFE02064D0B100DF2F76 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B56D1A12064E59000DF2F76 /* VrLanternDisplayVisitor.h in Headers */,
				2B56D1A32064E59000DF2F76 /* VrLanternVisitor.h in Headers */,
				2B56D1582064E58F00DF2F76 /* ModelCarWallElem.h in Headers */,
				2B56D23E2064E70E00DF2F76 /* GlobalInfoDataCommon.h in Headers */,
				2B0792852134E47D00A65EE1 /* Sel_Esca_Skirt.h in Headers */,
				2B56D1622064E58F00DF2F76 /* Sel_CopLcd.h in Headers */,
				2B0791D22134E46100A65EE1 /* EscaHandrailEnterVisitor.h in Headers */,
				2B56D18F2064E59000DF2F76 /* VrCopVisitor.h in Headers */,
				2B56D2292064E70E00DF2F76 /* skirting_factory.h in Headers */,
				2B56D1A92064E59000DF2F76 /* VrMirrorVisitor.h in Headers */,
				2B56D1A72064E59000DF2F76 /* VrLopVisitor.h in Headers */,
				2B0791D42134E46100A65EE1 /* EscaHandrailGuidVisitor.h in Headers */,
				2B56D19F2064E59000DF2F76 /* VrJambVisitor.h in Headers */,
				2B0791EE2134E46100A65EE1 /* EscaTrussLightingVisitor.h in Headers */,
				2B0792692134E47D00A65EE1 /* Sel_Esca_Background.h in Headers */,
				2B0791CC2134E46100A65EE1 /* EscaConfigVisitor.h in Headers */,
				2B0792AE2134E5AF00A65EE1 /* esca_elevator_part_factory.h in Headers */,
				2B07926B2134E47D00A65EE1 /* Sel_Esca_Balustrade.h in Headers */,
				2BC10E80207465DA00126085 /* svr_wall_element.h in Headers */,
				2B0791D02134E46100A65EE1 /* EscaDeckingVisitor.h in Headers */,
				2B56D2482064E70E00DF2F76 /* stdafx.h in Headers */,
				2B56D1A52064E59000DF2F76 /* VrLopDisplayVisitor.h in Headers */,
				2B0792772134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.h in Headers */,
				2B0791DA2134E46100A65EE1 /* EscaPhotoelectricVisitor.h in Headers */,
				2B56D2612064E70E00DF2F76 /* Util.h in Headers */,
				2B56D2522064E70E00DF2F76 /* db_visitor.h in Headers */,
				2B56D2392064E70E00DF2F76 /* ConstValueMap.h in Headers */,
				2B56D16A2064E58F00DF2F76 /* Sel_HILcd.h in Headers */,
				2BC10E7D207465DA00126085 /* svr_part_model_info.h in Headers */,
				2B56D1642064E58F00DF2F76 /* Sel_Hall.h in Headers */,
				2B56D1602064E58F00DF2F76 /* Sel_Cop.h in Headers */,
				2B56D20F2064E70E00DF2F76 /* skirting_part.h in Headers */,
				2B56D2252064E70E00DF2F76 /* download_visitor.h in Headers */,
				2B0792932134E47D00A65EE1 /* Sel_RGB_Share.h in Headers */,
				2B56D2242064E70E00DF2F76 /* IElevatorPart.h in Headers */,
				2B72CC7423C57A1700682713 /* Sel_Door_Imported.h in Headers */,
				2B56D0912064E4E800DF2F76 /* assertions.h in Headers */,
				2B56D1762064E59000DF2F76 /* Sel_Mirror.h in Headers */,
				2B0791C82134E46100A65EE1 /* EscaCombLightingVisitor.h in Headers */,
				2B56D2622064E70E00DF2F76 /* common_part.h in Headers */,
				2B72CC8723C57A7400682713 /* hall_room_part_factory.h in Headers */,
				2B56D0922064E4E800DF2F76 /* autolink.h in Headers */,
				2B56D1542064E58F00DF2F76 /* Global_Setting_Parameter.h in Headers */,
				2B56D2642064E70E00DF2F76 /* ElevatorPartOperator.h in Headers */,
				2B0791E22134E46100A65EE1 /* EscaSkirtBrushVisitor.h in Headers */,
				2B56D1AB2064E59000DF2F76 /* VrSightseeingVisitor.h in Headers */,
				2B56D0962064E4E800DF2F76 /* json.h in Headers */,
				2B0792512134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.h in Headers */,
				2B56D06B2064E4CA00DF2F76 /* i_decoration_vr_interface.h in Headers */,
				2B56D09D2064E4E800DF2F76 /* json_tool.h in Headers */,
				2B56D1992064E59000DF2F76 /* VrHallIndicatorDisplayVisitor.h in Headers */,
				2B72CC7323C57A1700682713 /* Sel_Bottom_Accessory.h in Headers */,
				2B07927D2134E47D00A65EE1 /* Sel_Esca_Photoelectric.h in Headers */,
				2B0792AC2134E5AF00A65EE1 /* esca_elevator_part.h in Headers */,
				2B0792552134E47D00A65EE1 /* Global_Script_Floor.h in Headers */,
				2B0792B62134E5AF00A65EE1 /* EscaSideCladdingFactory.h in Headers */,
				2B56D1812064E59000DF2F76 /* VrCarConfigVisitor.h in Headers */,
				2B56D0992064E4E800DF2F76 /* version.h in Headers */,
				2B0791C22134E46100A65EE1 /* EscaAcessOverExpendTypeVisitor.h in Headers */,
				2B56D1872064E59000DF2F76 /* VrCarWallVisitor.h in Headers */,
				2B0792B82134E5AF00A65EE1 /* EscaSkirtLightingFactory.h in Headers */,
				2B56D21B2064E70E00DF2F76 /* IDownloadVisitor.h in Headers */,
				2B72CC7523C57A1700682713 /* Sel_Car_Shell.h in Headers */,
				2B4CB15625B80D77004B0767 /* VrHallFloorVisitor.h in Headers */,
				2BC10E7B207465DA00126085 /* svr_part_material.h in Headers */,
				2B56D2572064E70E00DF2F76 /* elevator_size_parser.h in Headers */,
				2B56D1852064E59000DF2F76 /* VrCarIndicatorVisitor.h in Headers */,
				2B56D1912064E59000DF2F76 /* VrFrontWallVisitor.h in Headers */,
				2B0792452134E47D00A65EE1 /* Global_Camera_Position.h in Headers */,
				2B0791CE2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.h in Headers */,
				2B56D16C2064E58F00DF2F76 /* Sel_HL.h in Headers */,
				2B0792792134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.h in Headers */,
				2B0792B02134E5AF00A65EE1 /* EscaAcessOverFactory.h in Headers */,
				2B0792592134E47D00A65EE1 /* Global_Step_Parameter.h in Headers */,
				2B56D17F2064E59000DF2F76 /* VrBottomVisitor.h in Headers */,
				2B56D1952064E59000DF2F76 /* VrHallConfigVisitor.h in Headers */,
				2B56D1792064E59000DF2F76 /* vr_controller.h in Headers */,
				2B0791E62134E46100A65EE1 /* EscaSkirtVisitor.h in Headers */,
				2B4CB15825B80D77004B0767 /* VrHallWallVisitor.h in Headers */,
				2B0791E82134E46100A65EE1 /* EscaStepLightingVisitor.h in Headers */,
				2B56D06E2064E4CA00DF2F76 /* part_type.h in Headers */,
				2BC10E6C207465DA00126085 /* svr_part_basic_info.h in Headers */,
				2B0792672134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.h in Headers */,
				2B56D23C2064E70E00DF2F76 /* PartTypeManager.h in Headers */,
				2B56D14C2064E58F00DF2F76 /* Global_Loading_Scence.h in Headers */,
				2B0791D62134E46100A65EE1 /* EscaHandrailLightingVisitor.h in Headers */,
				2B56D2272064E70E00DF2F76 /* file_pool.h in Headers */,
				2B56D1832064E59000DF2F76 /* VrCarDoorVisitor.h in Headers */,
				2B07928D2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.h in Headers */,
				2B56D22B2064E70E00DF2F76 /* IDbVisitor.h in Headers */,
				2B72CC7023C57A1700682713 /* Sel_HallDoor_Imported.h in Headers */,
				2BC10E8A207466BE00126085 /* BaseFactory.h in Headers */,
				2BC10E8F207466E200126085 /* sha256.h in Headers */,
				2BC10E84207465DA00126085 /* svr_base_data.h in Headers */,
				2B56D2322064E70E00DF2F76 /* ConfigOperator.h in Headers */,
				2B07925B2134E47D00A65EE1 /* Global_StepLight0_Parameter.h in Headers */,
				2B0792912134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.h in Headers */,
				2B56D0902064E4E800DF2F76 /* allocator.h in Headers */,
				2B56D18D2064E59000DF2F76 /* VrCopDisplayVisitor.h in Headers */,
				2B0792432134E47D00A65EE1 /* Global_Camera_EffectParameter.h in Headers */,
				2B0792632134E47D00A65EE1 /* Sel_Esca_Access_Cover.h in Headers */,
				2B56D24B2064E70E00DF2F76 /* arg_cache.h in Headers */,
				2B56D0682064E4CA00DF2F76 /* decoration_vr_interface_lib.h in Headers */,
				2B56D1892064E59000DF2F76 /* VrChangeArg.h in Headers */,
				2B72CC8223C57A3500682713 /* VrHallVisitor.h in Headers */,
				2B56D24A2064E70E00DF2F76 /* config_factory.h in Headers */,
				2B72CC7F23C57A3500682713 /* VrCarShellVisitor.h in Headers */,
				2B0792832134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.h in Headers */,
				2B56D2632064E70E00DF2F76 /* ElevatorConfig.h in Headers */,
				2B56D18B2064E59000DF2F76 /* VrConfigInfo.h in Headers */,
				2B56D2472064E70E00DF2F76 /* decoration_vr_array_visitor.h in Headers */,
				2BC10E7F207465DA00126085 /* svr_material.h in Headers */,
				2B0791C42134E46100A65EE1 /* EscaAcessOverFlagVisitor.h in Headers */,
				2B0792892134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.h in Headers */,
				2B07926D2134E47D00A65EE1 /* Sel_Esca_Comb.h in Headers */,
				2B72CC8023C57A3500682713 /* VrHallShaftVisitor.h in Headers */,
				2B56D21C2064E70E00DF2F76 /* ElevatorPartFactoryManager.h in Headers */,
				2BC10E74207465DA00126085 /* svr_json_helper.h in Headers */,
				2B56D24E2064E70E00DF2F76 /* ConfigAnalyzer.h in Headers */,
				2B56D19D2064E59000DF2F76 /* VrHandrailVisitor.h in Headers */,
				2B56D21E2064E70E00DF2F76 /* car_wall_part.h in Headers */,
				2B56D1932064E59000DF2F76 /* VrGlobalInfo.h in Headers */,
				2B56D1702064E58F00DF2F76 /* Sel_Jamb.h in Headers */,
				2B56D2512064E70E00DF2F76 /* BasePartOperator.h in Headers */,
				2B56D1722064E58F00DF2F76 /* Sel_Lop.h in Headers */,
				2B56D1772064E59000DF2F76 /* IVrVisitor.h in Headers */,
				2B56D1742064E58F00DF2F76 /* Sel_LopLcd.h in Headers */,
				2B07924D2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.h in Headers */,
				2B56D2462064E70E00DF2F76 /* ConstPartInnerParams.h in Headers */,
				2B56D1482064E58F00DF2F76 /* DGBaseVisitor.h in Headers */,
				2B56D1662064E58F00DF2F76 /* Sel_HandRail.h in Headers */,
				2B56D06D2064E4CA00DF2F76 /* MsgParaValue.h in Headers */,
				2B0791D82134E46100A65EE1 /* EscaHandrailVisitor.h in Headers */,
				2B56D0A72064E54C00DF2F76 /* HttpAsyncManage.h in Headers */,
				2B56D23F2064E70E00DF2F76 /* electric_part.h in Headers */,
				2B0792532134E47D00A65EE1 /* Global_PartType_Info.h in Headers */,
				2B07928B2134E47D00A65EE1 /* Sel_Esca_Step.h in Headers */,
				2B56D2382064E70E00DF2F76 /* ElevatorSize.h in Headers */,
				2B56D16E2064E58F00DF2F76 /* Sel_HLLcd.h in Headers */,
				2B56D1972064E59000DF2F76 /* VrHallDoorVisitor.h in Headers */,
				2B72CC7223C57A1700682713 /* Sel_Shaft.h in Headers */,
				2B56D1502064E58F00DF2F76 /* Global_Setting_Car.h in Headers */,
				2B0792572134E47D00A65EE1 /* Global_Step0_Parameter.h in Headers */,
				2B56D0972064E4E800DF2F76 /* reader.h in Headers */,
				2B56D2102064E70E00DF2F76 /* decoration_vr_interface.h in Headers */,
				2B56D21A2064E70E00DF2F76 /* config_part.h in Headers */,
				2B0792B42134E5AF00A65EE1 /* EscaHandrailEnterFactory.h in Headers */,
				2BC10E76207465DA00126085 /* svr_material_special_rule.h in Headers */,
				2B0791E02134E46100A65EE1 /* EscaSideCladdingVisitor.h in Headers */,
				2B56D17B2064E59000DF2F76 /* vr_visitor_factory.h in Headers */,
				2BC10E82207465DA00126085 /* svr_car_wall.h in Headers */,
				2B56D21D2064E70E00DF2F76 /* PartOperatorManager.h in Headers */,
				2B56D20E2064E70E00DF2F76 /* resource.h in Headers */,
				2B56D2452064E70E00DF2F76 /* common_part_factory.h in Headers */,
				2B56D2532064E70E00DF2F76 /* ElevatorConfigManager.h in Headers */,
				2B56D17D2064E59000DF2F76 /* VrAccessoryVisitor.h in Headers */,
				2B0792982134E51500A65EE1 /* EscalatorSize.h in Headers */,
				2B56D2152064E70E00DF2F76 /* ElectricOperator.h in Headers */,
				2B07924B2134E47D00A65EE1 /* Global_Esca_PartMark.h in Headers */,
				2B56D2652064E70E00DF2F76 /* IPartTypeManager.h in Headers */,
				2B56D15C2064E58F00DF2F76 /* Sel_CarIndicator.h in Headers */,
				2B56D2342064E70E00DF2F76 /* IConfig.h in Headers */,
				2BC10E77207465DA00126085 /* svr_common_part.h in Headers */,
				2B56D14E2064E58F00DF2F76 /* Global_Select.h in Headers */,
				2B0791C62134E46100A65EE1 /* EscaBalustradeVisitor.h in Headers */,
				2BC10E6E207465DA00126085 /* svr_file_digital_info.h in Headers */,
				2B56D1AD2064E59000DF2F76 /* VrTopVisitor.h in Headers */,
				2B56D1522064E58F00DF2F76 /* Global_Setting_Hall.h in Headers */,
				2B0791C02134E46100A65EE1 /* EscaAccessOverVisitor.h in Headers */,
				2B72CC8923C57A7400682713 /* hall_room_part.h in Headers */,
				2B07929B2134E51500A65EE1 /* dvi_tinyxml2.h in Headers */,
				2B0792472134E47D00A65EE1 /* Global_Control_Flag.h in Headers */,
				2B07924F2134E47D00A65EE1 /* Global_Escalators_Parameter.h in Headers */,
				2B56D19B2064E59000DF2F76 /* VrHallIndicatorVisitor.h in Headers */,
				2B0792752134E47D00A65EE1 /* Sel_Esca_Handrail.h in Headers */,
				2B56D0942064E4E800DF2F76 /* features.h in Headers */,
				2B56D2662064E70E00DF2F76 /* BaseVisitor.h in Headers */,
				2BC10E70207465DA00126085 /* svr_material_channel.h in Headers */,
				2B0791CA2134E46100A65EE1 /* EscaCombVisitor.h in Headers */,
				2B0791EA2134E46100A65EE1 /* EscaStepVisitor.h in Headers */,
				2B0792652134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.h in Headers */,
				2B56D22D2064E70E00DF2F76 /* car_top.h in Headers */,
				2B0791DE2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.h in Headers */,
				2B56D2412064E70E00DF2F76 /* car_wall_factory.h in Headers */,
				2B0792B22134E5AF00A65EE1 /* EscaBalustradeFactory.h in Headers */,
				2B07926F2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.h in Headers */,
				2B56D09A2064E4E800DF2F76 /* writer.h in Headers */,
				2B0792872134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.h in Headers */,
				2BC10E92207466E200126085 /* lru_cache.h in Headers */,
				2B56D2232064E70E00DF2F76 /* material_channel_pool.h in Headers */,
				2B56D25D2064E70E00DF2F76 /* car_top_factory.h in Headers */,
				2B56D0982064E4E800DF2F76 /* value.h in Headers */,
				2B07928F2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.h in Headers */,
				2B56D06C2064E4CA00DF2F76 /* i_decoration_vr_interface_extend.h in Headers */,
				2B07925D2134E47D00A65EE1 /* Global_TestNote.h in Headers */,
				2B0792812134E47D00A65EE1 /* Sel_Esca_Side_Cladding.h in Headers */,
				2B56D2302064E70E00DF2F76 /* IElevatorPartFactory.h in Headers */,
				2B56D0932064E4E800DF2F76 /* config.h in Headers */,
				2BC10E81207465DA00126085 /* svr_wall_size_info.h in Headers */,
				2BC10E78207465DA00126085 /* svr_config.h in Headers */,
				2B0792732134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.h in Headers */,
				2B56D2142064E70E00DF2F76 /* IElevatorConfig.h in Headers */,
				2B0792612134E47D00A65EE1 /* Global_Work1_Parameter.h in Headers */,
				2B0791EC2134E46100A65EE1 /* EscaTrafficLightVisitor.h in Headers */,
				2BED8C10228BE75400AEF4D0 /* download_assist.h in Headers */,
				2B07925F2134E47D00A65EE1 /* Global_Work0_Parameter.h in Headers */,
				2B07927B2134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.h in Headers */,
				2B56D0692064E4CA00DF2F76 /* i_decoration_vr_array_visitor.h in Headers */,
				2B56D2432064E70E00DF2F76 /* decoration_vr_interface_include.h in Headers */,
				2B07927F2134E47D00A65EE1 /* Sel_Esca_Scene.h in Headers */,
				2B56D15E2064E58F00DF2F76 /* Sel_Ceiling.h in Headers */,
				2B56D15A2064E58F00DF2F76 /* Sel_Accessory.h in Headers */,
				2B56D25B2064E70E00DF2F76 /* FrontWallTypeOperator.h in Headers */,
				2B56D14A2064E58F00DF2F76 /* Global_Hwndmsg.h in Headers */,
				2B56D2182064E70E00DF2F76 /* electric_factory.h in Headers */,
				2B56D0952064E4E800DF2F76 /* forwards.h in Headers */,
				2B0792492134E47D00A65EE1 /* Global_CopyObject.h in Headers */,
				2B0791E42134E46100A65EE1 /* EscaSkirtLightingVisitor.h in Headers */,
				2B56D1682064E58F00DF2F76 /* Sel_HI.h in Headers */,
				2B56D06A2064E4CA00DF2F76 /* i_decoration_vr_callback.h in Headers */,
				2B0791DC2134E46100A65EE1 /* EscaScenesVisitor.h in Headers */,
				2B56D1562064E58F00DF2F76 /* material_channel.h in Headers */,
				2B0792712134E47D00A65EE1 /* Sel_Esca_Decking.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		2B4CAFA725B80B3B004B0767 /* DecorationVrInterfaceDll_mbcs_xio */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2B4CB14A25B80B3B004B0767 /* Build configuration list for PBXNativeTarget "DecorationVrInterfaceDll_mbcs_xio" */;
			buildPhases = (
				2B4CAFA825B80B3B004B0767 /* Sources */,
				2B4CB06A25B80B3B004B0767 /* Frameworks */,
				2B4CB06B25B80B3B004B0767 /* Copy Files */,
				2B4CB06C25B80B3B004B0767 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DecorationVrInterfaceDll_mbcs_xio;
			productName = DecorationVrInterfaceDll_mbcs;
			productReference = 2B4CB14D25B80B3B004B0767 /* libDecorationVrInterfaceDll_mbcs_xio.a */;
			productType = "com.apple.product-type.library.static";
		};
		2B56CF492064C90800DF2F76 /* DecorationVrInterfaceDll_mbcs */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2B56CF502064C90800DF2F76 /* Build configuration list for PBXNativeTarget "DecorationVrInterfaceDll_mbcs" */;
			buildPhases = (
				2B56CF462064C90800DF2F76 /* Sources */,
				2B56CF472064C90800DF2F76 /* Frameworks */,
				2B56CF482064C90800DF2F76 /* Copy Files */,
				2B56CFE02064D0B100DF2F76 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DecorationVrInterfaceDll_mbcs;
			productName = DecorationVrInterfaceDll_mbcs;
			productReference = 2B56CF4A2064C90800DF2F76 /* libDecorationVrInterfaceDll_mbcs_x6b64asd.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		D049E5251ADF6B7C004DC3D1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0610;
				ORGANIZATIONNAME = zxtech;
				TargetAttributes = {
					2B4CAFA725B80B3B004B0767 = {
						ProvisioningStyle = Automatic;
					};
					2B56CF492064C90800DF2F76 = {
						CreatedOnToolsVersion = 9.2;
						ProvisioningStyle = Automatic;
					};
					D05124A71BFF17B200CDA819 = {
						CreatedOnToolsVersion = 6.4;
					};
				};
			};
			buildConfigurationList = D049E5281ADF6B7C004DC3D1 /* Build configuration list for PBXProject "DecorationVrInterfaceDll" */;
			compatibilityVersion = "Xcode 6.3";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = D049E5241ADF6B7C004DC3D1;
			productRefGroup = D049E52E1ADF6B7C004DC3D1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2B56CF492064C90800DF2F76 /* DecorationVrInterfaceDll_mbcs */,
				D05124A71BFF17B200CDA819 /* BUILD_ALL_MBCS */,
				2B4CAFA725B80B3B004B0767 /* DecorationVrInterfaceDll_mbcs_xio */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		2B4CAFA825B80B3B004B0767 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B4CAFA925B80B3B004B0767 /* BaseVisitor.cpp in Sources */,
				2B4CB15325B80D77004B0767 /* VrHallWallVisitor.cpp in Sources */,
				2B4CAFAA25B80B3B004B0767 /* EscaBalustradeFactory.cpp in Sources */,
				2B4CAFAB25B80B3B004B0767 /* VrFrontWallVisitor.cpp in Sources */,
				2B4CAFAC25B80B3B004B0767 /* car_top.cpp in Sources */,
				2B4CAFAD25B80B3B004B0767 /* Sel_Bottom_Accessory.cpp in Sources */,
				2B4CAFAE25B80B3B004B0767 /* Sel_Esca_Comb_Lighting.cpp in Sources */,
				2B4CAFAF25B80B3B004B0767 /* Sel_Esca_Photoelectric.cpp in Sources */,
				2B4CAFB025B80B3B004B0767 /* Sel_Esca_Balustrade.cpp in Sources */,
				2B4CAFB125B80B3B004B0767 /* decoration_vr_interface_lib.cpp in Sources */,
				2B4CAFB225B80B3B004B0767 /* EscaSkirtBrushVisitor.cpp in Sources */,
				2B4CAFB325B80B3B004B0767 /* sha256.cpp in Sources */,
				2B4CAFB425B80B3B004B0767 /* Sel_Esca_Comb.cpp in Sources */,
				2B4CAFB525B80B3B004B0767 /* Sel_Esca_Skirt_Lighting.cpp in Sources */,
				2B4CAFB625B80B3B004B0767 /* Sel_Esca_Access_Cover_Flag.cpp in Sources */,
				2B4CAFB725B80B3B004B0767 /* svr_part_model_info.cpp in Sources */,
				2B4CAFB825B80B3B004B0767 /* EscaSideCladdingLightingVisitor.cpp in Sources */,
				2B4CAFB925B80B3B004B0767 /* EscaStepLightingVisitor.cpp in Sources */,
				2B4CAFBA25B80B3B004B0767 /* Sel_LopLcd.cpp in Sources */,
				2B4CAFBB25B80B3B004B0767 /* Global_Control_Flag.cpp in Sources */,
				2B4CAFBC25B80B3B004B0767 /* Sel_Esca_Handrail_Lighting.cpp in Sources */,
				2B4CAFBD25B80B3B004B0767 /* Sel_Accessory.cpp in Sources */,
				2B4CAFBE25B80B3B004B0767 /* BasePartOperator.cpp in Sources */,
				2B4CAFBF25B80B3B004B0767 /* Sel_Hall.cpp in Sources */,
				2B4CAFC025B80B3B004B0767 /* Global_TestNote.cpp in Sources */,
				2B4CAFC125B80B3B004B0767 /* VrLanternVisitor.cpp in Sources */,
				2B4CAFC225B80B3B004B0767 /* EscaBalustradeVisitor.cpp in Sources */,
				2B4CAFC325B80B3B004B0767 /* VrCarDoorVisitor.cpp in Sources */,
				2B4CAFC425B80B3B004B0767 /* VrChangeArg.cpp in Sources */,
				2B4CAFC525B80B3B004B0767 /* ElectricOperator.cpp in Sources */,
				2B4CAFC625B80B3B004B0767 /* Sel_Esca_Access_Cover_ExpendType.cpp in Sources */,
				2B4CAFC725B80B3B004B0767 /* EscaAcessOverExpendTypeVisitor.cpp in Sources */,
				2B4CAFC825B80B3B004B0767 /* VrLanternDisplayVisitor.cpp in Sources */,
				2B4CAFC925B80B3B004B0767 /* VrCarConfigVisitor.cpp in Sources */,
				2B4CAFCA25B80B3B004B0767 /* EscaHandrailEnterVisitor.cpp in Sources */,
				2B4CAFCB25B80B3B004B0767 /* skirting_part.cpp in Sources */,
				2B4CAFCC25B80B3B004B0767 /* Sel_Cop.cpp in Sources */,
				2B4CAFCD25B80B3B004B0767 /* Global_Loading_Scence.cpp in Sources */,
				2B4CAFCE25B80B3B004B0767 /* VrCopDisplayVisitor.cpp in Sources */,
				2B4CAFCF25B80B3B004B0767 /* EscaHandrailLightingVisitor.cpp in Sources */,
				2B4CAFD025B80B3B004B0767 /* EscaHandrailEnterFactory.cpp in Sources */,
				2B4CAFD125B80B3B004B0767 /* electric_part.cpp in Sources */,
				2B4CAFD225B80B3B004B0767 /* VrHallVisitor.cpp in Sources */,
				2B4CAFD325B80B3B004B0767 /* common_part.cpp in Sources */,
				2B4CAFD425B80B3B004B0767 /* json_reader.cpp in Sources */,
				2B4CAFD525B80B3B004B0767 /* Sel_Esca_Background.cpp in Sources */,
				2B4CAFD625B80B3B004B0767 /* VrAccessoryVisitor.cpp in Sources */,
				2B4CAFD725B80B3B004B0767 /* esca_elevator_part.cpp in Sources */,
				2B4CAFD825B80B3B004B0767 /* EscaSkirtVisitor.cpp in Sources */,
				2B4CAFD925B80B3B004B0767 /* Sel_Esca_Skirt.cpp in Sources */,
				2B4CAFDA25B80B3B004B0767 /* Sel_Esca_Access_Cover.cpp in Sources */,
				2B4CAFDB25B80B3B004B0767 /* common_part_factory.cpp in Sources */,
				2B4CAFDC25B80B3B004B0767 /* Sel_Esca_Decking.cpp in Sources */,
				2B4CAFDD25B80B3B004B0767 /* Global_Setting_Car.cpp in Sources */,
				2B4CAFDE25B80B3B004B0767 /* Sel_Ceiling.cpp in Sources */,
				2B4CAFDF25B80B3B004B0767 /* download_assist.cpp in Sources */,
				2B4CAFE025B80B3B004B0767 /* Sel_Esca_Side_Cladding_Lighting.cpp in Sources */,
				2B4CAFE125B80B3B004B0767 /* svr_car_wall.cpp in Sources */,
				2B4CAFE225B80B3B004B0767 /* stdafx.cpp in Sources */,
				2B4CAFE325B80B3B004B0767 /* EscaAcessOverFlagVisitor.cpp in Sources */,
				2B4CAFE425B80B3B004B0767 /* EscaAcessOverFactory.cpp in Sources */,
				2B4CAFE525B80B3B004B0767 /* svr_config.cpp in Sources */,
				2B4CAFE625B80B3B004B0767 /* GlobalInfoDataCommon.cpp in Sources */,
				2B4CAFE725B80B3B004B0767 /* VrCarIndicatorVisitor.cpp in Sources */,
				2B4CAFE825B80B3B004B0767 /* config_part.cpp in Sources */,
				2B4CAFE925B80B3B004B0767 /* VrJambVisitor.cpp in Sources */,
				2B4CAFEA25B80B3B004B0767 /* FrontWallTypeOperator.cpp in Sources */,
				2B4CAFEB25B80B3B004B0767 /* Global_Escalators_Rung_Parameter.cpp in Sources */,
				2B4CAFEC25B80B3B004B0767 /* Sel_Esca_Decking_Ctrl_Box.cpp in Sources */,
				2B4CAFED25B80B3B004B0767 /* Sel_HLLcd.cpp in Sources */,
				2B4CAFEE25B80B3B004B0767 /* arg_cache.cpp in Sources */,
				2B4CAFEF25B80B3B004B0767 /* EscaPhotoelectricVisitor.cpp in Sources */,
				2B4CAFF025B80B3B004B0767 /* Sel_Esca_Skirt_Brush.cpp in Sources */,
				2B4CAFF125B80B3B004B0767 /* db_visitor.cpp in Sources */,
				2B4CAFF225B80B3B004B0767 /* car_top_factory.cpp in Sources */,
				2B4CAFF325B80B3B004B0767 /* Global_Setting_Hall.cpp in Sources */,
				2B4CAFF425B80B3B004B0767 /* VrConfigInfo.cpp in Sources */,
				2B4CAFF525B80B3B004B0767 /* Sel_Jamb.cpp in Sources */,
				2B4CAFF625B80B3B004B0767 /* car_wall_part.cpp in Sources */,
				2B4CAFF725B80B3B004B0767 /* VrLopDisplayVisitor.cpp in Sources */,
				2B4CAFF825B80B3B004B0767 /* Global_StepLight0_Parameter.cpp in Sources */,
				2B4CAFF925B80B3B004B0767 /* Sel_Lop.cpp in Sources */,
				2B4CAFFA25B80B3B004B0767 /* EscaConfigVisitor.cpp in Sources */,
				2B4CAFFB25B80B3B004B0767 /* Sel_Esca_Truss_Lighting.cpp in Sources */,
				2B4CAFFC25B80B3B004B0767 /* Sel_Mirror.cpp in Sources */,
				2B4CAFFD25B80B3B004B0767 /* ConfigOperator.cpp in Sources */,
				2B4CAFFE25B80B3B004B0767 /* EscaHandrailGuidVisitor.cpp in Sources */,
				2B4CAFFF25B80B3B004B0767 /* hall_room_part.cpp in Sources */,
				2B4CB00025B80B3B004B0767 /* VrTopVisitor.cpp in Sources */,
				2B4CB00125B80B3B004B0767 /* VrHallShaftVisitor.cpp in Sources */,
				2B4CB00225B80B3B004B0767 /* EscaSkirtLightingFactory.cpp in Sources */,
				2B4CB00325B80B3B004B0767 /* Global_CopyObject.cpp in Sources */,
				2B4CB00425B80B3B004B0767 /* EscaCombLightingVisitor.cpp in Sources */,
				2B4CB00525B80B3B004B0767 /* decoration_vr_array_visitor.cpp in Sources */,
				2B4CB00625B80B3B004B0767 /* EscaSkirtLightingVisitor.cpp in Sources */,
				2B4CB00725B80B3B004B0767 /* ElevatorSize.cpp in Sources */,
				2B4CB00825B80B3B004B0767 /* Sel_HI.cpp in Sources */,
				2B4CB00925B80B3B004B0767 /* svr_json_helper.cpp in Sources */,
				2B4CB00A25B80B3B004B0767 /* vr_visitor_factory.cpp in Sources */,
				2B4CB00B25B80B3B004B0767 /* svr_part_basic_info.cpp in Sources */,
				2B4CB00C25B80B3B004B0767 /* svr_wall_size_info.cpp in Sources */,
				2B4CB00D25B80B3B004B0767 /* dvi_tinyxml2.cpp in Sources */,
				2B4CB00E25B80B3B004B0767 /* PartTypeManager.cpp in Sources */,
				2B4CB00F25B80B3B004B0767 /* file_pool.cpp in Sources */,
				2B4CB01025B80B3B004B0767 /* ElevatorPartFactoryManager.cpp in Sources */,
				2B4CB01125B80B3B004B0767 /* Sel_Esca_Side_Cladding.cpp in Sources */,
				2B4CB01225B80B3B004B0767 /* Global_Esca_PartMark.cpp in Sources */,
				2B4CB01325B80B3B004B0767 /* EscaSideCladdingFactory.cpp in Sources */,
				2B4CB01425B80B3B004B0767 /* electric_factory.cpp in Sources */,
				2B4CB01525B80B3B004B0767 /* EscaCombVisitor.cpp in Sources */,
				2B4CB01625B80B3B004B0767 /* Sel_Door_Imported.cpp in Sources */,
				2B4CB01725B80B3B004B0767 /* svr_common_part.cpp in Sources */,
				2B4CB01825B80B3B004B0767 /* VrCopVisitor.cpp in Sources */,
				2B4CB01925B80B3B004B0767 /* VrMirrorVisitor.cpp in Sources */,
				2B4CB01A25B80B3B004B0767 /* Global_Escalators_Parameter.cpp in Sources */,
				2B4CB15525B80D77004B0767 /* VrHallFloorVisitor.cpp in Sources */,
				2B4CB01B25B80B3B004B0767 /* decoration_vr_interface.cpp in Sources */,
				2B4CB01C25B80B3B004B0767 /* json_value.cpp in Sources */,
				2B4CB01D25B80B3B004B0767 /* vr_controller.cpp in Sources */,
				2B4CB01E25B80B3B004B0767 /* VrLopVisitor.cpp in Sources */,
				2B4CB01F25B80B3B004B0767 /* Util.cpp in Sources */,
				2B4CB02025B80B3B004B0767 /* Sel_HandRail.cpp in Sources */,
				2B4CB02125B80B3B004B0767 /* PartOperatorManager.cpp in Sources */,
				2B4CB02225B80B3B004B0767 /* VrSightseeingVisitor.cpp in Sources */,
				2B4CB02325B80B3B004B0767 /* svr_part_material.cpp in Sources */,
				2B4CB02425B80B3B004B0767 /* Global_Work0_Parameter.cpp in Sources */,
				2B4CB02525B80B3B004B0767 /* VrGlobalInfo.cpp in Sources */,
				2B4CB02625B80B3B004B0767 /* Sel_CopLcd.cpp in Sources */,
				2B4CB02725B80B3B004B0767 /* EscaStepVisitor.cpp in Sources */,
				2B4CB02825B80B3B004B0767 /* download_visitor.cpp in Sources */,
				2B4CB02925B80B3B004B0767 /* Sel_Shaft.cpp in Sources */,
				2B4CB02A25B80B3B004B0767 /* Sel_RGB_Share.cpp in Sources */,
				2B4CB02B25B80B3B004B0767 /* Sel_HILcd.cpp in Sources */,
				2B4CB02C25B80B3B004B0767 /* Global_PartType_Info.cpp in Sources */,
				2B4CB02D25B80B3B004B0767 /* ModelCarWallElem.cpp in Sources */,
				2B4CB02E25B80B3B004B0767 /* svr_file_digital_info.cpp in Sources */,
				2B4CB02F25B80B3B004B0767 /* EscaSideCladdingVisitor.cpp in Sources */,
				2B4CB03025B80B3B004B0767 /* svr_material.cpp in Sources */,
				2B4CB03125B80B3B004B0767 /* Global_Step_Parameter.cpp in Sources */,
				2B4CB03225B80B3B004B0767 /* Sel_Car_Shell.cpp in Sources */,
				2B4CB03325B80B3B004B0767 /* json_writer.cpp in Sources */,
				2B4CB03425B80B3B004B0767 /* ConfigAnalyzer.cpp in Sources */,
				2B4CB03525B80B3B004B0767 /* Sel_Esca_Scene.cpp in Sources */,
				2B4CB03625B80B3B004B0767 /* ConstValueMap.cpp in Sources */,
				2B4CB03725B80B3B004B0767 /* svr_wall_element.cpp in Sources */,
				2B4CB03825B80B3B004B0767 /* VrHallIndicatorDisplayVisitor.cpp in Sources */,
				2B4CB03925B80B3B004B0767 /* material_channel_pool.cpp in Sources */,
				2B4CB03A25B80B3B004B0767 /* Sel_Esca_Handrail.cpp in Sources */,
				2B4CB03B25B80B3B004B0767 /* Global_Esca_Setting_Parameter.cpp in Sources */,
				2B4CB03C25B80B3B004B0767 /* EscalatorSize.cpp in Sources */,
				2B4CB03D25B80B3B004B0767 /* EscaScenesVisitor.cpp in Sources */,
				2B4CB03E25B80B3B004B0767 /* svr_material_channel.cpp in Sources */,
				2B4CB03F25B80B3B004B0767 /* skirting_factory.cpp in Sources */,
				2B4CB04025B80B3B004B0767 /* config_factory.cpp in Sources */,
				2B4CB04125B80B3B004B0767 /* VrCarWallVisitor.cpp in Sources */,
				2B4CB04225B80B3B004B0767 /* svr_material_special_rule.cpp in Sources */,
				2B4CB04325B80B3B004B0767 /* Global_Setting_Parameter.cpp in Sources */,
				2B4CB04425B80B3B004B0767 /* VrHallConfigVisitor.cpp in Sources */,
				2B4CB04525B80B3B004B0767 /* Global_Camera_Position.cpp in Sources */,
				2B4CB04625B80B3B004B0767 /* EscaTrussLightingVisitor.cpp in Sources */,
				2B4CB04725B80B3B004B0767 /* Global_Step0_Parameter.cpp in Sources */,
				2B4CB04825B80B3B004B0767 /* VrHandrailVisitor.cpp in Sources */,
				2B4CB04925B80B3B004B0767 /* Sel_HallDoor_Imported.cpp in Sources */,
				2B4CB04A25B80B3B004B0767 /* EscaTrafficLightVisitor.cpp in Sources */,
				2B4CB04B25B80B3B004B0767 /* Global_Select.cpp in Sources */,
				2B4CB04C25B80B3B004B0767 /* Global_Camera_EffectParameter.cpp in Sources */,
				2B4CB04D25B80B3B004B0767 /* Sel_Esca_Handrail_Enter.cpp in Sources */,
				2B4CB04E25B80B3B004B0767 /* Sel_HL.cpp in Sources */,
				2B4CB04F25B80B3B004B0767 /* VrHallDoorVisitor.cpp in Sources */,
				2B4CB05025B80B3B004B0767 /* hall_room_part_factory.cpp in Sources */,
				2B4CB05125B80B3B004B0767 /* Sel_CarIndicator.cpp in Sources */,
				2B4CB05225B80B3B004B0767 /* Global_Work1_Parameter.cpp in Sources */,
				2B4CB05325B80B3B004B0767 /* material_channel.cpp in Sources */,
				2B4CB05425B80B3B004B0767 /* VrHallIndicatorVisitor.cpp in Sources */,
				2B4CB05525B80B3B004B0767 /* ElevatorConfig.cpp in Sources */,
				2B4CB05625B80B3B004B0767 /* EscaHandrailVisitor.cpp in Sources */,
				2B4CB05725B80B3B004B0767 /* DGBaseVisitor.cpp in Sources */,
				2B4CB05825B80B3B004B0767 /* Sel_Esca_Step_Lighting.cpp in Sources */,
				2B4CB05925B80B3B004B0767 /* car_wall_factory.cpp in Sources */,
				2B4CB05A25B80B3B004B0767 /* Sel_Esca_Handrail_Guid.cpp in Sources */,
				2B4CB05B25B80B3B004B0767 /* lru_cache.cpp in Sources */,
				2B4CB05C25B80B3B004B0767 /* Global_Script_Floor.cpp in Sources */,
				2B4CB05D25B80B3B004B0767 /* EscaDeckingCtrlBoxVisitor.cpp in Sources */,
				2B4CB05E25B80B3B004B0767 /* ElevatorPartOperator.cpp in Sources */,
				2B4CB05F25B80B3B004B0767 /* HttpAsyncManage.cpp in Sources */,
				2B4CB06025B80B3B004B0767 /* VrBottomVisitor.cpp in Sources */,
				2B4CB06125B80B3B004B0767 /* elevator_size_parser.cpp in Sources */,
				2B4CB06225B80B3B004B0767 /* Global_Hwndmsg.cpp in Sources */,
				2B4CB06325B80B3B004B0767 /* Sel_Esca_Traffic_Light.cpp in Sources */,
				2B4CB06425B80B3B004B0767 /* EscaAccessOverVisitor.cpp in Sources */,
				2B4CB06525B80B3B004B0767 /* EscaDeckingVisitor.cpp in Sources */,
				2B4CB06625B80B3B004B0767 /* Sel_Esca_Step.cpp in Sources */,
				2B4CB06725B80B3B004B0767 /* esca_elevator_part_factory.cpp in Sources */,
				2B4CB06825B80B3B004B0767 /* ElevatorConfigManager.cpp in Sources */,
				2B4CB06925B80B3B004B0767 /* VrCarShellVisitor.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B56CF462064C90800DF2F76 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B56D2402064E70E00DF2F76 /* BaseVisitor.cpp in Sources */,
				2B4CB15225B80D77004B0767 /* VrHallWallVisitor.cpp in Sources */,
				2B0792B12134E5AF00A65EE1 /* EscaBalustradeFactory.cpp in Sources */,
				2B56D1902064E59000DF2F76 /* VrFrontWallVisitor.cpp in Sources */,
				2B56D2552064E70E00DF2F76 /* car_top.cpp in Sources */,
				2BBE12D3222F633D0003D940 /* Sel_Bottom_Accessory.cpp in Sources */,
				2B07926E2134E47D00A65EE1 /* Sel_Esca_Comb_Lighting.cpp in Sources */,
				2B07927C2134E47D00A65EE1 /* Sel_Esca_Photoelectric.cpp in Sources */,
				2B07926A2134E47D00A65EE1 /* Sel_Esca_Balustrade.cpp in Sources */,
				2B56D24F2064E70E00DF2F76 /* decoration_vr_interface_lib.cpp in Sources */,
				2B0791E12134E46100A65EE1 /* EscaSkirtBrushVisitor.cpp in Sources */,
				2BC10E90207466E200126085 /* sha256.cpp in Sources */,
				2B07926C2134E47D00A65EE1 /* Sel_Esca_Comb.cpp in Sources */,
				2B0792882134E47D00A65EE1 /* Sel_Esca_Skirt_Lighting.cpp in Sources */,
				2B0792662134E47D00A65EE1 /* Sel_Esca_Access_Cover_Flag.cpp in Sources */,
				2BC10E7C207465DA00126085 /* svr_part_model_info.cpp in Sources */,
				2B0791DD2134E46100A65EE1 /* EscaSideCladdingLightingVisitor.cpp in Sources */,
				2B0791E72134E46100A65EE1 /* EscaStepLightingVisitor.cpp in Sources */,
				2B56D1732064E58F00DF2F76 /* Sel_LopLcd.cpp in Sources */,
				2B0792462134E47D00A65EE1 /* Global_Control_Flag.cpp in Sources */,
				2B07927A2134E47D00A65EE1 /* Sel_Esca_Handrail_Lighting.cpp in Sources */,
				2B56D1592064E58F00DF2F76 /* Sel_Accessory.cpp in Sources */,
				2B56D2262064E70E00DF2F76 /* BasePartOperator.cpp in Sources */,
				2B56D1632064E58F00DF2F76 /* Sel_Hall.cpp in Sources */,
				2B07925C2134E47D00A65EE1 /* Global_TestNote.cpp in Sources */,
				2B56D1A22064E59000DF2F76 /* VrLanternVisitor.cpp in Sources */,
				2B0791C52134E46100A65EE1 /* EscaBalustradeVisitor.cpp in Sources */,
				2B56D1822064E59000DF2F76 /* VrCarDoorVisitor.cpp in Sources */,
				2B56D1882064E59000DF2F76 /* VrChangeArg.cpp in Sources */,
				2B56D2192064E70E00DF2F76 /* ElectricOperator.cpp in Sources */,
				2B0792642134E47D00A65EE1 /* Sel_Esca_Access_Cover_ExpendType.cpp in Sources */,
				2B0791C12134E46100A65EE1 /* EscaAcessOverExpendTypeVisitor.cpp in Sources */,
				2B56D1A02064E59000DF2F76 /* VrLanternDisplayVisitor.cpp in Sources */,
				2B56D1802064E59000DF2F76 /* VrCarConfigVisitor.cpp in Sources */,
				2B0791D12134E46100A65EE1 /* EscaHandrailEnterVisitor.cpp in Sources */,
				2B56D2372064E70E00DF2F76 /* skirting_part.cpp in Sources */,
				2B56D15F2064E58F00DF2F76 /* Sel_Cop.cpp in Sources */,
				2B56D14B2064E58F00DF2F76 /* Global_Loading_Scence.cpp in Sources */,
				2B56D18C2064E59000DF2F76 /* VrCopDisplayVisitor.cpp in Sources */,
				2B0791D52134E46100A65EE1 /* EscaHandrailLightingVisitor.cpp in Sources */,
				2B0792B32134E5AF00A65EE1 /* EscaHandrailEnterFactory.cpp in Sources */,
				2B56D2362064E70E00DF2F76 /* electric_part.cpp in Sources */,
				2B72CC8123C57A3500682713 /* VrHallVisitor.cpp in Sources */,
				2B56D22E2064E70E00DF2F76 /* common_part.cpp in Sources */,
				2B56D09C2064E4E800DF2F76 /* json_reader.cpp in Sources */,
				2B0792682134E47D00A65EE1 /* Sel_Esca_Background.cpp in Sources */,
				2B56D17C2064E59000DF2F76 /* VrAccessoryVisitor.cpp in Sources */,
				2B0792AB2134E5AF00A65EE1 /* esca_elevator_part.cpp in Sources */,
				2B0791E52134E46100A65EE1 /* EscaSkirtVisitor.cpp in Sources */,
				2B0792842134E47D00A65EE1 /* Sel_Esca_Skirt.cpp in Sources */,
				2B0792622134E47D00A65EE1 /* Sel_Esca_Access_Cover.cpp in Sources */,
				2B56D2592064E70E00DF2F76 /* common_part_factory.cpp in Sources */,
				2B0792702134E47D00A65EE1 /* Sel_Esca_Decking.cpp in Sources */,
				2B56D14F2064E58F00DF2F76 /* Global_Setting_Car.cpp in Sources */,
				2B56D15D2064E58F00DF2F76 /* Sel_Ceiling.cpp in Sources */,
				2BED8C0F228BE75400AEF4D0 /* download_assist.cpp in Sources */,
				2B0792822134E47D00A65EE1 /* Sel_Esca_Side_Cladding_Lighting.cpp in Sources */,
				2BC10E6D207465DA00126085 /* svr_car_wall.cpp in Sources */,
				2B56D2212064E70E00DF2F76 /* stdafx.cpp in Sources */,
				2B0791C32134E46100A65EE1 /* EscaAcessOverFlagVisitor.cpp in Sources */,
				2B0792AF2134E5AF00A65EE1 /* EscaAcessOverFactory.cpp in Sources */,
				2BC10E75207465DA00126085 /* svr_config.cpp in Sources */,
				2B56D22F2064E70E00DF2F76 /* GlobalInfoDataCommon.cpp in Sources */,
				2B56D1842064E59000DF2F76 /* VrCarIndicatorVisitor.cpp in Sources */,
				2B56D22A2064E70E00DF2F76 /* config_part.cpp in Sources */,
				2B56D19E2064E59000DF2F76 /* VrJambVisitor.cpp in Sources */,
				2B56D2332064E70E00DF2F76 /* FrontWallTypeOperator.cpp in Sources */,
				2B0792502134E47D00A65EE1 /* Global_Escalators_Rung_Parameter.cpp in Sources */,
				2B0792722134E47D00A65EE1 /* Sel_Esca_Decking_Ctrl_Box.cpp in Sources */,
				2B56D16D2064E58F00DF2F76 /* Sel_HLLcd.cpp in Sources */,
				2B56D2172064E70E00DF2F76 /* arg_cache.cpp in Sources */,
				2B0791D92134E46100A65EE1 /* EscaPhotoelectricVisitor.cpp in Sources */,
				2B0792862134E47D00A65EE1 /* Sel_Esca_Skirt_Brush.cpp in Sources */,
				2B56D24D2064E70E00DF2F76 /* db_visitor.cpp in Sources */,
				2B56D2542064E70E00DF2F76 /* car_top_factory.cpp in Sources */,
				2B56D1512064E58F00DF2F76 /* Global_Setting_Hall.cpp in Sources */,
				2B56D18A2064E59000DF2F76 /* VrConfigInfo.cpp in Sources */,
				2B56D16F2064E58F00DF2F76 /* Sel_Jamb.cpp in Sources */,
				2B56D2492064E70E00DF2F76 /* car_wall_part.cpp in Sources */,
				2B56D1A42064E59000DF2F76 /* VrLopDisplayVisitor.cpp in Sources */,
				2B07925A2134E47D00A65EE1 /* Global_StepLight0_Parameter.cpp in Sources */,
				2B56D1712064E58F00DF2F76 /* Sel_Lop.cpp in Sources */,
				2B0791CB2134E46100A65EE1 /* EscaConfigVisitor.cpp in Sources */,
				2B0792902134E47D00A65EE1 /* Sel_Esca_Truss_Lighting.cpp in Sources */,
				2B56D1752064E58F00DF2F76 /* Sel_Mirror.cpp in Sources */,
				2B56D2442064E70E00DF2F76 /* ConfigOperator.cpp in Sources */,
				2B0791D32134E46100A65EE1 /* EscaHandrailGuidVisitor.cpp in Sources */,
				2B72CC8823C57A7400682713 /* hall_room_part.cpp in Sources */,
				2B56D1AC2064E59000DF2F76 /* VrTopVisitor.cpp in Sources */,
				2B72CC7E23C57A3500682713 /* VrHallShaftVisitor.cpp in Sources */,
				2B0792B72134E5AF00A65EE1 /* EscaSkirtLightingFactory.cpp in Sources */,
				2B0792482134E47D00A65EE1 /* Global_CopyObject.cpp in Sources */,
				2B0791C72134E46100A65EE1 /* EscaCombLightingVisitor.cpp in Sources */,
				2B56D2222064E70E00DF2F76 /* decoration_vr_array_visitor.cpp in Sources */,
				2B0791E32134E46100A65EE1 /* EscaSkirtLightingVisitor.cpp in Sources */,
				2B56D2132064E70E00DF2F76 /* ElevatorSize.cpp in Sources */,
				2B56D1672064E58F00DF2F76 /* Sel_HI.cpp in Sources */,
				2BC10E71207465DA00126085 /* svr_json_helper.cpp in Sources */,
				2B56D17A2064E59000DF2F76 /* vr_visitor_factory.cpp in Sources */,
				2BC10E7A207465DA00126085 /* svr_part_basic_info.cpp in Sources */,
				2BC10E72207465DA00126085 /* svr_wall_size_info.cpp in Sources */,
				2B07929A2134E51500A65EE1 /* dvi_tinyxml2.cpp in Sources */,
				2B56D25C2064E70E00DF2F76 /* PartTypeManager.cpp in Sources */,
				2B56D21F2064E70E00DF2F76 /* file_pool.cpp in Sources */,
				2B56D2352064E70E00DF2F76 /* ElevatorPartFactoryManager.cpp in Sources */,
				2B0792802134E47D00A65EE1 /* Sel_Esca_Side_Cladding.cpp in Sources */,
				2B07924A2134E47D00A65EE1 /* Global_Esca_PartMark.cpp in Sources */,
				2B0792B52134E5AF00A65EE1 /* EscaSideCladdingFactory.cpp in Sources */,
				2B56D2112064E70E00DF2F76 /* electric_factory.cpp in Sources */,
				2B0791C92134E46100A65EE1 /* EscaCombVisitor.cpp in Sources */,
				2B72CC6E23C57A1700682713 /* Sel_Door_Imported.cpp in Sources */,
				2BC10E6A207465DA00126085 /* svr_common_part.cpp in Sources */,
				2B56D18E2064E59000DF2F76 /* VrCopVisitor.cpp in Sources */,
				2B56D1A82064E59000DF2F76 /* VrMirrorVisitor.cpp in Sources */,
				2B07924E2134E47D00A65EE1 /* Global_Escalators_Parameter.cpp in Sources */,
				2B4CB15425B80D77004B0767 /* VrHallFloorVisitor.cpp in Sources */,
				2B56D2282064E70E00DF2F76 /* decoration_vr_interface.cpp in Sources */,
				2B56D09E2064E4E800DF2F76 /* json_value.cpp in Sources */,
				2B56D1782064E59000DF2F76 /* vr_controller.cpp in Sources */,
				2B56D1A62064E59000DF2F76 /* VrLopVisitor.cpp in Sources */,
				2B56D2602064E70E00DF2F76 /* Util.cpp in Sources */,
				2B56D1652064E58F00DF2F76 /* Sel_HandRail.cpp in Sources */,
				2B56D25A2064E70E00DF2F76 /* PartOperatorManager.cpp in Sources */,
				2B56D1AA2064E59000DF2F76 /* VrSightseeingVisitor.cpp in Sources */,
				2BC10E85207465DA00126085 /* svr_part_material.cpp in Sources */,
				2B07925E2134E47D00A65EE1 /* Global_Work0_Parameter.cpp in Sources */,
				2B56D1922064E59000DF2F76 /* VrGlobalInfo.cpp in Sources */,
				2B56D1612064E58F00DF2F76 /* Sel_CopLcd.cpp in Sources */,
				2B0791E92134E46100A65EE1 /* EscaStepVisitor.cpp in Sources */,
				2B56D2502064E70E00DF2F76 /* download_visitor.cpp in Sources */,
				2B72CC7123C57A1700682713 /* Sel_Shaft.cpp in Sources */,
				2B0792922134E47D00A65EE1 /* Sel_RGB_Share.cpp in Sources */,
				2B56D1692064E58F00DF2F76 /* Sel_HILcd.cpp in Sources */,
				2B0792522134E47D00A65EE1 /* Global_PartType_Info.cpp in Sources */,
				2B56D1572064E58F00DF2F76 /* ModelCarWallElem.cpp in Sources */,
				2BC10E6F207465DA00126085 /* svr_file_digital_info.cpp in Sources */,
				2B0791DF2134E46100A65EE1 /* EscaSideCladdingVisitor.cpp in Sources */,
				2BC10E73207465DA00126085 /* svr_material.cpp in Sources */,
				2B0792582134E47D00A65EE1 /* Global_Step_Parameter.cpp in Sources */,
				2B72CC7623C57A1700682713 /* Sel_Car_Shell.cpp in Sources */,
				2B56D09F2064E4E800DF2F76 /* json_writer.cpp in Sources */,
				2B56D24C2064E70E00DF2F76 /* ConfigAnalyzer.cpp in Sources */,
				2B07927E2134E47D00A65EE1 /* Sel_Esca_Scene.cpp in Sources */,
				2B56D2202064E70E00DF2F76 /* ConstValueMap.cpp in Sources */,
				2BC10E83207465DA00126085 /* svr_wall_element.cpp in Sources */,
				2B56D1982064E59000DF2F76 /* VrHallIndicatorDisplayVisitor.cpp in Sources */,
				2B56D2582064E70E00DF2F76 /* material_channel_pool.cpp in Sources */,
				2B0792742134E47D00A65EE1 /* Sel_Esca_Handrail.cpp in Sources */,
				2B07924C2134E47D00A65EE1 /* Global_Esca_Setting_Parameter.cpp in Sources */,
				2B0792992134E51500A65EE1 /* EscalatorSize.cpp in Sources */,
				2B0791DB2134E46100A65EE1 /* EscaScenesVisitor.cpp in Sources */,
				2BC10E6B207465DA00126085 /* svr_material_channel.cpp in Sources */,
				2B56D22C2064E70E00DF2F76 /* skirting_factory.cpp in Sources */,
				2B56D2162064E70E00DF2F76 /* config_factory.cpp in Sources */,
				2B56D1862064E59000DF2F76 /* VrCarWallVisitor.cpp in Sources */,
				2BC10E7E207465DA00126085 /* svr_material_special_rule.cpp in Sources */,
				2B56D1532064E58F00DF2F76 /* Global_Setting_Parameter.cpp in Sources */,
				2B56D1942064E59000DF2F76 /* VrHallConfigVisitor.cpp in Sources */,
				2B0792442134E47D00A65EE1 /* Global_Camera_Position.cpp in Sources */,
				2B0791ED2134E46100A65EE1 /* EscaTrussLightingVisitor.cpp in Sources */,
				2B0792562134E47D00A65EE1 /* Global_Step0_Parameter.cpp in Sources */,
				2B56D19C2064E59000DF2F76 /* VrHandrailVisitor.cpp in Sources */,
				2B72CC6F23C57A1700682713 /* Sel_HallDoor_Imported.cpp in Sources */,
				2B0791EB2134E46100A65EE1 /* EscaTrafficLightVisitor.cpp in Sources */,
				2B56D14D2064E58F00DF2F76 /* Global_Select.cpp in Sources */,
				2B0792422134E47D00A65EE1 /* Global_Camera_EffectParameter.cpp in Sources */,
				2B0792762134E47D00A65EE1 /* Sel_Esca_Handrail_Enter.cpp in Sources */,
				2B56D16B2064E58F00DF2F76 /* Sel_HL.cpp in Sources */,
				2B56D1962064E59000DF2F76 /* VrHallDoorVisitor.cpp in Sources */,
				2B72CC8A23C57A7400682713 /* hall_room_part_factory.cpp in Sources */,
				2B56D15B2064E58F00DF2F76 /* Sel_CarIndicator.cpp in Sources */,
				2B0792602134E47D00A65EE1 /* Global_Work1_Parameter.cpp in Sources */,
				2B56D1552064E58F00DF2F76 /* material_channel.cpp in Sources */,
				2B56D19A2064E59000DF2F76 /* VrHallIndicatorVisitor.cpp in Sources */,
				2B56D2312064E70E00DF2F76 /* ElevatorConfig.cpp in Sources */,
				2B0791D72134E46100A65EE1 /* EscaHandrailVisitor.cpp in Sources */,
				2B56D1472064E58F00DF2F76 /* DGBaseVisitor.cpp in Sources */,
				2B07928C2134E47D00A65EE1 /* Sel_Esca_Step_Lighting.cpp in Sources */,
				2B56D2562064E70E00DF2F76 /* car_wall_factory.cpp in Sources */,
				2B0792782134E47D00A65EE1 /* Sel_Esca_Handrail_Guid.cpp in Sources */,
				2BC10E91207466E200126085 /* lru_cache.cpp in Sources */,
				2B0792542134E47D00A65EE1 /* Global_Script_Floor.cpp in Sources */,
				2B0791CD2134E46100A65EE1 /* EscaDeckingCtrlBoxVisitor.cpp in Sources */,
				2B56D25E2064E70E00DF2F76 /* ElevatorPartOperator.cpp in Sources */,
				2B56D0A62064E54C00DF2F76 /* HttpAsyncManage.cpp in Sources */,
				2B56D17E2064E59000DF2F76 /* VrBottomVisitor.cpp in Sources */,
				2B56D2422064E70E00DF2F76 /* elevator_size_parser.cpp in Sources */,
				2B56D1492064E58F00DF2F76 /* Global_Hwndmsg.cpp in Sources */,
				2B07928E2134E47D00A65EE1 /* Sel_Esca_Traffic_Light.cpp in Sources */,
				2B0791BF2134E46100A65EE1 /* EscaAccessOverVisitor.cpp in Sources */,
				2B0791CF2134E46100A65EE1 /* EscaDeckingVisitor.cpp in Sources */,
				2B07928A2134E47D00A65EE1 /* Sel_Esca_Step.cpp in Sources */,
				2B0792AD2134E5AF00A65EE1 /* esca_elevator_part_factory.cpp in Sources */,
				2B56D23A2064E70E00DF2F76 /* ElevatorConfigManager.cpp in Sources */,
				2B72CC7D23C57A3500682713 /* VrCarShellVisitor.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2B56D0132064D14600DF2F76 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2B56CF492064C90800DF2F76 /* DecorationVrInterfaceDll_mbcs */;
			targetProxy = 2B56D0122064D14600DF2F76 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2B4CB14B25B80B3B004B0767 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_INPUT_FILETYPE = sourcecode.cpp.objcpp;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					XIZI_OTIS,
					ESCALATOR,
				);
				HEADER_SEARCH_PATHS = (
					../../../rse/tools/RseDiscoverGraph_mt/RseDiscoverGraph/Include,
					./include,
					./vr_visitor/array_visitor,
					./vr_visitor,
					./jsoncpp/include,
					./jsoncpp/src/lib_json,
					./netWork,
					.,
					../../../rse/engine_mt/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				OBJROOT = ../../../rse/static_lib/intermediate;
				OTHER_LDFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SYMROOT = ../../../rse/static_lib/StaticDebugMbcs6xio;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2B4CB14C25B80B3B004B0767 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)";
				COPY_PHASE_STRIP = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INPUT_FILETYPE = sourcecode.cpp.objcpp;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					RENDER_SKETCHER_APPLE,
					RSE_STATIC_LIB,
					XIZI_OTIS,
					ESCALATOR,
				);
				HEADER_SEARCH_PATHS = (
					../../../rse/tools/RseDiscoverGraph_mt/RseDiscoverGraph/Include,
					./include,
					./vr_visitor/array_visitor,
					./vr_visitor,
					./jsoncpp/include,
					./jsoncpp/src/lib_json,
					./netWork,
					.,
					../../../rse/engine_mt/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				OBJROOT = ../../../rse/static_lib/intermediate;
				OTHER_LDFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SYMROOT = ../../../rse/static_lib/StaticReleaseMbcs6xio;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2B56CF512064C90800DF2F76 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_INPUT_FILETYPE = sourcecode.cpp.objcpp;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				HEADER_SEARCH_PATHS = (
					../../../rse/tools/RseDiscoverGraph_mt/RseDiscoverGraph/Include,
					./include,
					./vr_visitor/array_visitor,
					./vr_visitor,
					./jsoncpp/include,
					./jsoncpp/src/lib_json,
					./netWork,
					.,
					../../../rse/engine_mt/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				OBJROOT = ../../../rse/static_lib/intermediate;
				OTHER_LDFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)_x6b64asd";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SYMROOT = ../../../rse/static_lib/StaticDebugMbcs6;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2B56CF522064C90800DF2F76 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)";
				COPY_PHASE_STRIP = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INPUT_FILETYPE = sourcecode.cpp.objcpp;
				GCC_NO_COMMON_BLOCKS = YES;
				HEADER_SEARCH_PATHS = (
					../../../rse/tools/RseDiscoverGraph_mt/RseDiscoverGraph/Include,
					./include,
					./vr_visitor/array_visitor,
					./vr_visitor,
					./jsoncpp/include,
					./jsoncpp/src/lib_json,
					./netWork,
					.,
					../../../rse/engine_mt/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				OBJROOT = ../../../rse/static_lib/intermediate;
				OTHER_LDFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)_x6b64as";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SYMROOT = ../../../rse/static_lib/StaticReleaseMbcs6;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D049E5361ADF6B7C004DC3D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_INPUT_FILETYPE = sourcecode.cpp.objcpp;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					RENDER_SKETCHER_APPLE,
					RSE_STATIC_LIB,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "";
				SDKROOT = iphoneos;
				SYMROOT = ../../../rse/static_lib/StaticDebugMbcs6;
			};
			name = Debug;
		};
		D049E5371ADF6B7C004DC3D1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_INPUT_FILETYPE = sourcecode.cpp.objcpp;
				GCC_PREPROCESSOR_DEFINITIONS = (
					RENDER_SKETCHER_APPLE,
					RSE_STATIC_LIB,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = "";
				SDKROOT = iphoneos;
				SYMROOT = ../../../rse/static_lib/StaticReleaseMbcs6;
			};
			name = Release;
		};
		D05124A91BFF17B200CDA819 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
				SYMROOT = ../../../static_lib/StaticDebugMbcs6;
			};
			name = Debug;
		};
		D05124AA1BFF17B200CDA819 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
				SYMROOT = ../../../static_lib/StaticReleaseMbcs6;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2B4CB14A25B80B3B004B0767 /* Build configuration list for PBXNativeTarget "DecorationVrInterfaceDll_mbcs_xio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B4CB14B25B80B3B004B0767 /* Debug */,
				2B4CB14C25B80B3B004B0767 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2B56CF502064C90800DF2F76 /* Build configuration list for PBXNativeTarget "DecorationVrInterfaceDll_mbcs" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B56CF512064C90800DF2F76 /* Debug */,
				2B56CF522064C90800DF2F76 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D049E5281ADF6B7C004DC3D1 /* Build configuration list for PBXProject "DecorationVrInterfaceDll" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D049E5361ADF6B7C004DC3D1 /* Debug */,
				D049E5371ADF6B7C004DC3D1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D05124A81BFF17B200CDA819 /* Build configuration list for PBXAggregateTarget "BUILD_ALL_MBCS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D05124A91BFF17B200CDA819 /* Debug */,
				D05124AA1BFF17B200CDA819 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = D049E5251ADF6B7C004DC3D1 /* Project object */;
}
