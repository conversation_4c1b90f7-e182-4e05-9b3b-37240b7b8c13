﻿#include "stdafx.h"
#include "VrHallConfigVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Hall.h"
#include "hall_room_part.h"
#ifdef TIAN_SUO
#include "common_part.h"
#endif

namespace decoration_vr_interface
{
	VrHallConfigVisitor::VrHallConfigVisitor()
	{
		part_type_ = PartTypeHallConfig;

		available_parttypeid_list_.push_back(PartTypeHallConfig);
	}


	VrHallConfigVisitor::~VrHallConfigVisitor()
	{
	}

	void VrHallConfigVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Hall>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}
		else
		{
			model_->init(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}
	}

	bool VrHallConfigVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		Global_Setting_Hall* hall = VrGlobalInfo::Instance()->get_hall_array_visitor();
		hall->Clear();

		auto list = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->get_elevator_config_list_ptr();
		std::for_each(list->begin(), list->end(), [&hall](IElevatorConfigPtr elevator)
		{
			int row = hall->AddRow();
			auto setting = elevator->GetElevatorSetting();
			auto size = elevator->GetElevatorSize();

			hall->Setcar_Size_X(row, size->width_);
			hall->Setcar_Size_Z(row, size->depth_);

			hall->Setdistance(row, setting->distance_to_next_);

			//hall->Setdoor_Type(row, setting->door_type_);
			hall->Sethalldoor_SIze_X(row, size->door_width_);
			hall->Sethalldoor_SIze_Y(row, size->door_height_);
		});

		//2019.12.10
		int door_type = VrGlobalInfo::Instance()->GetDoorType();
        VrGlobalInfo::Instance()->UpdateDoorType(door_type); 
        
		auto mode = VrGlobalInfo::Instance()->GetRunningMode();
		float w = VrGlobalInfo::Instance()->GetDoorWidth();
		float h = VrGlobalInfo::Instance()->GetDoorHeight();
		if (mode == AR_RUNNING_MODE && w > 1.0f && h > 1.0f)
		{
			for (int i = 0, count = hall->GetRowCount(); i < count; ++i)
			{
				hall->Sethalldoor_SIze_X(i, w);
				hall->Sethalldoor_SIze_Y(i, h);
			}
		}

		//////////////////////////////////////////////////////////////////////////
		auto vr_glob = VrGlobalInfo::Instance();

		model_->Clear();
		auto row = model_->AddRow();

		int64 part_id = -1;
		tstring model_file;
		auto hall_room = static_cast<HallRoomPart*>(vr_change->config_->GetPart(PartTypeHall));
		if (hall_room)
		{
			part_id = hall_room->GetPartId();
		}
#if defined(XIZI_OTIS)
		//目前的做法是
		//数据维护工具录入3个大厅的信息，发布成商品，将商品的id修改为1， 2， 3
		//3个大厅模型无需从服务器下载
		bool load_new_hall =false;
		switch (part_id)
		{
		case 221001837192977://1: 别墅梯大厅玻璃结构
			model_file = TSTR("Hall/Hall_01.zxrm");
			break;
		case 221001837192978://2: 两门大厅
			model_file = TSTR("Hall/Hall_02.zxrm");
			break;
		case 221001837192979://3: 别墅梯大厅混凝土结构
			model_file = TSTR("Hall/Hall_03.zxrm");
			break;
		case 221001837192980://4: 平台梯
			model_file = TSTR("Hall/Hall_04_PT_Back.zxrm");
			break;
		case 221001837192981://5: 平台梯
			model_file = TSTR("Hall/Hall_05_PT_Side.zxrm");
			break;
		default:
			load_new_hall = part_id > 0;
			if (load_new_hall)
			{
				model_file = hall_room->LoadModel();
			}
			else
			{
				model_file = TSTR("Hall/Hall_02.zxrm");
			}
			break;
		}
#else
		bool load_new_hall = part_id > 0;
		if (load_new_hall)
		{
			model_file = hall_room->LoadModel();
		}
		else
		{
#if defined(TIAN_SUO)
			//2021-05-21 syp466
			//天梭有两个大厅，一个普通，另一个是井道使用的
			auto shaft = static_cast<CommonPart*>(vr_change->config_->GetPart(PartTypeHallShaft));
			if (shaft && shaft->GetPartId() > 0)
			{
				//西继迅达so编译：装潢使用XIZI_OTIS，vrinterface使用TIAN_SUO;但是大厅只有一个
#if defined(XJ)
				model_file = TSTR("Hall/Hall_01.zxrm");
#else
				model_file = TSTR("Hall/Hall_02.zxrm");
#endif		
			}
			else
			{
				model_file = TSTR("Hall/Hall_01.zxrm");
			}
#else
			//Hall_villa只是测试用，没有在项目中正式使用，在web版运行时出现问题了，所以注掉了 syp011 2020-05-22
			//auto elev_size_ptr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize();
			//if (elev_size_ptr->width_ == 110 && elev_size_ptr->depth_ == 110)
			//{
			//	model_file = TSTR("Hall/Hall_villa.zxrm");
			//}
			//else
			{
				model_file = TSTR("Hall/Hall_01.zxrm");
			}
#endif
		}
#endif
		auto dir = vr_glob->get_config_info()->get_model_dir();
		auto path = Util::CombinePath(dir, model_file);
		model_->SetPath(0, path.c_str());
		if (load_new_hall)
		{
			AddFileToModelCache(PartTypeHall, part_id, model_file, path);
		}

		return true;
	}
	 
	void VrHallConfigVisitor::PrintData(const tchar* file_name)
	{
		Global_Setting_Hall* hall = VrGlobalInfo::Instance()->get_hall_array_visitor();
		hall->PrintData(file_name);
		model_->PrintData(file_name);
	}

}
