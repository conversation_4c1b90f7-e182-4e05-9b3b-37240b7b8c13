#include "stdafx.h"
#include "ElevatorConfig.h"
#include "config_part.h"
namespace decoration_vr_interface
{
	ElevatorConfig::ElevatorConfig()
	{
	}


	ElevatorConfig::~ElevatorConfig()
	{
	}

	IConfigPtr ElevatorConfig::GetCarConfig()
	{
		return car_;
	}

	void ElevatorConfig::SetCarConfig(IConfigPtr material)
	{
		auto car_config = static_cast<Config*>(material.get());
		car_config->SetElevatorConfig(this);
		car_ = material;
	}

	IConfigPtr ElevatorConfig::GetHallConfig(int pos)
	{
		return hall_;
	}

	void ElevatorConfig::SetHallConfig(int pos, IConfigPtr material)
	{
		auto hall_config = static_cast<Config*>(material.get());
		hall_config->SetElevatorConfig(this);
		hall_ = material;
	}

	decoration_vr_interface::IConfigPtr ElevatorConfig::GetEscalatorConfig()
	{
		return escalator_;
	}

	void ElevatorConfig::SetEscalatorConfig(IConfigPtr material)
	{
		escalator_ = material;
	}

	ElevatorSize* ElevatorConfig::GetElevatorSize()
	{
		return &elevator_size_;
	}

	void ElevatorConfig::SetElevatorSize(const ElevatorSize& size)
	{
		elevator_size_ = size;
	}

	EscalatorSize* ElevatorConfig::GetEscalatorSize()
	{
		return &escalator_size_;
	}

	void ElevatorConfig::SetEscalatorSize(const EscalatorSize& size)
	{
		escalator_size_ = size;
	}

	ElevatorSetting* ElevatorConfig::GetElevatorSetting()
	{
		return &elevator_setting_;
	}


	void ElevatorConfig::SetElevatorSetting(const ElevatorSetting& setting)
	{
		elevator_setting_ = setting;
	}

	ElevatorSetting* ElevatorConfig::GetEscalatorSetting()
	{
		return &escalator_setting_;
	}

	void ElevatorConfig::SetEscalatorSetting(const ElevatorSetting& setting)
	{
		escalator_setting_ = setting;
	}

}