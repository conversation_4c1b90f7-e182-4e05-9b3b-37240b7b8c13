﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClInclude Include="Util.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="PartTypeManager.h" />
    <ClInclude Include="PartOperatorManager.h" />
    <ClInclude Include="material_channel_pool.h" />
    <ClInclude Include="IPartTypeManager.h" />
    <ClInclude Include="IElevatorPartFactory.h" />
    <ClInclude Include="IElevatorPart.h" />
    <ClInclude Include="IElevatorConfig.h" />
    <ClInclude Include="IDownloadVisitor.h" />
    <ClInclude Include="IDbVisitor.h" />
    <ClInclude Include="IConfig.h" />
    <ClInclude Include="GlobalInfoDataCommon.h" />
    <ClInclude Include="file_pool.h" />
    <ClInclude Include="ElevatorPartOperator.h" />
    <ClInclude Include="ElevatorPartFactoryManager.h" />
    <ClInclude Include="ElevatorConfigManager.h" />
    <ClInclude Include="ElevatorConfig.h" />
    <ClInclude Include="download_visitor.h" />
    <ClInclude Include="decoration_vr_interface_include.h" />
    <ClInclude Include="decoration_vr_interface.h" />
    <ClInclude Include="decoration_vr_array_visitor.h" />
    <ClInclude Include="db_visitor.h" />
    <ClInclude Include="ConstValueMap.h" />
    <ClInclude Include="ConstPartInnerParams.h" />
    <ClInclude Include="ConfigAnalyzer.h" />
    <ClInclude Include="BaseVisitor.h" />
    <ClInclude Include="vr_visitor\IVrVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\vr_controller.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\vr_visitor_factory.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrAccessoryVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrBottomVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarConfigVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarDoorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarIndicatorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarWallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrChangeArg.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrConfigInfo.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCopDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCopVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrFrontWallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrGlobalInfo.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallConfigVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallDoorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallIndicatorDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallIndicatorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHandrailVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrJambVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLanternDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLanternVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLopDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLopVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrMirrorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrSightseeingVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrTopVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\DGBaseVisitor.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Hwndmsg.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Loading_Scence.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Select.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Car.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Hall.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Parameter.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\material_channel.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\ModelCarWallElem.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Accessory.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_CarIndicator.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Ceiling.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Cop.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_CopLcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Hall.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HandRail.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HI.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HILcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HL.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HLLcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Jamb.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Lop.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_LopLcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Mirror.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="netWork\HttpAsyncManage.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="skirting_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="skirting_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="electric_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="electric_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="config_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="common_part_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="common_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_wall_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_wall_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_top_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_top.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="ElevatorSize.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="BasePartOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="ConfigOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="ElectricOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="FrontWallTypeOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="include\decoration_vr_interface_lib.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_array_visitor.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_callback.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_interface.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_interface_extend.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\MsgParaValue.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\part_type.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\allocator.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\assertions.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\autolink.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\config.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\features.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\forwards.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\json.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\reader.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\value.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\version.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\writer.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\src\lib_json\json_tool.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="config_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="arg_cache.h">
      <Filter>parse_str</Filter>
    </ClInclude>
    <ClInclude Include="elevator_size_parser.h">
      <Filter>parse_str</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\BaseFactory.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_base_data.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_car_wall.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_common_part.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_config.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_file_digital_info.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_json_helper.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_material.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_material_channel.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_material_special_rule.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_part_basic_info.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_part_material.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_part_model_info.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_wall_element.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_wall_size_info.h">
      <Filter>parse_json</Filter>
    </ClInclude>
    <ClInclude Include="util\lru_cache.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="util\sha256.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="dvi_tinyxml2.h" />
    <ClInclude Include="esca\esca_elevator_part.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\esca_elevator_part_factory.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaAcessOverFactory.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaBalustradeFactory.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaHandrailEnterFactory.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaSideCladdingFactory.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaSkirtLightingFactory.h">
      <Filter>esca</Filter>
    </ClInclude>
    <ClInclude Include="EscalatorSize.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="download_assist.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Shaft.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Door_Imported.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Car_Shell.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarShellVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallShaftVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="hall_room_part_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="hall_room_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallFloorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallWallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Other_Datas.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallFireBoxVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_FireBox.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrEmidsVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Emids.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Util.cpp" />
    <ClCompile Include="stdafx.cpp" />
    <ClCompile Include="PartTypeManager.cpp" />
    <ClCompile Include="PartOperatorManager.cpp" />
    <ClCompile Include="material_channel_pool.cpp" />
    <ClCompile Include="GlobalInfoDataCommon.cpp" />
    <ClCompile Include="file_pool.cpp" />
    <ClCompile Include="ElevatorPartOperator.cpp" />
    <ClCompile Include="ElevatorPartFactoryManager.cpp" />
    <ClCompile Include="ElevatorConfigManager.cpp" />
    <ClCompile Include="ElevatorConfig.cpp" />
    <ClCompile Include="download_visitor.cpp" />
    <ClCompile Include="decoration_vr_interface_lib.cpp" />
    <ClCompile Include="decoration_vr_interface.cpp" />
    <ClCompile Include="decoration_vr_array_visitor.cpp" />
    <ClCompile Include="db_visitor.cpp" />
    <ClCompile Include="ConstValueMap.cpp" />
    <ClCompile Include="ConfigAnalyzer.cpp" />
    <ClCompile Include="BaseVisitor.cpp" />
    <ClCompile Include="vr_visitor\vr_controller.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\vr_visitor_factory.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrAccessoryVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrBottomVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarConfigVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarDoorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarIndicatorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarWallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrChangeArg.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrConfigInfo.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCopDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCopVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrFrontWallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrGlobalInfo.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallConfigVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallDoorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallIndicatorDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallIndicatorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHandrailVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrJambVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLanternDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLanternVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLopDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLopVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrMirrorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrSightseeingVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrTopVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\DGBaseVisitor.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Hwndmsg.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Loading_Scence.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Select.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Car.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Hall.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Parameter.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\material_channel.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\ModelCarWallElem.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Accessory.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_CarIndicator.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Ceiling.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Cop.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_CopLcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Hall.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HandRail.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HI.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HILcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HL.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HLLcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Jamb.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Lop.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_LopLcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Mirror.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="netWork\HttpAsyncManage.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="skirting_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="skirting_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="electric_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="electric_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="config_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="common_part_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="common_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_wall_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_wall_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_top_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_top.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="ElevatorSize.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="BasePartOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="ConfigOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="ElectricOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="FrontWallTypeOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_reader.cpp">
      <Filter>jsoncpp</Filter>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_value.cpp">
      <Filter>jsoncpp</Filter>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_writer.cpp">
      <Filter>jsoncpp</Filter>
    </ClCompile>
    <ClCompile Include="config_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="arg_cache.cpp">
      <Filter>parse_str</Filter>
    </ClCompile>
    <ClCompile Include="elevator_size_parser.cpp">
      <Filter>parse_str</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_car_wall.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_common_part.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_config.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_file_digital_info.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_json_helper.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_material.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_material_channel.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_material_special_rule.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_part_basic_info.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_part_material.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_part_model_info.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_wall_element.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_wall_size_info.cpp">
      <Filter>parse_json</Filter>
    </ClCompile>
    <ClCompile Include="util\lru_cache.cpp">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="util\sha256.cpp">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="dvi_tinyxml2.cpp" />
    <ClCompile Include="esca\esca_elevator_part.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\esca_elevator_part_factory.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaAcessOverFactory.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaBalustradeFactory.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaHandrailEnterFactory.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaSideCladdingFactory.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaSkirtLightingFactory.cpp">
      <Filter>esca</Filter>
    </ClCompile>
    <ClCompile Include="EscalatorSize.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="download_assist.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Shaft.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Door_Imported.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Car_Shell.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallShaftVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarShellVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="hall_room_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="hall_room_part_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallFloorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallWallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Other_Datas.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallFireBoxVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_FireBox.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrEmidsVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Emids.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="vr_visitor">
      <UniqueIdentifier>{71da847d-db09-4103-8080-936777fb3a6f}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor\array_visitor">
      <UniqueIdentifier>{8e8a3c03-c1c6-479d-a6ae-b965444f1517}</UniqueIdentifier>
    </Filter>
    <Filter Include="part">
      <UniqueIdentifier>{b3ab192d-0b6f-4e5a-96f1-b1a3c312d28f}</UniqueIdentifier>
    </Filter>
    <Filter Include="part_operator">
      <UniqueIdentifier>{e5312c90-c81d-4715-8d4d-e769664baa3b}</UniqueIdentifier>
    </Filter>
    <Filter Include="network">
      <UniqueIdentifier>{f3eb9c76-6b6a-4da9-9d23-9f2d4a101b64}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{4f6c728c-be4e-49b4-82ff-8b6a0c4ce9e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="jsoncpp">
      <UniqueIdentifier>{fbbe84d8-5363-4e85-b813-5e4d35a39d61}</UniqueIdentifier>
    </Filter>
    <Filter Include="parse_str">
      <UniqueIdentifier>{c97af192-c258-42c5-ade6-8ea5a7f10ad0}</UniqueIdentifier>
    </Filter>
    <Filter Include="parse_json">
      <UniqueIdentifier>{74cf2c72-3050-4f14-92d6-baf36a213e25}</UniqueIdentifier>
    </Filter>
    <Filter Include="util">
      <UniqueIdentifier>{5a7e7328-eca6-4fd6-82d8-dac6cf44c9e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor\array_visitor\esca">
      <UniqueIdentifier>{4a003a7f-f7ac-43f6-9b0a-4a4dc2b9e316}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor\esca">
      <UniqueIdentifier>{b57c1cc8-419e-49a4-9ecd-90e0541fe17f}</UniqueIdentifier>
    </Filter>
    <Filter Include="esca">
      <UniqueIdentifier>{c36ff789-7a5c-4434-8ec3-321f4a3a42bb}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="jsoncpp\src\lib_json\json_valueiterator.inl">
      <Filter>jsoncpp</Filter>
    </None>
    <None Include="jsoncpp\src\lib_json\version.h.in">
      <Filter>jsoncpp</Filter>
    </None>
  </ItemGroup>
</Project>