#pragma once
#include "esca_elevator_part_factory.h"
namespace decoration_vr_interface
{
	class EscaSkirtLightingFactory : public EscalatorPartFactory
	{
	public:
		EscaSkirtLightingFactory(int part_type)
			:EscalatorPartFactory(part_type)
		{}
		virtual ~EscaSkirtLightingFactory(){}
	protected:
		virtual IElevatorPartPtr GetPart(FactoryArgsOfLoadingFromDb* args);
		virtual IElevatorPartPtr GetPart(FactoryArgsOfConfigLoading* args);
	};

}