#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Global_Select : public DGBaseVisitor
{
public:
	Global_Select(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	int GetCurrentElevtor(int row);
	void SetCurrentElevtor(int row, int val);

	int GetCurrentCarId(int row);
	void SetCurrentCarId(int row, int val);

	bool GetIsHaveAuxCop(int row);
	void SetIsHaveAuxCop(int row, bool val);

	bool GetIsHaveHcCop(int row);
	void SetIsHaveHcCop(int row, bool val);

	bool GetIsHaveAuxHcCop(int row);
	void SetIsHaveAuxHcCop(int row, bool val);

	bool GetIsHaveCarIndicator(int row);
	void SetIsHaveCarIndicator(int row, bool val);

	bool GetIsHaveWallTrim(int row);
	void SetIsHaveWallTrim(int row, bool val);

	bool GetIsHaveCarDoorTrim(int row);
	void SetIsHaveCarDoorTrim(int row, bool val);

	bool GetIsHaveThiefCamera(int row);
	void SetIsHaveThiefCamera(int row, bool val);

	bool GetIsHaveProtectedWall(int row);
	void SetIsHaveProtectedWall(int row, bool val);

	bool GetIsHaveHallIndicator(int row);
	void SetIsHaveHallIndicator(int row, bool val);

	bool GetIsHaveLantern(int row);
	void SetIsHaveLantern(int row, bool val);

	bool GetIsHaveHallDoorTrim(int row);
	void SetIsHaveHallDoorTrim(int row, bool val);

	int GetDoorType(int row);
	void SetDoorType(int row, int val);

	int GetStopSwitcherFloorCount(int row);
	void SetStopSwitcherFloorCount(int row, int val);

	int GetElevatorWeight(int row);
	void SetElevatorWeight(int row, int val);

	int GetFrontWallType(int row);
	void SetFrontWallType(int row, int val);

	int GetCopButtonCount(int row);
	void SetCopButtonCount(int row, int val);

	bool GetIsAuxCopSameAsMain(int row);
	void SetIsAuxCopSameAsMain(int row, bool val);

	bool GetIsAuxHcCopSameAsMain(int row);
	void SetIsAuxHcCopSameAsMain(int row, bool val);

	int GetCarType(int row);
	void SetCarType(int row, int val);

	int GetHandrailType(int row);
	void SetHandrailType(int row, int val);

	int GetcarType(int row);
	void SetcarType(int row, int val);

	int GetThroughBackWallType(int row);
	void SetThroughBackWallType(int row, int val);

	int GetThroughBackDoorType(int row);
	void SetThroughBackDoorType(int row, int val);
};
}