#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Ceiling;
	class MaterialChannel;

	class VrTopVisitor :
		public BaseVisitor
	{
	public:
		VrTopVisitor();
		~VrTopVisitor();

		DEFINE_CREATE_FUN(VrTopVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void NotifyVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<Sel_Ceiling> model_;
		std::shared_ptr<MaterialChannel> material_;

		bool first_run_;
		bool is_ceiling_thickness_changed_;
	};

}