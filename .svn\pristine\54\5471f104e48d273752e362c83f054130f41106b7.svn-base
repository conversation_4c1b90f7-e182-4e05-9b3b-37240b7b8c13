#include "stdafx.h"
#include "VrGlobalInfo.h"
#include "esca_elevator_part.h"

#include "EscaSkirtBrushVisitor.h"

namespace decoration_vr_interface
{

	VrEscaSkirtBrushVisitor::VrEscaSkirtBrushVisitor()
	{
		part_type_ = PartTypeEscaSkirtBrush;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrEscaSkirtBrushVisitor::~VrEscaSkirtBrushVisitor()
	{

	}

	void VrEscaSkirtBrushVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<esca::Sel_Esca_Skirt_Brush>(scene, type_info->model_array_.c_str());
		}
		else
		{
			model_->init(scene, type_info->model_array_.c_str());
		}
	}

	bool VrEscaSkirtBrushVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (!vr_change)
		{
			return false;
		}

		auto part = static_cast<EscalatorPart*>(vr_change->config_->GetPart(part_type_));

		if (model_->GetRowCount() < 1)
		{
			model_->AddRow();
		}

		if (part)
		{
			model_->Setbool_select(0, true);

			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
			auto model_file = part->LoadModel();
			auto model_path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(part->GetPartType(), part->GetPartId(), model_file, model_path);
			model_->Setstr_Path(0, model_path.c_str());
		}
		else
		{
			model_->Setbool_select(0, false);
		}
		
		return true;
	}

	void VrEscaSkirtBrushVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
	}

}