#include "stdafx.h"
#include "VrGlobalInfo.h"
#include "esca_elevator_part.h"
#include "material_channel.h"
#include "EscaAccessOverVisitor.h"

namespace decoration_vr_interface
{

	VrEscaAccessOverVisitor::VrEscaAccessOverVisitor()
	{
		part_type_ = PartTypeEscaAcessOver;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrEscaAccessOverVisitor::~VrEscaAccessOverVisitor()
	{

	}

	void VrEscaAccessOverVisitor::Initialize()
	{
		LOGI("VrEscaAccessOverVisitor::Initialize in\n");
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();

		if (scene == nullptr)
		{
			LOGI("scene == nullptr\n");
		}

		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		
		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<esca::Sel_Esca_Access_Cover>(scene, type_info->model_array_.c_str());
		}
		else
		{
			model_->init(scene, type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(scene, type_info->material_array_.c_str());
		}
		else
		{
			material_->init(scene, type_info->material_array_.c_str());
		}
	}

	bool VrEscaAccessOverVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (!vr_change)
		{
			return false;
		}

		auto part = static_cast<EscaAcessOver*>(vr_change->config_->GetPart(part_type_));
		if (!part)
		{
			return false;
		}

		if (model_->GetRowCount() < 1)
		{
			model_->AddRow();
		}
		
		/*model_->Setint_DownStates(0, part->GetDownFloorNumber());
		model_->Setint_UpStates(0, part->GetUpFloorNumber());*/

		auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		auto model_file = part->LoadModel();
		auto model_path = Util::CombinePath(dir, model_file);
		AddFileToModelCache(part->GetPartType(), part->GetPartId(), model_file, model_path);
		model_->Setstr_Path(0, model_path.c_str());

		if (part->GetPartMaterial() > 0)
		{
			auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

			rse::vector<MaterialInfo> material_infos;
			material_infos.push_back(MaterialInfo(part->GetPartMaterial(), 1, type_info->vr_type_));

			VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), type_info->vr_type_, material_infos, false);
		}

		return true;
	}

	void VrEscaAccessOverVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
		material_->PrintData(file_name);
	}

}
