#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Sel_Esca_Access_Cover : public DGBaseVisitor
		{
		public:
			Sel_Esca_Access_Cover(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			rse::string Getstr_Path(int row);
			void Setstr_Path(int row, const tchar* val);

			int Getint_DownStates(int row);
			void Setint_DownStates(int row, int val);

			int Getint_UpStates(int row);
			void Setint_UpStates(int row, int val);

		};
	}

}