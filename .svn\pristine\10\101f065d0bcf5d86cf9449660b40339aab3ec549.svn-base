#ifndef _VR_EscaSkirtLightingVisitor_H_
#define _VR_EscaSkirtLightingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Skirt_Lighting.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaSkirtLightingVisitor : public BaseVisitor
	{
	public:
		VrEscaSkirtLightingVisitor();
		virtual ~VrEscaSkirtLightingVisitor();

		DEFINE_CREATE_FUN(VrEscaSkirtLightingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Skirt_Lighting> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif