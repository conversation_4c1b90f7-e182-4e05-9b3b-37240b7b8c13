#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Lop;
class MaterialLopButton;
class MaterialChannel;

class VrLopVisitor : public BaseVisitor
{
public:
	VrLopVisitor();
	~VrLopVisitor();

	DEFINE_CREATE_FUN(VrLopVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	int GetSetupPos(int pos, int ele_id);
	std::shared_ptr<Sel_Lop> model_;
	std::shared_ptr<MaterialChannel> material_;
	std::shared_ptr<MaterialLopButton> material_button_;
	tstring GetButtonPanelTexture(int flag);
};
}
