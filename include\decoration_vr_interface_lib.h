#pragma once

#ifdef DECORATION_VR_INTERFACELIB
#define DECORATION_VR_INTERFACE_API
#else
#ifdef  RSE_STATIC_LIB
#define DECORATION_VR_INTERFACE_API
#else
#ifdef DECORATIONVRINTERFACEDLL_EXPORTS
#define DECORATION_VR_INTERFACE_API __declspec(dllexport)
#else
#define DECORATION_VR_INTERFACE_API __declspec(dllimport)
#endif
#endif
#endif

typedef int64_t int64;

#include "part_type.h"
#include "i_decoration_vr_array_visitor.h"
#include "i_decoration_vr_interface.h"
#include "i_decoration_vr_interface_extend.h"
#if defined(RENDER_SKETCHER_HTML)
#include "i_html_decoration_vr_interface.h"
#endif

namespace decoration_vr_interface
{
	extern DECORATION_VR_INTERFACE_API IDecorationVrInterface* GetDecorationVrInterface();
	extern DECORATION_VR_INTERFACE_API void DeleteDecorationVrInterface();
	extern DECORATION_VR_INTERFACE_API IDecorationVrInterfaceExtend* GetDecorationVrInterfaceExtend();

#if defined(RENDER_SKETCHER_HTML)
	extern DECORATION_VR_INTERFACE_API IHTMLDecorationVrInterface* GetHTMLDecorationVrInterface();
	extern DECORATION_VR_INTERFACE_API void DeleteHTMLDecorationVrInterface();
#endif
}
