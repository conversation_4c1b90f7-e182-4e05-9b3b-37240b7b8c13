#ifndef _LRU_CACHE_H_
#define _LRU_CACHE_H_

#include <stdint.h>

#include <functional>
#include <list>
#include <unordered_map>
#include <string>

namespace util
{

	class lru_cache
	{
	public:
		lru_cache();
		~lru_cache();

		void set_capacity(int32_t capacity);

		void set_config_file(const rse::string& file);

		bool load();

		bool save();

		void clear();

		int32_t get_capacity();

		int32_t get_used();

		int32_t get(const rse::string& key);

		void put(const rse::string& key, int32_t value);

		std::function<void(const rse::string& key, int32_t value)> deleter;

	private:
		struct cache_item
		{
			cache_item(const rse::string& name, int32_t size)
				:file_name(name), file_size(size)
			{ }

			rse::string file_name;
			int32_t file_size;
		};
		typedef rse::list<cache_item>::iterator cache_item_iterator;

		rse::list<cache_item> lru_;
		rse::unordered_map<rse::string, cache_item_iterator> hash_;

		int32_t capacity_;
		int32_t used_;

		rse::string config_file_;
	};

}//namespace util

#endif//_LRU_CACHE_H_

