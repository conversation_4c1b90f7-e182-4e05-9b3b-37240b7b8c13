#ifndef _VR_EscaAccessOverVisitor_H_
#define _VR_EscaAccessOverVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Access_Cover.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaAccessOverVisitor : public BaseVisitor
	{
	public:
		VrEscaAccessOverVisitor();
		virtual ~VrEscaAccessOverVisitor();

		DEFINE_CREATE_FUN(VrEscaAccessOverVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<esca::Sel_Esca_Access_Cover> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif //endif
