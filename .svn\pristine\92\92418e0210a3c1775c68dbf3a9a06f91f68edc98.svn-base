#pragma once

#include "vr_controller.h"
#include "VrConfigInfo.h"

#include "Global_Select.h"
#include "Global_Setting_Car.h"
#include "Global_Setting_Hall.h"
#include "Global_Setting_Parameter.h"
#include "Global_Loading_Scence.h"
#include "Global_Other_Datas.h"
#if defined(ESCALATOR)
#include "array_visitor/esca/Global_Esca_Setting_Parameter.h"
#endif
#include "Global_Hwndmsg.h"

#include "file_pool.h"

namespace decoration_vr_interface
{
	enum 
	{
		DEFAULT_RUNNING_MODE = 0,
		VR_RUNNING_MODE = 1,
		AR_RUNNING_MODE = 2
	};

	struct MaterialInfo
	{
		MaterialInfo(int64 material_id, int edit_part_id, int part_id)
			:material_id_(material_id), part_id_(part_id), edit_part_id_(edit_part_id)
		{

		}

		int64 material_id_;
		int edit_part_id_;
		int part_id_;
	};

	class MaterialChannel;
	class VrGlobalInfo
	{
	public:

		static void Create();
		static void Destory();
		static VrGlobalInfo* Instance();
		void Initialize(IDGSceneEx* scene);
		void Finalize();

		void* get_main_hwnd() const { return main_hwnd_; }
		void set_main_hwnd(void* val) { main_hwnd_ = val; }
		void* get_parent_hwnd() const { return parent_hwnd_; }
		void set_parent_hwnd(void* val) { parent_hwnd_ = val; }
		IDGLibMgr* get_dg_lib_mgr() const { return dg_lib_mgr_; }
		void set_dg_lib_mgr(IDGLibMgr* val) { dg_lib_mgr_ = val; }
		IDGContext* get_dg_context() const { return dg_context_; }
		void set_dg_context(IDGContext* val) { dg_context_ = val; }
		IDGSceneEx* get_dg_scene() const { return dg_scene_; }
		void set_dg_scene(IDGSceneEx* val) { dg_scene_ = val; }
		IDGUserMsgReceiver* get_dg_user_msg_receiver() const { return dg_user_msg_receiver_; }
		void set_dg_user_msg_receiver(IDGUserMsgReceiver* val) { dg_user_msg_receiver_ = val; }
		VrController* get_vr_controller() const { return vr_controller_; }
		void set_vr_controller(VrController* val) { vr_controller_ = val; }

		VrConfigInfo* get_config_info() { return &config_info_; }

		Global_Select* get_select_array_visitor() { return select_.get(); }
		Global_Setting_Car* get_car_array_visitor() { return car_.get(); }
		Global_Setting_Hall* get_hall_array_visitor() { return hall_.get(); }
		Global_Setting_Parameter* get_setting_parameter_array_visitor() { return setting_params_.get(); }
		Global_Loading_Scence* get_global_loading_scene_array_visitor() { return global_scene_.get(); }
		Global_Other_Datas* get_global_other_datas_array_visitor() { return global_other_datas_.get(); }
#if defined(ESCALATOR)
		esca::Global_Esca_Setting_Parameter* get_esca_setting_parameter_array_visitor() { return esca_setting_params_.get(); }
#endif
		Global_Hwndmsg* get_hwnd_array_visitor() { return global_hwnd_array_.get(); }
	
		int WriteMaterialChannelInfo(decoration_vr_interface::MaterialChannel* channel, int type, const rse::vector<decoration_vr_interface::MaterialInfo>& list, bool is_reflect);
		int AppendMaterialChannelInfo(decoration_vr_interface::MaterialChannel* channel, int type, const rse::vector<decoration_vr_interface::MaterialInfo>& list, bool is_reflect);

		int GetDoorType();
        void UpdateDoorType(int door_type);
	public:
		int GetRunningMode() { return runing_mode_; }
		void SetRunningMode(int mode) { runing_mode_ = mode; }

		float GetDoorWidth() { return door_width_; }
		void SetDoorWidth(float w) { door_width_ = w; }

		float GetDoorHeight() { return door_height_; }
		void SetDoorHeight(float h) { door_height_ = h; }
	private:
		
		void ScriptMaskTexCountAndOffset(decoration_vr_interface::MaterialChannel* channel, int part_type, int row);
	private:
		int runing_mode_;

		float door_width_;
		float door_height_;
	protected:
		tstring PickColor(int clr_ref);
	protected:
		void* main_hwnd_;
		void* parent_hwnd_;
		IDGLibMgr* dg_lib_mgr_;
		IDGContext* dg_context_;
		IDGSceneEx* dg_scene_;
		IDGUserMsgReceiver* dg_user_msg_receiver_;
		VrController* vr_controller_;
		VrConfigInfo config_info_;

		std::shared_ptr<Global_Select> select_;
		std::shared_ptr<Global_Setting_Car> car_;
		std::shared_ptr<Global_Setting_Hall> hall_;
		std::shared_ptr<Global_Setting_Parameter> setting_params_;
		std::shared_ptr<Global_Hwndmsg> global_hwnd_array_;
		std::shared_ptr<Global_Loading_Scence> global_scene_;
		std::shared_ptr<Global_Other_Datas> global_other_datas_;
		
#if defined(ESCALATOR)
		std::shared_ptr<esca::Global_Esca_Setting_Parameter> esca_setting_params_;
#endif
	public:
		VrGlobalInfo();
		~VrGlobalInfo();
	};
}
