#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_TestNote : public DGBaseVisitor
		{
		public:
			Global_TestNote(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			int GetNumber(int row);
			void SetNumber(int row, int val);

			rse::string GetName(int row);
			void SetName(int row, const tchar* val);

		};
	}

}