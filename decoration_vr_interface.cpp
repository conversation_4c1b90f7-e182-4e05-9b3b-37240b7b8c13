﻿#include "stdafx.h"
#include "decoration_vr_interface.h"
#include "car_top.h"
#include "car_wall_part.h"
#include "electric_part.h"
#include "skirting_part.h"
#include "common_part.h"
//#include "esca/esca_elevator_part.h"
#include "VrGlobalInfo.h"
#include "ElevatorConfig.h"
//#include "database_visitor_mgr.h"
#include "ConfigAnalyzer.h"
#include "decoration_vr_array_visitor.h"
#include "db_visitor.h"
#include "material_channel_pool.h"
#include "download_assist.h"
#include "Util.h"
#include "parse_json/svr_json_helper.h"


namespace decoration_vr_interface
{
	DecorationVrInterface::DecorationVrInterface()
		: is_init_(false)
		, is_skip_do_change_(false)
		, is_auto_set_fcop_pos_(true)
		, is_remove_skirting_(false)
	{
		arg_cache_.cb_ = [this](int segment, int err_code, const char* err_msg)
		{
			this->SendErrorMessage(segment, err_code, err_msg);
		};

		GlobalInfoDataCommon::Create();
	}


	DecorationVrInterface::~DecorationVrInterface()
	{
		GlobalInfoDataCommon::Destory();
	}

	bool DecorationVrInterface::IsInitialize()
	{
		return is_init_;
	}

	void DecorationVrInterface::SendErrorMessage(int segment, int err_code, const char* err_msg)
	{
		auto cb = GetVrCallBack();
		if (cb != nullptr)
		{
			cb->LoadCompleteCallBack(err_code, segment, err_msg);
		}

		//SendVrMessage("msg_build_completed", 1);
	}

	void DecorationVrInterface::InitializeElevator(void* hwnd, int elev_size, int width, int depth, int door_width, int door_height, int flag)
	{
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeElevator);

		VrControllerInitialize(hwnd, flag);
		ElevatorSpecification elev_spec;
		elev_spec.elevator_size_id_ = elev_size;
        elev_spec.width_ = width;
        elev_spec.depth_ = depth;
		elev_spec.door_width_ = door_width;
		elev_spec.door_height_ = door_height;
		InitializeElevator(&elev_spec);

		is_init_ = true;
	}

	void DecorationVrInterface::InitializeElevator(void* hwnd, const ElevatorSpecification* elev_spec, int flag)
	{
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeElevator);

		//SetSceneFilePath("decoration_test0901.zxrm");

		VrControllerInitialize(hwnd, flag);
		InitializeElevator(elev_spec);
		is_init_ = true;
	}

	void DecorationVrInterface::InitializeElevator(void* hwnd, void* am, const ElevatorSpecification* elev_spec, int flag)
	{
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeElevator);

		VrControllerInitialize(hwnd, flag, am);
		InitializeElevator(elev_spec);

		is_init_ = true;
	}

	void DecorationVrInterface::InitializeElevator(void* hwnd, int flag)
	{
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeElevator);

		VrControllerInitialize(hwnd, flag);
		is_init_ = true;
	}

	void DecorationVrInterface::SetDir(const char* app_dir, const char* home_dir)
	{
		//SetResousePath(app_dir);
		fnGetLigMgr()->SetPlatformDir(app_dir, home_dir);
	}

	decoration_vr_interface::ElevatorSpecification DecorationVrInterface::GenerateDefaultSpecification(int width, int depth)
	{
		ElevatorSpecification elev_spec;
		elev_spec.width_ = width;
		elev_spec.height_ = kCarHeightDefaultValue;
		elev_spec.depth_ = depth;
		elev_spec.door_height_ = 0;
		elev_spec.door_width_ = kCarDoorWidthDefaultValue;
		return elev_spec;
	}

	void DecorationVrInterface::VrControllerInitialize(void* hwnd, int flag, void* am)
	{
		array_visitors_.clear();

		hwnd_ = hwnd;
		switch(flag)
		{
		case IT_NEED_CREATE:
		{
			vr_controller_.Initialize(am, hwnd_, hwnd_);
		}
		break;

		case IT_NEED_INIT:
		case IT_DIRECT_USE:
			vr_controller_.Initialize(hwnd_);
			break;
		}
	}

	void DecorationVrInterface::InitializeEscalator(void* hwnd, int width, int height, int flag)
	{
		//SetSceneFilePath("/Model/Hall/main_052_Demo_448.zxrm");
		
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeEscalator);

		VrControllerInitialize(hwnd, flag);

		InitializeEscalator(width, height);

		is_init_ = true;
	}

	void DecorationVrInterface::InitializeEscalator(int width, int depth)
	{
		ElevatorConfigManager* config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();

		auto config_list = config_mgr->get_elevator_config_list_ptr();

		if (config_list)
		{
			config_list->clear();
		}
		
		IElevatorConfigPtr elevator_config = RSE_MAKE_SHARED<ElevatorConfig>();
		if (width == 0 || depth == 0)
		{
			elevator_config->SetEscalatorSize(EscalatorSize::GetEscalatorSizeFromDb(3));
		}
		else
		{
			elevator_config->SetEscalatorSize(EscalatorSize::GetEscalatorSizeFromDb(width, depth));
		}

		ElevatorSetting setting;
		setting.current_serial_ = 1;
		setting.elevator_count_ = 1;

		elevator_config->SetEscalatorSetting(setting);

		config_list->push_back(elevator_config);

		config_mgr->SetCurrentWorkingElevatorConfig(0);
	}

	void DecorationVrInterface::InitializeElevator(const ElevatorSpecification* elev_spec)
	{
		arg_cache_.es_ = *elev_spec;

		ElevatorConfigManager* config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
		auto config_list = config_mgr->get_elevator_config_list_ptr();
		config_list->clear();
		IElevatorConfigPtr elevator_config = RSE_MAKE_SHARED<ElevatorConfig>();

		ElevatorSize elev_size;
			elev_size.id_ = elev_spec->elevator_size_id_;
		elev_size.width_ = elev_spec->width_;
		elev_size.depth_ = elev_spec->depth_;
			elev_size.height_ = elev_spec->height_;
			elev_size.door_height_ = elev_spec->door_height_;
			elev_size.door_width_ = elev_spec->door_width_;
			elevator_config->SetElevatorSize(elev_size);
		
		ElevatorSetting setting;
		setting.current_serial_ = elev_spec->current_serial_;
		setting.elevator_count_ = elev_spec->elevator_count_;
		setting.floor_count_ = elev_spec->default_floor_count_;
		setting.door_type_ = elev_spec->default_door_open_type_;
		setting.distance_to_next_ = 280;
		//2019/09/03修改
		//默认使用大厅1
		//规格为110*110的，使用大厅2
		//if (elev_spec->width_ == 110 && elev_spec->depth_ == 110)
		//{
		//	setting.current_serial_ = 0;
		//	setting.elevator_count_ = 1;
		//}

		elevator_config->SetElevatorSetting(setting);
		for(int i=0; i<setting.elevator_count_; ++i)
		{
			config_list->push_back(elevator_config);
		}
		config_mgr->SetCurrentWorkingElevatorConfig(setting.current_serial_);
	}

	void DecorationVrInterface::GoToCar()
    {
        vr_controller_.SendMessageToVr(TSTR("msg_enter360"), 0);
    }
    
    void DecorationVrInterface::GoToHall()
    {
        vr_controller_.SendMessageToVr(TSTR("msg_gotohall"), 0);
    }
    
	void DecorationVrInterface::Finalize()
	{
		is_init_ = false;

		array_visitors_.clear();
		vr_controller_.Finalize();

		GlobalInfoDataCommon::Instance()->Finalize();
		GlobalInfoDataCommon::Destory();
		//db_visitor::DBVisitorMgr::Instance()->Finalize();

		//VrGlobalInfo::Instance()->Finalize();
		//rs_delete(g_vrGlobalInfo);
		//g_vrGlobalInfo = nullptr;
	}

	bool DecorationVrInterface::InitEngine()
	{
		is_init_ = vr_controller_.InitEngine();
		return is_init_;
	}

	void DecorationVrInterface::SetResourcesPath(const char* path)
	{
		vr_controller_.SetResourcesPath(path);
	}

	bool DecorationVrInterface::CreateRender(void* hwnd, int w, int h, int device_type)
	{
		return vr_controller_.CreateRender(hwnd, w, h, device_type);
	}

	void DecorationVrInterface::LoadScene()
	{
		vr_controller_.InitVrController();
	}

	void DecorationVrInterface::InitElevator(const ElevatorSpecification* elev_spec)
	{
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeElevator);
		InitializeElevator(elev_spec);
	}

	void DecorationVrInterface::UnloadScene()
	{
		vr_controller_.FinalizeVrController();
	}

	void DecorationVrInterface::FinalizeEngine()
	{
		if (is_init_)
		{
			array_visitors_.clear();
			vr_controller_.FinalizeEngine();
			is_init_ = false;
		}
	}

	void DecorationVrInterface::ChangeElevatorSpecification(const ElevatorSpecification* elev_spec)
	{
		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeElevator);

		InitializeElevator(elev_spec);
	}

	void DecorationVrInterface::ChangeEscalatorSpecification(int size_id, int width, int height)
	{

		//SetSceneFilePath("/Model/Hall/main_052_Demo_448.zxrm");

		GlobalInfoDataCommon::Instance()->SetElevatorType(kConstTypeEscalator);
		LoadScene();

		InitializeEscalator(width, height);

		ElevatorConfigManager* config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();

		/*auto config_list = config_mgr->get_elevator_config_list_ptr();
		if(!config_list->empty())
		{
			IElevatorConfigPtr elevator_config = config_list->at(0);
		}
		else
		{
			
		}*/
				
		auto esca_size = config_mgr->GetCurrentElevatorConfig()->GetEscalatorSize();
		LOGI("GetEscalatorSize\n");
		esca_size->id_ = size_id;
	}

	void DecorationVrInterface::SetPart(int part_type, int64 part_id, bool notify)
	{
		if(part_type == PartTypeElevatorSize)
		{
			ElevatorConfigManager* config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
			auto config_list = config_mgr->get_elevator_config_list_ptr();
			config_list->clear();
			IElevatorConfigPtr elevator_config = RSE_MAKE_SHARED<ElevatorConfig>();
			elevator_config->SetElevatorSize(ElevatorSize::GetElevatorSizeFromDb(part_id));
			
			ElevatorSetting setting;
			setting.current_serial_ = 1;
			setting.elevator_count_ = 1;
			setting.floor_count_ = 15;
			setting.door_type_ = DOT_CENTER;
			setting.distance_to_next_ = 280;

			elevator_config->SetElevatorSetting(setting);
			config_list->push_back(elevator_config);
			config_list->push_back(elevator_config);
			config_list->push_back(elevator_config);
			config_list->push_back(elevator_config);

			config_mgr->SetCurrentWorkingElevatorConfig(1);
		}
		else
		{
			auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
			part_operator->SetPart(part_type, part_id);

			//ScriptChangeArg script_arg(part_type, false, part_id);
			//GlobalInfoDataCommon::Instance()->GetScriptExecutor()->ExecuteChangedScript(&script_arg);

			DoChange(part_type, notify);
		}
	}

	void DecorationVrInterface::SetMaterial(int part_type, int64 material_id, bool notify)
	{
		auto part = ConfigAnalyzer::GetPart(part_type);
        
        auto part_type_manager = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
        
        //if (part_type_manager->IsEscalatorType(part_type)) {
        //    auto escalator_part = static_cast<EscalatorPart*>(part);
        //    escalator_part->SetMaterialId(material_id);
        //}
        
		switch (part_type_manager->GetMaterialPartType(part_type))
		{
		case MPT_TOP:
			{
				auto top = static_cast<CarTop*>(part);
				top->SetEditPartMaterial(1, material_id);
				break;
			}
		case MPT_ELECTRIC:
		{
			auto material_part = static_cast<ElectricPart*>(part);
			material_part->SetPartMaterial(material_id);
		}
			break;
		case MPT_COMMON_PART:
		{
			auto material_part = static_cast<CommonPart*>(part);
			material_part->SetPartMaterial(material_id);
		}
			break;
		case MPT_WALL:
		{
			auto material_part = static_cast<CarWallPart*>(part);
			material_part->SetWallMaterial(material_id);
		}
			break;
		case MPT_HALL_ROOM:
			SendErrorMessage(UpdateVR, -1, "SetMaterial failed, hall room not support set material");
			GlobalInfoDataCommon::Instance()->ReportInfoToHost(
				"SetMaterial failed, hall room not support set material");
			GlobalInfoDataCommon::Instance()->LogErrorLn("hall not support SetMaterial");
			return;
		//case MPT_ESCALATOR:
		//{
		//	auto material_part = static_cast<EscalatorPart*>(part);
		//	material_part->SetMaterialId(material_id);
		//}
	/*		break;*/
        default:
            break;
		}

		//ScriptChangeArg script_arg(part_type, true, material_id);
		//GlobalInfoDataCommon::Instance()->GetScriptExecutor()->ExecuteChangedScript(&script_arg);
		auto db_part = DbVisitor::Instance()->LoadMaterial(material_id);

		svr_data::SvrMaterialInfo *material = static_cast<svr_data::SvrMaterialInfo*>(db_part.get());
		GlobalInfoDataCommon::Instance()->GetFilePool()->AddMaterialInfo(*material);
		GlobalInfoDataCommon::Instance()->GetMaterialChannelPool()->AddMaterialInfo(material->basic_info_.Id, *material);

		//auto notify_part_type = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type)->GetNotifyPartType(part_type);
		auto notify_part_type = part_type;
		DoChange(notify_part_type, notify);
	}
	//
	//void DecorationVrInterface::SetMaterialWithTexture(int part_type, int64 material_id, int channel_id, const char* tex_path, bool notify)
	//{
	//	auto part = ConfigAnalyzer::GetPart(part_type);
 //       
 //       auto part_type_manager = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
 //       
 //       if (part_type_manager->IsEscalatorType(part_type)) {
 //           auto escalator_part = static_cast<EscalatorPart*>(part);
 //           escalator_part->SetMaterialId(material_id);
 //       }
 //       
	//	switch (part_type_manager->GetMaterialPartType(part_type))
	//	{
	//	case MPT_ELECTRIC:
	//	{
	//		auto material_part = static_cast<ElectricPart*>(part);
	//		material_part->SetPartMaterial(material_id);
	//	}
	//		break;
	//	case MPT_COMMON_PART:
	//	{
	//		auto material_part = static_cast<CommonPart*>(part);
	//		material_part->SetPartMaterial(material_id);
	//	}
	//		break;
	//	case MPT_WALL:
	//	{
	//		auto material_part = static_cast<CarWallPart*>(part);
	//		material_part->SetWallMaterial(material_id);
	//	}
	//		break;
	//	//case MPT_ESCALATOR:
	//	//{
	//	//	auto material_part = static_cast<EscalatorPart*>(part);
	//	//	material_part->SetMaterialId(material_id);
	//	//}
	//	//	break;
 //       default:
 //           break;
	//	}

	//	//ScriptChangeArg script_arg(part_type, true, material_id);
	//	//GlobalInfoDataCommon::Instance()->GetScriptExecutor()->ExecuteChangedScript(&script_arg);

	//	auto new_tex_path = Util::StringToUTF8String(Util::StringToTString(tex_path).c_str());

	//	//更新数据库数据
	//	//db_visitor::DBVisitorMgr::Instance()->GetVisitor()->UpdateMaterialChannelTexture(material_id, channel_id, new_tex_path.c_str());

	//	auto notify_part_type = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type)->GetNotifyPartType(part_type);
	//	DoChange(notify_part_type, notify);
	//}

	int64 DecorationVrInterface::GetCurrentPartId(int part_type)
    {
		if (part_type == PartTypeCarConfig)
		{
			auto c = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentCarConfig();
			return c->GetPartId();
		}
		else if(part_type == PartTypeHallConfig)
		{
			auto c = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentHallConfig();
			return c->GetPartId();
		}
		else if(part_type == PartTypeElevatorSize)
		{
			return GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		}
		else
		{
			auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
			return part_operator->GetCurrentPartId(part_type);
		}
    }

	const char* DecorationVrInterface::GetCurrentPartName(int part_type)
	{
		if (part_type == PartTypeCarConfig)
		{
			auto c = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentCarConfig();
			return c->GetPartName().c_str();
		}
		else if (part_type == PartTypeHallConfig)
		{
			auto c = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentHallConfig();
			return c->GetPartName().c_str();
		}
		else
		{
			auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
			return part_operator->GetCurrentPartName(part_type);
		}
	}

    int64 DecorationVrInterface::GetCurrentMaterialId(int part_type)
    {
		auto part = ConfigAnalyzer::GetPart(part_type);
        switch (GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type))
		{
		case MPT_ELECTRIC:
		{
			auto material_part = static_cast<ElectricPart*>(part);
			if (material_part != nullptr)
			{
				return material_part->GetPartMaterial();
			}
		}
		break;
		case MPT_COMMON_PART:
		{
			auto material_part = static_cast<CommonPart*>(part);
			if (material_part != nullptr)
			{
				return material_part->GetPartMaterial();
			}
		}
		break;
		case MPT_WALL:
		{
			auto material_part = static_cast<CarWallPart*>(part);
			if (material_part != nullptr)
			{
				return material_part->GetWallMaterial();
			}
		}
		break;
		case MPT_TOP:
		{
			auto top = static_cast<CarTop*>(part);
			if (top != nullptr)
			{
				return top->GetEditPartMaterial(1);
			}
		}
		case MPT_HALL_ROOM:
			SendErrorMessage(UpdateVR, -1, "GetCurrentMaterialId failed, hall room not support get material");
			GlobalInfoDataCommon::Instance()->ReportInfoToHost(
				"GetCurrentMaterialId failed, hall room not support get material");
			GlobalInfoDataCommon::Instance()->LogErrorLn("hall not support GetCurrentMaterialId");
			return -1;
		default:
			break;
        }
        
        return -1;
    }
    
	int DecorationVrInterface::GetHandrailPos()
	{
		IConfigPtr car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
		if (car_config)
		{
			auto vari = car_config->GetExtendProperty(EPK_HandrailSetupPos);
			if (vari != nullptr)
			{
				return (int)(*vari);
			}
		}
		
		return 0;		
	}

	int DecorationVrInterface::GetCopButtonCount()
	{
		ElevatorConfigManager* config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
		if (config_mgr)
		{
			auto config = config_mgr->GetCurrentElevatorConfig();
			if (config && config->GetElevatorSetting())
			{
				return config->GetElevatorSetting()->floor_count_;
			}
		}

		return -1;
	}

	int64 DecorationVrInterface::GetSubitemMaterial(int part_type, int part_mark)
	{
		int64 res = -1;
		switch (GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type))
		{
		case MPT_WALL:
		{
			if (part_mark == WALLELEM_MARK_SKIRTING)
			{
				auto skirt = static_cast<SkirtingPart*>(ConfigAnalyzer::GetPart(PartTypeSkirting));
				if (skirt)
				{
					res = skirt->GetSkirtingMaterial(part_type);
				}
			}
			else
			{
				auto part = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(part_type));
				if (part)
				{
					auto all_elems = part->GetWallElems();

					auto find_it = std::find_if(all_elems.begin(), all_elems.end(), [&part_mark](CarWallElemPtr elem)
					{
						return part_mark == elem->marker_;
					});

					if (find_it != all_elems.end())
					{
						res = (*find_it)->material_id_;
					}
				}
			}
		}
		break;
		case MPT_TOP:
		{
			auto part = static_cast<CarTop*>(ConfigAnalyzer::GetPart(part_type));
			if (part)
			{
				res = part->GetEditPartMaterial(part_mark);
			}
		}
		break;

		case MPT_COMMON_PART:
		{
			auto part = static_cast<CommonPart*>(ConfigAnalyzer::GetPart(part_type));
			if (part)
			{
				res = part->GetEditPartMaterial(part_mark);
			}
		}
		break;
		case MPT_ELECTRIC:
		{
			auto part = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
			if (part)
			{
				res = part->GetEditPartMaterial(part_mark);
			}
		}
		break;
		case MPT_HALL_ROOM:
			SendErrorMessage(UpdateVR, -1, "GetSubitemMaterial failed, hall room not support get material");
			GlobalInfoDataCommon::Instance()->ReportInfoToHost(
				"GetSubitemMaterial failed, hall room not support get material");
			GlobalInfoDataCommon::Instance()->LogErrorLn("hall not support GetSubitemMaterial");
			return res;
		default:
		{
			GlobalInfoDataCommon::Instance()->LogErrorLn("GetSubitemMaterial,unknown part type:%d", part_type);
		}
		break;
		}

		return res;
	}

	int DecorationVrInterface::GetElectricPartOrientation(int part_type)
	{
		auto tid = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type);
		if (tid == MPT_ELECTRIC)
		{
			auto part = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
			if (part)
			{
				return part->GetSetupOrientation();
			}
		}

		return -1;
	}

	float DecorationVrInterface::GetElectricPartPosX(int part_type)
	{
		auto tid = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type);
		if (tid == MPT_ELECTRIC)
		{
			auto part = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
			if (part)
			{
				return part->GetPosX();
			}
		}

		return -1;
	}

	float DecorationVrInterface::GetElectricPartPosY(int part_type)
	{
		auto tid = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type);
		if (tid == MPT_ELECTRIC)
		{
			auto part = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
			if (part)
			{
				return part->GetPosY();
			}
		}

		return -1;
	}

	int DecorationVrInterface::GetWallElemCount(int part_type)
	{
		auto part = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(part_type));
		if (part)
		{
			auto all_elems = part->GetWallElems();
			auto find_it = std::find_if(all_elems.begin(), all_elems.end(), [](CarWallElemPtr elem)
			{
				return WALLELEM_MARK_SKIRTING == elem->marker_;
			});

			auto elem_count = all_elems.size();
			if (find_it != all_elems.end()) --elem_count;

			return elem_count;
		}
        assert(false);
        return -1;
	}

	int DecorationVrInterface::GetElevatorSizeId()
	{
		auto config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
		auto elev_size = config_mgr->GetCurrentElevatorSize();
		return elev_size->id_;
	}

	ElevatorSpecification* DecorationVrInterface::GetCurrentElevatorSpecification()
	{
		return &arg_cache_.es_;
	}

	int DecorationVrInterface::GetCarHeight()
	{
		auto elev_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig();
		auto elev_size = elev_config->GetElevatorSize();
		if (elev_size->height_ > 0) return elev_size->height_;

		auto car_config = elev_config->GetCarConfig();
		auto top = static_cast<CarTop*>(car_config->GetPart(PartTypeTop));
		return top->GetHeight();
	}

	void DecorationVrInterface::Resize(float width, float height)
	{
		vr_controller_.NotifyChangeCameraRatio(width, height);
	}

	void DecorationVrInterface::NotifyVr()
	{
		vr_controller_.DoChanges();
	}

	void DecorationVrInterface::SetRemoveSkirting(bool val)
	{
		is_remove_skirting_ = val;
	}

	bool DecorationVrInterface::IsRemoveSkirting()
	{
		return is_remove_skirting_;
	}

	void DecorationVrInterface::SetMirrorSetupInfo(int orient, float x, float y, bool notify)
	{
		auto material = ConfigAnalyzer::GetConfig(PartTypeMirror);
		material->SetExtendProperty(EPK_MirrorSetupPos, RSE_MAKE_SHARED<Variant>(orient));

		material->SetExtendProperty(EPK_MirrorPosX, RSE_MAKE_SHARED<Variant>(x));
		material->SetExtendProperty(EPK_MirrorPosY, RSE_MAKE_SHARED<Variant>(y));

		auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(PartTypeMirror);
		//DoChange(part_operator->GetNotifyPartType(PartTypeMirror), notify);
		DoChange(PartTypeMirror, notify);
	}

	void DecorationVrInterface::ClearOptionalPart(int type, bool notify)
	{
		auto material = ConfigAnalyzer::GetConfig(type);

		switch (type)
		{
		case PartTypeAuxCop:
			SetIsHasAuxCop(false, false);
			break;
		case PartTypeHDCop:
			SetIsHasHdCop(false, false);
			break;
		case PartTypeMirror:
			material->SetExtendProperty(EPK_MirrorSetupPos, RSE_MAKE_SHARED<Variant>(0));
			break;
		case PartTypeHandrail:
			material->SetExtendProperty(EPK_HandrailSetupPos, RSE_MAKE_SHARED<Variant>(kHandrailPosNone));
			break;
		default:
			break;
		}
		auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(type);
		//DoChange(part_operator->GetNotifyPartType(type), notify);
		DoChange(type, notify);

		material->RemovePart(type);
	}

	void DecorationVrInterface::DoVrProcess()
	{
		VrGlobalInfo::Instance()->get_vr_controller()->DoVrProcess();
	}
    
    int DecorationVrInterface::RenderFrameC()
    {
        return VrGlobalInfo::Instance()->get_vr_controller()->RenderFrameC();
    }
    
    bool DecorationVrInterface::InitializeForRenderFrameC()
    {
		return vr_controller_.InitializeForRenderFrameC();
    }
    
    void DecorationVrInterface::SendVrMessage(const char* msg, int delay_frame)
    {
        vr_controller_.SendMessageToVr(Util::StringToTString(msg).c_str(), delay_frame);
    }

	void DecorationVrInterface::SendVrMessage(const char* msg, MsgParaValue *arr, int arr_length, int delay_frame)
	{
		vr_controller_.SendMessageToVr(Util::StringToTString(msg).c_str(), arr, arr_length, delay_frame);
	}

	void DecorationVrInterface::SetHandrailPos(int pos, bool notify)
	{
		IConfigPtr car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
		auto handrail_setuppos = RSE_MAKE_SHARED<Variant>();
		handrail_setuppos->i_ = pos;
		car_config->SetExtendProperty(EPK_HandrailSetupPos, handrail_setuppos);

		DoChange(PartTypeHandrail, notify);
	}

	void DecorationVrInterface::SetIsHasAuxCop(bool has, bool notify)
	{
		IConfigPtr car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
		auto ishave_auxcop_property = RSE_MAKE_SHARED<Variant>();
		ishave_auxcop_property->b_ = has;
		car_config->SetExtendProperty(EPK_IsHaveAuxCop, ishave_auxcop_property);

		DoChange(PartTypeCop, notify);
	}

	void DecorationVrInterface::SetIsHasHdCop(bool has, bool notify) 
	{
		IConfigPtr car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
		auto ishave_hdcop_property = RSE_MAKE_SHARED<Variant>();
		ishave_hdcop_property->b_ = has;
		car_config->SetExtendProperty(EPK_IsHaveHdCop, ishave_hdcop_property);

		DoChange(PartTypeCop, notify);
	}
	void DecorationVrInterface::SetIsHasAuxHdCop(bool has, bool notify) 
	{
		IConfigPtr car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
		auto ishave_hdcop_property = RSE_MAKE_SHARED<Variant>();
		ishave_hdcop_property->b_ = has;
		car_config->SetExtendProperty(EPK_IsHaveAuxHdCop, ishave_hdcop_property);

		DoChange(PartTypeCop, notify);
	}

	void DecorationVrInterface::SetCopButtonCount(int btn_count, bool notify)
	{
		ElevatorConfigManager* config_mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
		auto config_list = config_mgr->get_elevator_config_list_ptr();
		std::for_each(config_list->begin(), config_list->end(), [btn_count](IElevatorConfigPtr config_ptr)
		{
			config_ptr->GetElevatorSetting()->floor_count_ = btn_count;
		});

		DoChange(PartTypeCop, notify);
	}

	bool DecorationVrInterface::SetSubitemMaterial(int part_type, int part_mark, int64 material_id, bool notify)
	{
		auto res = false;
		switch (GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type))
		{
		case MPT_WALL:
			{
				if (part_mark == WALLELEM_MARK_SKIRTING)
				{
					auto skirt = static_cast<SkirtingPart*>(ConfigAnalyzer::GetPart(PartTypeSkirting));
					skirt->SetSkirtingMaterial(part_type, material_id);
					res = true;
				}
				else
				{
					auto part = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(part_type));
					auto all_elems = part->GetWallElems();

					auto find_it = std::find_if(all_elems.begin(), all_elems.end(), [&part_mark](CarWallElemPtr elem)
					{
						return part_mark == elem->marker_;
					});
					
					if (find_it != all_elems.end())
					{
						(*find_it)->material_id_ = material_id;
						res = true;
					}
				}
			}
			break;
		case MPT_TOP:
			{
				auto part = static_cast<CarTop*>(ConfigAnalyzer::GetPart(part_type));
				res = part->SetEditPartMaterial(part_mark, material_id);
			}
			break;

		case MPT_COMMON_PART:
			{
				auto part = static_cast<CommonPart*>(ConfigAnalyzer::GetPart(part_type));
				part->SetEditPartMaterial(part_mark, material_id);
			}
			break;
		case MPT_HALL_ROOM:
			SendErrorMessage(UpdateVR, -1, "SetSubitemMaterial failed, hall room not support set material");
			GlobalInfoDataCommon::Instance()->ReportInfoToHost(
				"SetSubitemMaterial failed, hall room not support set material");
			res = false;
			break;
		}

		if (res)
		{
			DoChange(part_type, notify);
		}

		return res;
	}

	void DecorationVrInterface::SetElectricPartOrientation(int part_type, int orient, bool notify)
	{
		auto tid = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type);
		if (tid != MPT_ELECTRIC) return;

		auto part = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
		if (part)
		{
			part->SetSetupOrientation(orient);
			auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
			//DoChange(part_operator->GetNotifyPartType(part_type), notify);
			DoChange(part_type, notify);
		}
	}

	void DecorationVrInterface::SetElectricPartPos(int part_type, float x, float y, bool notify)
	{
		auto tid = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetMaterialPartType(part_type);
		if (tid != MPT_ELECTRIC) return;

		auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);		
		auto part = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
		if (part)
		{	
			part->SetPosX(x);
			part->SetPosY(y);

			auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
			//DoChange(part_operator->GetNotifyPartType(part_type), notify);
			DoChange(part_type, notify);
		}
	}

	void DecorationVrInterface::SetDoorOpenType(int door_type, int elevator_id, bool notify)
	{
		auto mgr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager();
		auto plist = mgr->get_elevator_config_list_ptr();
		if (plist->size() > 1)
		{
			ElevatorSetting *setting = nullptr;
			if (elevator_id < 0)
			{
				setting = mgr->GetCurrentElevatorConfig()->GetElevatorSetting();
			}
			else if (elevator_id < plist->size())
			{
				setting = plist->at(elevator_id)->GetElevatorSetting();
			}

			if (setting)
			{
				if (setting->door_type_ != door_type)
				{
					setting->door_type_ = door_type;
					DoChange(PartTypeHallConfig, false);
					DoChange(PartTypeCarConfig, notify);
				}
			}
		}
	}

	//bool DecorationVrInterface::UpdateConstructByElemCount(int part_type, int unit_count)
	//{
	//	auto part = static_cast<CarWallPart*>(ConfigAnalyzer::GetPart(part_type));
	//	if(part != nullptr)
	//	{
	//		return part->UpdateConstructByElemCount(unit_count);
	//	}
	//	return false;
	//}

	void DecorationVrInterface::SetHandrailPosMark(int ori, int mark)
	{
		auto material = ConfigAnalyzer::GetConfig(PartTypeHandrail);
		switch (ori)
		{
		case PartTypeLeftWall:
			material->SetExtendProperty(EPK_HandrailLeftPosMark, RSE_MAKE_SHARED<Variant>(mark));
			break;
		case PartTypeRightWall:
			material->SetExtendProperty(EPK_HandrailRightPosMark, RSE_MAKE_SHARED<Variant>(mark));
			break;
		case PartTypeBackWall:
			material->SetExtendProperty(EPK_HandrailBackPosMark, RSE_MAKE_SHARED<Variant>(mark));
			break;
		default:
			break;
		}
	}

	void DecorationVrInterface::SetSceneFilePath(const char* scene_path)
	{
		auto rp = Util::StringToTString(scene_path);
		while (rp.front() != '\\' && rp.front() != '/')
		{
			rp.insert(rp.begin(), '/');
		}
		VrGlobalInfo::Instance()->get_config_info()->sceneFilePath = rp;
		LOGI("sceneFilePath = %s\n", rp.c_str());
	}

	//void DecorationVrInterface::SetResousePath(const char* res_path)
	//{
	//	auto rp = Util::StringToTString(res_path);
	//	while (rp.back() == '\\' || rp.back() == '/')
	//	{
	//		rp = rp.substr(0, rp.length() - 1);
	//	}

	//	VrGlobalInfo::Instance()->get_config_info()->resouseFilePath = rp;
	//}

	//void DecorationVrInterface::SetDataBasePath(const char* db_path)
	//{
	//	db_visitor::DBVisitorMgr::Instance()->SetDbPath(Util::StringToUTF8String(db_path));
	//	GlobalInfoDataCommon::Instance()->Initialize();
	//}

	void DecorationVrInterface::SetDecorationVrCallBack(IDecorationVrCallBack* callback)
	{
		vr_controller_.SetVrCallBack(callback);
	}

	void DecorationVrInterface::GestureDetector(int gesture_type, int gesture_state, float x, float y)
	{
#if defined(RENDER_SKETCHER_ANDROID)
		VrGlobalInfo::Instance()->get_dg_lib_mgr()->GetContext()->gesture(gesture_type, gesture_state, x, y);
#endif
	}

	void DecorationVrInterface::OnTouch(int finger_id, int x, int y)
	{
#if defined(RENDER_SKETCHER_ANDROID)
		auto dg_lib = VrGlobalInfo::Instance()->get_dg_lib_mgr();
		if (dg_lib == NULL)
		{
			LOGI("dg_lib is NULL");
			return;
		}
		auto context = dg_lib->GetContext();
		if (context == NULL)
		{
			LOGI("context is NULL");
			return;
		}
		VrGlobalInfo::Instance()->get_dg_lib_mgr()->GetContext()->onTouch(finger_id, x, y);
#endif
	}

	bool DecorationVrInterface::DoChange(int part_type, bool need_notify)
	{
		auto gd = GlobalInfoDataCommon::Instance();
		gd->ReportInfoToHost( "PartType=%d, notify=%d, Begin do vr change...", part_type, need_notify ? 1 : 0);
		if (!is_skip_do_change_)
		{
			auto part = ConfigAnalyzer::GetConfig(part_type);
			VrChangeArg arg;
			arg.config_ = part.get();
			arg.part_type_ = (PartTypeId)part_type;
			arg.need_notify_ = need_notify;
			vr_controller_.AddChange(arg);
			auto res = vr_controller_.DoChanges();
			switch (res)
			{
			case 0:
				gd->ReportInfoToHost( "PartType=%d, do vr change ok", part_type);
				break;
			case 1:
				SendErrorMessage(UpdateVR, -1, "vr is not loaded");
				gd->ReportInfoToHost( "PartType=%d, do vr change failed:vr is not loaded", part_type);
				break;
			case 2:
				SendErrorMessage(UpdateVR, -1, "update vr failed");
				gd->ReportInfoToHost( "PartType=%d, do vr change failed:vr is not loaded", part_type);
				break;
			case 3:
			{
				auto s = GlobalInfoDataCommon::Instance()->GetFilePool()->GetErrorStr();
				SendErrorMessage(UpdateVR, -1, s.c_str());
				gd->ReportInfoToHost( "PartType=%d, do vr change failed:%s", part_type, s.c_str());
			}
			break;
			case 4:
				SendErrorMessage(UpdateVR, -1, "download file failed");
				gd->ReportInfoToHost( "PartType=%d, do vr change failed:download file failed", part_type);
				break;
			default:
				break;
			}
			return res == 0;
		}

		return false;
	}

	//void DecorationVrInterface::SetSkipDoChange(bool val)
	//{
	//	is_skip_do_change_ = val;
	//}

	//bool DecorationVrInterface::IsSkipDoChange()
	//{
	//	return is_skip_do_change_;
	//}

	void DecorationVrInterface::AutoSetFCopSetupPos(bool val)
	{
		is_auto_set_fcop_pos_ = val;
	}

	bool DecorationVrInterface::IsAutoSetFCopSetupPos()
	{
		return is_auto_set_fcop_pos_;
	}

	/*int DecorationVrInterface::GetStereoMode()
	{
		return vr_controller_.GetStereoMode();
	}
	void DecorationVrInterface::SetStereoMode(int val)
	{
		vr_controller_.SetStereoMode((StereoMode)val);
	}

	float DecorationVrInterface::GetEyeDistance()
	{
		return vr_controller_.GetEyeDistance();
	}
	void DecorationVrInterface::SetEyeDistance(float val)
	{
		vr_controller_.SetEyeDistance(val);
	}

	float DecorationVrInterface::GetFocusLength()
	{
		return vr_controller_.GetFocusLength();
	}
	void DecorationVrInterface::SetFocusLength(float val)
	{
		vr_controller_.SetFocusLength(val);
	}

	void DecorationVrInterface::SetDefaultCameraAspectRatio(float ratio)
	{
		vr_controller_.SetDefaultCameraAspectRatio(ratio);
	}

	void DecorationVrInterface::ChangeCameraFace(int pos)
	{
		vr_controller_.ChangeCameraFace((CameraPos)pos);
	}

	bool DecorationVrInterface::CreateSlaveRenderWindow(void* parentWnd)
	{
		return vr_controller_.CreateSlaveRenderWindow(parentWnd);
	}

	bool DecorationVrInterface::CreateSlaveRenderWindow()
	{
		return VrGlobalInfo::Instance()->get_dg_context()->CreateSlaveRenderWindow();
	}

	bool DecorationVrInterface::DestroySlaveRenderWindow()
	{
		return VrGlobalInfo::Instance()->get_dg_context()->DestroySlaveRenderWindow();
	}*/

	void DecorationVrInterface::SetSnapPos(int pos)
	{
		auto a = VrGlobalInfo::Instance()->get_hwnd_array_visitor();
		a->Setmsg(0, 10);
		a->Setcampos(0, pos);
	}

	void DecorationVrInterface::ResizeRender(int w, int h)
	{
		vr_controller_.ResizeRender(w, h);
	}
    //
    //bool DecorationVrInterface::EnterMojing()
    //{
    //    return VrGlobalInfo::Instance()->get_dg_context()->EnterMojing();
    //}
    //
    //void DecorationVrInterface::ExitMojing()
    //{
    //    VrGlobalInfo::Instance()->get_dg_context()->ExitMojing();
    //}

	void DecorationVrInterface::RenderLargeImage(const char*  path, float width, float height)
	{
		//vr_controller_.RenderLargeImage(path, width, height);
		auto a = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		a->SettexturePath(0, path);
		int w = width;
		a->SetrecordFramerate(0, w);
		a->SetrecodCamPosition(0, w);

		SendVrMessage("msg_largepic", 0);
	}

	void DecorationVrInterface::RenderSmallImage(const char* path, float width, float height)
	{
		auto a = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		a->SettexturePath(0, path);
		int w = width;
		a->SetrecordFramerate(0, w);
		a->SetrecodCamPosition(0, height);

		SendVrMessage("msg_smallpic", 0);
	}

	void DecorationVrInterface::RenderPanoramaImages(const char*  path)
	{
		//vr_controller_.RenderLargeImage(path, width, height);
		auto a = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		a->SetPanronamaPath(0, path);

		SendVrMessage("msg_gen_panorama", 0);
	}

	void DecorationVrInterface::RenderPanoramaImages(const char* path, bool is_plat/* = false*/)
	{
		//vr_controller_.RenderLargeImage(path, width, height);
		auto a = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		a->SetPanronamaPath(0, path);

		MsgParaValue msg_val("is_plat", MPVT_INT32, is_plat ? 1 : 0);
		SendVrMessage("msg_gen_panorama", &msg_val, 1, 0);
	}

	void DecorationVrInterface::RenderLargeImageWithFixedViewPos(int width, int height, int fixed_view_pos)
	{
		auto a = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		char split = '/';
#ifdef RENDER_SKETCHER_WINDOWS
		split = '\\';
#endif
		auto path = VrGlobalInfo::Instance()->get_config_info()->resouseFilePath + split + "large_pic.jpg";
		a->SettexturePath(0, path.c_str());

		a->SetrecordFramerate(0, width);
		a->SetrecodCamPosition(0, height);

		//SnapAngle
		MsgParaValue msg_val("SnapAngle", MPVT_INT32, fixed_view_pos);
		SendVrMessage("msg_fix_of_view_snap", &msg_val, 1, 0);
	}

	void DecorationVrInterface::Print(const char* path)
	{		
		vr_controller_.PrintDataArrayXml(Util::StringToTString(path));
	}

	//bool DecorationVrInterface::IsExistedOfPartId(int part_type, int64 part_id)
	//{
	//	if(part_id <= 0) return false;
	//	
	//	auto dbvisitor = db_visitor::DBVisitorMgr::Instance()->GetVisitor();

	//	auto part_type_mgr = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
	//	auto material_type = part_type_mgr->GetMaterialPartType(part_type);
	//	switch (material_type)
	//	{
	//		case MPT_COMMON_PART:
	//			{
	//				auto part = dbvisitor->LoadCommonPart(part_type, part_id);
	//				return part.IsValid() && !part.IsEof();
	//			}
	//			break;
	//		case MPT_ELECTRIC:
	//			{
	//				auto part = dbvisitor->LoadElectricPart(part_type, part_id);
	//				return part.IsValid() && !part.IsEof();
	//			}
	//			break;
	//		case MPT_TOP:
	//			{
	//				int size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
	//				auto part = dbvisitor->LoadCarTopModel(part_id, size_id);
	//				return part.IsValid() && !part.IsEof();
	//			}
	//			break;
	//		//case MPT_ESCALATOR:
	//		//	{
	//		//		auto part = dbvisitor->LoadEscaPart(part_type, part_id);
	//		//		return part.IsValid() && !part.IsEof();
	//		//	}
	//		//	break;
	//		default:				
	//			break;
	//	}
	//	
	//	switch(part_type)
	//	{
	//	//case PartTypeMaterial:
	//	//	{
	//	//		auto part = dbvisitor->LoadEscaMaterialBasicInfo(part_id);
	//	//		return part.IsValid() && !part.IsEof();
	//	//	}
	//	//	break;
	//	case PartTypeHallConfig:
	//	case PartTypeCarConfig:
	//	{
	//		auto part = dbvisitor->LoadConfigList(part_id);
	//		return part.IsValid() && !part.IsEof();
	//	}
	//	break;
	//	default:
	//		break;
	//	}

	//	return false;
	//}

	decoration_vr_interface::IDecorationVrArrayVisitor* DecorationVrInterface::GetSceneArrayVisitor(const char* name)
	{
		if (!IsInitialize())
		{
			return nullptr;
		}

		auto it = array_visitors_.begin(), end = array_visitors_.end();
		while (it != end)
		{
			if (strcmp((*it)->GetName(),name) == 0)
			{
				return it->get();
			}
			++it;
		}

		IDGArrayVisitor* v = VrGlobalInfo::Instance()->get_dg_scene()->GetSceneDataReceiver(name);
		if (v)
		{
			auto visitor = RSE_MAKE_SHARED<DecorationVrArrayVisitor>(v, name);
			array_visitors_.push_back(visitor);
			return visitor.get();
		}
		return nullptr;
	}

	void DecorationVrInterface::ReleaseBuffer(const void* buf)
	{
		fnGetLigMgr()->ReleaseLibBuf(buf);
	}

	void DecorationVrInterface::SetDownloadServerURL(const char *url, const char *down_file_root)
	{
		GlobalInfoDataCommon::Instance()->SetWebServiceSite(Util::StringToTString(url).c_str(), Util::StringToTString(down_file_root).c_str());
	}
	void DecorationVrInterface::SetLoginInfo(int64 nLibraryId, int nLangId, int64 nUserId)
	{
		GlobalInfoDataCommon::Instance()->SetLoginInfo(nLibraryId, nLangId, nUserId);
	}

	void DecorationVrInterface::SetContentString(const char* content)
	{
		//GlobalInfoDataCommon::Instance()->LogErrorLn("%s", content);
		arg_cache_.SetPart(content);
	}

	void DecorationVrInterface::SetIsStandalongVersion(bool val)
	{
		GlobalInfoDataCommon::Instance()->SetIsStandalongVersion(val);
	}

	IDecorationVrCallBack * DecorationVrInterface::GetVrCallBack()
	{
		return vr_controller_.GetVrCallBack();
	}

	IDGContext * DecorationVrInterface::GetDGContext()
	{
		return vr_controller_.get_dg_context();
	}

	void DecorationVrInterface::SetPartByGoods(int part_type, int64 goods_id, bool notify)
	{
		auto part_operator = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type);
		if (!part_operator)
		{
			SendErrorMessage(InitPart, -1, "get part operator is null");
			return;
		}

		if (!part_operator->SetPartByGoods(part_type, goods_id))
		{
			SendErrorMessage(CreateVRPart, -1, "analyze json or create vr part failed");
			return;
		}
		
		if (!DoChange(part_type, notify))
		{
			return;
		}
	}

	void DecorationVrInterface::SetMaterialByGoods(int part_type, int64 goods_id, bool notify)
	{
		auto part = ConfigAnalyzer::GetPart(part_type);
		if (!part)
		{
			SendErrorMessage(InitPart, -1, "get part is null");
			return;
		}

		std::shared_ptr<svr_data::SvrMaterialInfo> svr_material;
		//2024-11-28 新增if判断，用于驱动地板
		if (part_type == PartTypeBottom)
		{
			auto es = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize();
			svr_material = std::static_pointer_cast<svr_data::SvrMaterialInfo>(DbVisitor::Instance()->LoadBootomMaterialByGoodsId(goods_id, (int)es->width_, (int)es->depth_, (int)es->door_width_, false));
		}
		else
		{
			svr_material = std::static_pointer_cast<svr_data::SvrMaterialInfo>(DbVisitor::Instance()->LoadMaterialByGoodsId(goods_id));
		}
		auto material_id = svr_material->basic_info_.Id;

		auto part_type_manager = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
		switch (part_type_manager->GetMaterialPartType(part_type))
		{
		case MPT_TOP:
		{
			auto top = static_cast<CarTop*>(part);
			top->SetEditPartMaterial(1, material_id);
			break;
		}
		case MPT_ELECTRIC:
		{
			auto material_part = static_cast<ElectricPart*>(part);
			material_part->SetPartMaterial(material_id);
		}
		break;
		case MPT_COMMON_PART:
		{
			auto material_part = static_cast<CommonPart*>(part);
			material_part->SetPartMaterial(material_id);
		}
		break;
		case MPT_WALL:
		{
			auto material_part = static_cast<CarWallPart*>(part);
			material_part->SetWallMaterial(material_id);
		}
		break;
		case MPT_HALL_ROOM:
			SendErrorMessage(UpdateVR, -1, "SetMaterialByGoods failed, hall room not support set material");
			GlobalInfoDataCommon::Instance()->ReportInfoToHost(
				"SetMaterialByGoods failed, hall room not support set material");
			return;
		default:
			break;
		}

		GlobalInfoDataCommon::Instance()->GetFilePool()->AddMaterialInfo(*svr_material);
		GlobalInfoDataCommon::Instance()->GetMaterialChannelPool()->AddMaterialInfo(material_id, *svr_material);

		//auto notify_part_type = GlobalInfoDataCommon::Instance()->GetPartOperatorManager()->GetPartOperator(part_type)->GetNotifyPartType(part_type);
		auto notify_part_type = part_type;
		if (!DoChange(notify_part_type, notify))
		{
			return;
		}
	}

	void DecorationVrInterface::OnExternalDeviceLost()
	{
		vr_controller_.OnExternalDeviceLost();
	}

	void DecorationVrInterface::OnExternalDeviceReCreate(void* hwnd, int w, int h)
	{
		vr_controller_.OnExternalDeviceReCreate(hwnd, w, h);
	}

	void DecorationVrInterface::SetPlatformOperation(void* operation)
	{
		vr_controller_.SetPlatformOperation(operation);
	}

	void DecorationVrInterface::OnPlatformAsyncCallBack(int function_code, int wparam, int lparam, const char *extra_data)
	{
		vr_controller_.OnPlatformAsyncCallBack(function_code, wparam, lparam, extra_data);
	}

    void* DecorationVrInterface::GetPluginInterfaceObject(const char *guid_text)
    {
        return vr_controller_.GetPluginInterfaceObject(guid_text);
    }

	void DecorationVrInterface::WaitRenderThreadQuited()
	{
		vr_controller_.WaitRenderThreadQuited();
	}

	void DecorationVrInterface::SignalRenderThreadQuited()
	{
		vr_controller_.SignalRenderThreadQuited();
	}

	int DecorationVrInterface::GetCopPanelType(int part_type)
	{
		auto cop = static_cast<ElectricPart*>(ConfigAnalyzer::GetPart(part_type));
		if (cop)
		{
			return cop->GetPanelType();
		}

		return 200;
	}

	/*void DecorationVrInterface::ExitRenderThread()
	{
		vr_controller_.ExitRenderThread();
	}*/

	bool DecorationVrInterface::SendVrMessageWithParas(const char *msg, const char *param_json_text, int json_text_len, int delay_frame)
	{
		Json::CharReaderBuilder reader_builder;
		reader_builder["collectComments"] = false;
		auto reader = reader_builder.newCharReader();

		Json::Value json_arr;
		JSONCPP_STRING err;
		auto ret = reader->parse(param_json_text, param_json_text + json_text_len, &json_arr, &err);
		rs_delete(reader);
		if (ret)
		{
			if (json_arr.isArray())
			{
				rse::vector<rse::string> str_value_cache;
				rse::vector<MsgParaValue> paras;

				auto n = json_arr.size();
				paras.resize(n);
				str_value_cache.resize(n * 2);

				int str_index = 0;
				for (int i = 0; i < n; ++i)
				{
					auto &json_para = json_arr[i];

					//name
					svr_data::GetJsonValue(json_para["ParaName"], str_value_cache[str_index]);
					paras[i].name_ = const_cast<char*>(str_value_cache[str_index].c_str());
					++str_index;

					//type
					int value_type;
					svr_data::GetJsonValue(json_para["ParaType"], value_type);
					paras[i].value_type_ = static_cast<MsgParaValueType>(value_type);

					//value
					switch (value_type)
					{
					case MPVT_INT32:
						svr_data::GetJsonValue(json_para["ParaIntValue"], paras[i].i_val_);
						break;
					//case MPVT_UINT32:
					//	svr_data::GetJsonValue(para_obj["ParaIntValue"], paras[i].ui_val_);
						break;
					case MPVT_FLOAT32:
						svr_data::GetJsonValue(json_para["ParaFloatValue"], paras[i].f_val_);
						break;
					//case MPVT_DOUBLE:
					//	svr_data::GetJsonValue(para_obj["ParaIntValue"], paras[i].i_val_);
						break;
					case MPVT_STRING:
						svr_data::GetJsonValue(json_para["ParaStrValue"], str_value_cache[str_index]);
						paras[i].str_val_ = const_cast<char*>(str_value_cache[str_index].c_str());
						++str_index;
						break;
					default:
#ifdef RENDER_SKETCHER_HTML
						printf("SendVrMessageWithParas: unknown para value type,name=%s,type=%d !", paras[i].name_, value_type);
#endif						
						break;
					}
				}

				SendVrMessage(msg, &paras[0], paras.size(), delay_frame);
			}
			else
			{
				ret = false;
#ifdef RENDER_SKETCHER_HTML
				printf("SendVrMessageWithParas: Root of parse param_json_values must be array!");
#endif
			}
		}
		else
		{
#ifdef RENDER_SKETCHER_HTML
			printf("SendVrMessageWithParas: Failed to parse parse param_json_values!");
#endif
		}

		return ret;
	}

	bool DecorationVrInterface::DownloadFileCompleted(const char* file)
	{
		return decoration_vr_interface::GlobalInfoDataCommon::Instance()->GetDownLoadAssist()->DownloadFileCompleted(file);
	}

	/*void DecorationVrInterface::SetLogMark(int mark)
	{
		vr_controller_.SetLogMark(mark);
	}
	void DecorationVrInterface::PrintLogByMark(int mark, const char *fold_path)
	{
		vr_controller_.PrintLogByMark(mark, fold_path);
	}
	void DecorationVrInterface::SetValidCounterRang(bool isEnable, int min, int max)
	{
		vr_controller_.SetValidCounterRang(isEnable, min, max);
	}
	void DecorationVrInterface::StopLogCallStack()
	{
		vr_controller_.StopLogCallStack();
	}*/
}
