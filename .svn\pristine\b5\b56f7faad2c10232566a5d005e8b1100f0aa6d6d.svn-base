#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Cop;
class MaterialCopButton;
class MaterialChannel;

class VrCopVisitor : public BaseVisitor
{
public:
	VrCopVisitor();
	~VrCopVisitor();

	DEFINE_CREATE_FUN(VrCopVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_Cop> model_;
	std::shared_ptr<MaterialChannel> material_;
	std::shared_ptr<MaterialCopButton> material_button_;
	tstring GetButtonPanelTexture(int flag);
};
}
