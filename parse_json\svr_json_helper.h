#ifndef _JSON_HELPER_H_
#define _JSON_HELPER_H_

#include <stddef.h>
#include <stdint.h>

#include <string>

#include "json/json.h"

namespace svr_data
{
	bool IsNullJsonValue(const Json::Value& jv);

	bool GetJsonValue(const Json::Value& jv, rse::string& val, bool allow_empty = true);

	bool GetJsonValue(const Json::Value& jv, bool& val);

	bool GetJsonValue(const Json::Value& jv, int32_t& val);

	bool GetJsonValue(const Json::Value& jv, int64_t& val);

	bool GetJsonValue(const Json::Value& jv, float& val);

	bool GetJsonValue(const Json::Value& jv, double& val);

}//namespace svr_data

#endif//_JSON_HELPER_H_

