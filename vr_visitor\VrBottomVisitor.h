#ifndef _VR_BOTTOM_VISITOR_H_
#define _VR_BOTTOM_VISITOR_H_

#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Bottom_Accessory;
	class MaterialChannel;
	class VrBottomVisitor :public BaseVisitor
	{
	public:
		VrBottomVisitor();
		virtual ~VrBottomVisitor();

		DEFINE_CREATE_FUN(VrBottomVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	private:
		std::shared_ptr<MaterialChannel> material_;
		std::shared_ptr<Sel_Bottom_Accessory> model_;
	};
}

#endif//_VR_BOTTOM_VISITOR_H_

