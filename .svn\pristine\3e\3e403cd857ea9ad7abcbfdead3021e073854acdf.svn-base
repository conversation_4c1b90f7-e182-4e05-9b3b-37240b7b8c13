﻿#ifndef IGLOBAL_INFO_DATA_COMMON_H_B91EDD91_E5E9_4A8E_90D6_0D421A67DE93
#define IGLOBAL_INFO_DATA_COMMON_H_B91EDD91_E5E9_4A8E_90D6_0D421A67DE93

#include "PartTypeManager.h"
#include "ElevatorConfigManager.h"
#include "IElevatorPartFactory.h"
#include "vr_visitor_factory.h"
#include "PartOperatorManager.h"
#include "IDownloadVisitor.h"
#include "IDbVisitor.h"
#include "lru_cache.h"
#include "download_assist.h"
#include "file_pool.h"
#include "material_channel_pool.h"

class IDGContext;
namespace decoration_vr_interface
{
	//static const tchar *kConstDataServiceName = TSTR("/SDSExhibitDataService.svc/");
	static const tchar* kConstDataServiceName = TSTR("/SDSExhibitData/");


class GlobalInfoDataCommon
{
public:
	GlobalInfoDataCommon();
	~GlobalInfoDataCommon();

	void Initialize();
	static void Create();
	static void Destory();
	static GlobalInfoDataCommon* Instance();
	
	PartTypeManager* GetPartTypeManager() { return part_type_manager_.get(); }
	ElevatorConfigManager* GetElevatorConfigManager() { return elevator_config_manager_.get(); }
	IElevatorPartFactoryManager* GetElevatorPartFactoryManager() { return elevator_part_factory_manager_.get(); }
	VrVisitorFactory* get_vr_visitor_factory() { return &vr_visitor_factory_; }
	PartOperatorManager* GetPartOperatorManager() { return &part_operator_manager_; }

	IDownloadVisitor* GetDownloadVisitor() { return download_visitor_ptr_.get(); }
	IDbVisitor* GetDbVisitor() { return db_visitor_ptr_.get(); }
	DownloadAssist* GetDownLoadAssist() { return down_load_assist_.get(); }
	FilePool* GetFilePool() { return file_pool_.get(); }
	MaterialChannelPool* GetMaterialChannelPool() { return material_channel_pool_.get(); }

	void SetWebServiceSite(const tchar *data_site, const tchar *down_site);

	LoginInfo* GetLoginInfo();
	void SetLoginInfo(int64 lib_id, int64 user_id, int lang_id);

	const tstring& GetDataSiteUrl();
	const tstring& GetDownloadSiteUrl();

	util::lru_cache* GetLRUCache() { return lru_cache_.get(); }

	void Finalize();

	//***note***:the length of info must be less than 2k
	void LogErrorLn(const char* format, ...);

	void ReportInfoToHost(const char* format, ...);

	int GetElevatorType();
	void SetElevatorType(int elev_type);

	void SetIsStandalongVersion(bool v);
	bool IsStandalongVersion();
	bool IsOppositeDoor();

	//设置 或者获取井道深度 宽度
	double GetShaftWidth() { return shaft_width_; }
	double GetShaftDepth() { return shaft_depth_; }
	void SetShaftDepth(double depth) { shaft_depth_ = depth; }
	void SetShaftWidth(double width) { shaft_width_ = width; }

	int GetGlobalData(int key)
	{
		if (global_data_.find(key) != global_data_.end())
		{
			return global_data_[key];
		}
		return INT_MIN;
	}
	void SetGlobalData(int key, int val)
	{
		global_data_[key] = val;
	}
protected:
	IDGContext* GetDGContext();
	IDecorationVrCallBack* GetVrCallBack();

	std::shared_ptr<PartTypeManager> part_type_manager_;
    std::shared_ptr<ElevatorConfigManager> elevator_config_manager_;
    std::shared_ptr<IElevatorPartFactoryManager> elevator_part_factory_manager_;
	VrVisitorFactory vr_visitor_factory_;
	PartOperatorManager part_operator_manager_;

    std::shared_ptr<IDownloadVisitor> download_visitor_ptr_;
    std::shared_ptr<IDbVisitor> db_visitor_ptr_;

	std::shared_ptr<LoginInfo> login_info_ptr_;

	std::shared_ptr<DownloadAssist> down_load_assist_;

	std::shared_ptr<FilePool> file_pool_;

	std::shared_ptr<MaterialChannelPool> material_channel_pool_;

	tstring data_site_host_url_, download_site_host_url_;

	std::shared_ptr<util::lru_cache> lru_cache_;

	int elevator_type_; //直梯 or 扶梯

	bool is_stand_along_version_; //是否为单机版程序


public:
	int cur_part_type_;
	int handrail_pos_;
	int opposite_door_;
	rse::vector<int> partType_reflect_;	

	int cop_btn_mat_id_ = 0;
	int lop_btn_mat_id_ = 0;
	int door_type_ = 100;

	std::map<int, int> global_data_;

private:
	double shaft_width_;//井道宽度
	double shaft_depth_;//井道深度
};
}
#endif