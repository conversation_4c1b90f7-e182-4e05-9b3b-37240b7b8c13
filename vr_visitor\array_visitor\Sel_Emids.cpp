//
//  Sel_Cop.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Emids.h"


namespace decoration_vr_interface
{
	rse::string Sel_Emids::GetPath(int row)
	{
		const char* val = visitor_->GetElementString(row, 0);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Emids::SetPath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	int Sel_Emids::GetPos(int row)
	{
		return visitor_->GetElementInt(row, 1);
	}

	void Sel_Emids::SetPos(int row, int val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	float Sel_Emids::GetPos_X(int row)
	{
		return visitor_->GetElementFloat(row, 2);
	}

	void Sel_Emids::SetPos_X(int row, float val)
	{
		visitor_->SetElementValue(row, 2, val);
	}

	float Sel_Emids::GetPos_Y(int row)
	{
		return visitor_->GetElementFloat(row, 3);
	}

	void Sel_Emids::SetPos_Y(int row, float val)
	{
		visitor_->SetElementValue(row, 3, val);
	}

}