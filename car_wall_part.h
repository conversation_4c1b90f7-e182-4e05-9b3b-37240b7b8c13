#ifndef _CARWALL_PART_H_
#define _CARWALL_PART_H_

#include "IElevatorPart.h"

namespace decoration_vr_interface
{
	struct CarWallElem
	{
		int id_;

		float pos_x_;
		float pos_y_;

		float size_x_;
		float size_y_;

		float pos_z_;

		float size_zl_;
		float size_zr_;
		float size_zt_;
		float size_zb_;

		int edge_;

		float corner_x_;
		float corner_y_;
		float corner_z_;

		int corner_type_;

		float gap_;

		int marker_;

		int lm_number_;

		int64 material_id_;
	};
	typedef std::shared_ptr<CarWallElem> CarWallElemPtr;

	class CarWallPart : public IElevatorPart
	{
		friend class CarWallFactory;
	public:
		CarWallPart();
		virtual ~CarWallPart();

		virtual int GetPartType() override;
		virtual int64 GetPartId() override;

		virtual tstring GetPartName() override;
		virtual void SetPartName(const tstring &new_name) override;

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		int GetWidth();
		int GetHeight();

		int64 GetWallMaterial();
		void SetWallMaterial(int64 mat);

		int GetWallElemCount();
		const rse::vector<CarWallElemPtr>& GetWallElems() { return elems_; }

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

	private:
		int part_type_;

		int64 part_id_;

		tstring part_name_;

		int width_;
		int height_;
		bool is_reflect_;

		rse::vector<CarWallElemPtr> elems_;
	};
	typedef std::shared_ptr<CarWallPart> CarWallPartPtr;
}

#endif//_CARWALL_PART_H_