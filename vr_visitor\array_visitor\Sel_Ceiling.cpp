//
//  Sel_Ceiling.h
//  VrVisitor
//
//  Created by vrprg on 10:25:05.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Ceiling.h"


namespace decoration_vr_interface
{

rse::string Sel_Ceiling::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 0);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_Ceiling::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 0, val);
}

}