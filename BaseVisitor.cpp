#include "stdafx.h"
#include "BaseVisitor.h"
#include "VrGlobalInfo.h"
#include "file_pool.h"

namespace decoration_vr_interface
{
	BaseVisitor::BaseVisitor()
	{
	}


	BaseVisitor::~BaseVisitor()
	{
	}

	int BaseVisitor::GetType()
	{
		return part_type_;
	}

	void BaseVisitor::Initialize()
	{
		DECORATION_ASSERT(false);
	}

	bool BaseVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		return false;
	}

	void BaseVisitor::NotifyVr(VrChangeArg* vr_change)
	{
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(vr_change->part_type_);
		tstring notify_msg = type_info->vr_msg_;
		
#if defined(RENDER_SKETCHER_ANDROID)
		LOGI("send message: %s\n", notify_msg.c_str());
#endif
#if defined(RENDER_SKETCHER_HTML)
		printf("send message: %s\n", notify_msg.c_str());
#endif
		VrGlobalInfo::Instance()->get_dg_user_msg_receiver()->SendVrMessage(notify_msg.c_str(), 0);
	}

	const rse::vector<PartTypeId>& BaseVisitor::GetAvailablePartTypeId()
	{
		return available_parttypeid_list_;
	}

	void BaseVisitor::PrintData(const tchar* file_name)
	{
	}

	void BaseVisitor::AddFileToModelCache(int part_type, int64 id, const tstring& file, const tstring& local_path)
	{
		GlobalInfoDataCommon::Instance()->GetFilePool()->AddDownloadFileList(part_type, id, file.c_str(), local_path.c_str());
	}

	void decoration_vr_interface::BaseVisitor::InitializeAvailablePartTypes(const rse::vector<PartTypeId>& typs)
	{
		available_parttypeid_list_ = typs;
	}
}