#ifndef IELEVATOR_CONFIG_MANAGER_DATA_COMMON_H_8EF51C1A_6215_41AA_881B_E94596395EAA
#define IELEVATOR_CONFIG_MANAGER_DATA_COMMON_H_8EF51C1A_6215_41AA_881B_E94596395EAA
#include "IConfig.h"
#include "IElevatorConfig.h"

namespace decoration_vr_interface
{
class ElevatorConfigManager
{
public:
	void SetCurrentWorkingElevatorConfig(int index);
	int GetCurrentElevatorId();

	IConfigPtr GetCarConfig(int elevOrderId);
	IConfigPtr GetHallConfig(int elevOrderId);
	IConfigPtr GetEscalatorConfig(int elevOrderId);

	IElevatorConfigPtr GetCurrentElevatorConfig();
	IConfigPtr GetCurrentCarConfig();
	IConfigPtr GetCurrentHallConfig();

	ElevatorSize* GetCurrentElevatorSize();
	EscalatorSize* GetCurrentEscalatorSize();

	void Finalize();
public:
	IConfigPtr GetCurrentEscalatorConfig();

	rse::vector<IElevatorConfigPtr>* get_elevator_config_list_ptr() { return &elevator_config_list_; }
protected:
	rse::vector<IElevatorConfigPtr> elevator_config_list_;
	int current_working_config_index_;
};
}

#endif