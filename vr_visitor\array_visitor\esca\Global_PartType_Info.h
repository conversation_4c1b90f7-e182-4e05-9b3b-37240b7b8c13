#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_PartType_Info : public DGBaseVisitor
		{
		public:
			Global_PartType_Info(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			int GetnPartType(int row);
			void SetnPartType(int row, int val);

			rse::string Getparttypename(int row);
			void Setparttypename(int row, const tchar* val);

			rse::string GetstrMsgName(int row);
			void SetstrMsgName(int row, const tchar* val);

			rse::string GetActivatScriptName(int row);
			void SetActivatScriptName(int row, const tchar* val);

			rse::string Getdelscript_arrName(int row);
			void Setdelscript_arrName(int row, const tchar* val);

			int GetCopyObjectMain(int row);
			void SetCopyObjectMain(int row, int val);

			int GetCopyObjectMaterial(int row);
			void SetCopyObjectMaterial(int row, int val);

		};
	}

}