#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Shaft;
	class MaterialChannel;
	class VrHallShaftVisitor : public BaseVisitor
	{
	public:
		VrHallShaftVisitor();
		~VrHallShaftVisitor();

		DEFINE_CREATE_FUN(VrHallShaftVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<Sel_Shaft> model_;
		std::shared_ptr<MaterialChannel> material_;
#if defined(XJ)
		std::shared_ptr<MaterialChannel> floor_material_;
#endif
	};

}