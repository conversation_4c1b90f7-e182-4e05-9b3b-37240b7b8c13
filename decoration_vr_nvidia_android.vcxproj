﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="NsightTegraProject">
    <NsightTegraProjectRevisionNumber>11</NsightTegraProjectRevisionNumber>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Tegra-Android">
      <Configuration>Debug</Configuration>
      <Platform>Tegra-Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Releasev7a|Tegra-Android">
      <Configuration>Releasev7a</Configuration>
      <Platform>Tegra-Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Tegra-Android">
      <Configuration>Release</Configuration>
      <Platform>Tegra-Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|Tegra-Android">
      <Configuration>Shipping</Configuration>
      <Platform>Tegra-Android</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="arg_cache.h" />
    <ClInclude Include="BasePartOperator.h" />
    <ClInclude Include="BaseVisitor.h" />
    <ClInclude Include="car_top.h" />
    <ClInclude Include="car_top_factory.h" />
    <ClInclude Include="car_wall_factory.h" />
    <ClInclude Include="car_wall_part.h" />
    <ClInclude Include="common_part.h" />
    <ClInclude Include="common_part_factory.h" />
    <ClInclude Include="ConfigAnalyzer.h" />
    <ClInclude Include="ConfigOperator.h" />
    <ClInclude Include="config_factory.h" />
    <ClInclude Include="config_part.h" />
    <ClInclude Include="ConstPartInnerParams.h" />
    <ClInclude Include="ConstValueMap.h" />
    <ClInclude Include="db_visitor.h" />
    <ClInclude Include="decoration_vr_array_visitor.h" />
    <ClInclude Include="decoration_vr_interface.h" />
    <ClInclude Include="decoration_vr_interface_include.h" />
    <ClInclude Include="download_assist.h" />
    <ClInclude Include="download_visitor.h" />
    <ClInclude Include="dvi_tinyxml2.h" />
    <ClInclude Include="ElectricOperator.h" />
    <ClInclude Include="electric_factory.h" />
    <ClInclude Include="electric_part.h" />
    <ClInclude Include="ElevatorConfig.h" />
    <ClInclude Include="ElevatorConfigManager.h" />
    <ClInclude Include="ElevatorPartFactoryManager.h" />
    <ClInclude Include="ElevatorPartOperator.h" />
    <ClInclude Include="ElevatorSize.h" />
    <ClInclude Include="elevator_size_parser.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="esca\EscaAcessOverFactory.h" />
    <ClInclude Include="esca\EscaBalustradeFactory.h" />
    <ClInclude Include="esca\EscaHandrailEnterFactory.h" />
    <ClInclude Include="esca\EscaSideCladdingFactory.h" />
    <ClInclude Include="esca\EscaSkirtLightingFactory.h" />
    <ClInclude Include="esca\esca_elevator_part.h" />
    <ClInclude Include="esca\esca_elevator_part_factory.h" />
    <ClInclude Include="file_pool.h" />
    <ClInclude Include="FrontWallTypeOperator.h" />
    <ClInclude Include="GlobalInfoDataCommon.h" />
    <ClInclude Include="hall_room_part.h" />
    <ClInclude Include="hall_room_part_factory.h" />
    <ClInclude Include="IConfig.h" />
    <ClInclude Include="IDbVisitor.h" />
    <ClInclude Include="IDownloadVisitor.h" />
    <ClInclude Include="IElevatorConfig.h" />
    <ClInclude Include="IElevatorPart.h" />
    <ClInclude Include="IElevatorPartFactory.h" />
    <ClInclude Include="include\decoration_vr_interface_lib.h" />
    <ClInclude Include="include\i_decoration_vr_array_visitor.h" />
    <ClInclude Include="include\i_decoration_vr_callback.h" />
    <ClInclude Include="include\i_decoration_vr_interface.h" />
    <ClInclude Include="include\i_decoration_vr_interface_extend.h" />
    <ClInclude Include="include\MsgParaValue.h" />
    <ClInclude Include="include\part_type.h" />
    <ClInclude Include="IPartTypeManager.h" />
    <ClInclude Include="jsoncpp\include\json\allocator.h" />
    <ClInclude Include="jsoncpp\include\json\assertions.h" />
    <ClInclude Include="jsoncpp\include\json\autolink.h" />
    <ClInclude Include="jsoncpp\include\json\config.h" />
    <ClInclude Include="jsoncpp\include\json\features.h" />
    <ClInclude Include="jsoncpp\include\json\forwards.h" />
    <ClInclude Include="jsoncpp\include\json\json.h" />
    <ClInclude Include="jsoncpp\include\json\reader.h" />
    <ClInclude Include="jsoncpp\include\json\value.h" />
    <ClInclude Include="jsoncpp\include\json\version.h" />
    <ClInclude Include="jsoncpp\include\json\writer.h" />
    <ClInclude Include="jsoncpp\src\lib_json\json_tool.h" />
    <ClInclude Include="material_channel_pool.h" />
    <ClInclude Include="netWork\HttpAsyncManage.h" />
    <ClInclude Include="parse_json\BaseFactory.h" />
    <ClInclude Include="parse_json\svr_base_data.h" />
    <ClInclude Include="parse_json\svr_car_wall.h" />
    <ClInclude Include="parse_json\svr_common_part.h" />
    <ClInclude Include="parse_json\svr_config.h" />
    <ClInclude Include="parse_json\svr_file_digital_info.h" />
    <ClInclude Include="parse_json\svr_json_helper.h" />
    <ClInclude Include="parse_json\svr_material.h" />
    <ClInclude Include="parse_json\svr_material_channel.h" />
    <ClInclude Include="parse_json\svr_material_special_rule.h" />
    <ClInclude Include="parse_json\svr_part_basic_info.h" />
    <ClInclude Include="parse_json\svr_part_material.h" />
    <ClInclude Include="parse_json\svr_part_model_info.h" />
    <ClInclude Include="parse_json\svr_wall_element.h" />
    <ClInclude Include="parse_json\svr_wall_size_info.h" />
    <ClInclude Include="PartOperatorManager.h" />
    <ClInclude Include="PartTypeManager.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="skirting_factory.h" />
    <ClInclude Include="skirting_part.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="Util.h" />
    <ClInclude Include="util\lru_cache.h" />
    <ClInclude Include="util\sha256.h" />
    <ClInclude Include="vr_visitor\array_visitor\DGBaseVisitor.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Hwndmsg.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Loading_Scence.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Other_Datas.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Select.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Car.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Hall.h" />
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Parameter.h" />
    <ClInclude Include="vr_visitor\array_visitor\material_channel.h" />
    <ClInclude Include="vr_visitor\array_visitor\ModelCarWallElem.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Accessory.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_CarIndicator.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Car_Shell.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Ceiling.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Cop.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_CopLcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Door_Imported.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Emids.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_FireBox.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Hall.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HandRail.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HI.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HILcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HL.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_HLLcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Jamb.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Lop.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_LopLcd.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Mirror.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Shaft.h" />
    <ClInclude Include="vr_visitor\IVrVisitor.h" />
    <ClInclude Include="vr_visitor\VrAccessoryVisitor.h" />
    <ClInclude Include="vr_visitor\VrBottomVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarConfigVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarDoorVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarIndicatorVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarShellVisitor.h" />
    <ClInclude Include="vr_visitor\VrCarWallVisitor.h" />
    <ClInclude Include="vr_visitor\VrChangeArg.h" />
    <ClInclude Include="vr_visitor\VrConfigInfo.h" />
    <ClInclude Include="vr_visitor\VrCopDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrCopVisitor.h" />
    <ClInclude Include="vr_visitor\VrEmidsVisitor.h" />
    <ClInclude Include="vr_visitor\VrFrontWallVisitor.h" />
    <ClInclude Include="vr_visitor\VrGlobalInfo.h" />
    <ClInclude Include="vr_visitor\VrHallConfigVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallDoorVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallFireBoxVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallFloorVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallIndicatorDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallIndicatorVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallShaftVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallVisitor.h" />
    <ClInclude Include="vr_visitor\VrHallWallVisitor.h" />
    <ClInclude Include="vr_visitor\VrHandrailVisitor.h" />
    <ClInclude Include="vr_visitor\VrJambVisitor.h" />
    <ClInclude Include="vr_visitor\VrLanternDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrLanternVisitor.h" />
    <ClInclude Include="vr_visitor\VrLopDisplayVisitor.h" />
    <ClInclude Include="vr_visitor\VrLopVisitor.h" />
    <ClInclude Include="vr_visitor\VrMirrorVisitor.h" />
    <ClInclude Include="vr_visitor\VrSightseeingVisitor.h" />
    <ClInclude Include="vr_visitor\VrTopVisitor.h" />
    <ClInclude Include="vr_visitor\vr_controller.h" />
    <ClInclude Include="vr_visitor\vr_visitor_factory.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="arg_cache.cpp" />
    <ClCompile Include="BasePartOperator.cpp" />
    <ClCompile Include="BaseVisitor.cpp" />
    <ClCompile Include="car_top.cpp" />
    <ClCompile Include="car_top_factory.cpp" />
    <ClCompile Include="car_wall_factory.cpp" />
    <ClCompile Include="car_wall_part.cpp" />
    <ClCompile Include="common_part.cpp" />
    <ClCompile Include="common_part_factory.cpp" />
    <ClCompile Include="ConfigAnalyzer.cpp" />
    <ClCompile Include="ConfigOperator.cpp" />
    <ClCompile Include="config_factory.cpp" />
    <ClCompile Include="config_part.cpp" />
    <ClCompile Include="ConstValueMap.cpp" />
    <ClCompile Include="db_visitor.cpp" />
    <ClCompile Include="decoration_vr_array_visitor.cpp" />
    <ClCompile Include="decoration_vr_interface.cpp" />
    <ClCompile Include="decoration_vr_interface_lib.cpp" />
    <ClCompile Include="download_assist.cpp" />
    <ClCompile Include="download_visitor.cpp" />
    <ClCompile Include="dvi_tinyxml2.cpp" />
    <ClCompile Include="ElectricOperator.cpp" />
    <ClCompile Include="electric_factory.cpp" />
    <ClCompile Include="electric_part.cpp" />
    <ClCompile Include="ElevatorConfig.cpp" />
    <ClCompile Include="ElevatorConfigManager.cpp" />
    <ClCompile Include="ElevatorPartFactoryManager.cpp" />
    <ClCompile Include="ElevatorPartOperator.cpp" />
    <ClCompile Include="ElevatorSize.cpp" />
    <ClCompile Include="elevator_size_parser.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="esca\EscaAcessOverFactory.cpp" />
    <ClCompile Include="esca\EscaBalustradeFactory.cpp" />
    <ClCompile Include="esca\EscaHandrailEnterFactory.cpp" />
    <ClCompile Include="esca\EscaSideCladdingFactory.cpp" />
    <ClCompile Include="esca\EscaSkirtLightingFactory.cpp" />
    <ClCompile Include="esca\esca_elevator_part.cpp" />
    <ClCompile Include="esca\esca_elevator_part_factory.cpp" />
    <ClCompile Include="file_pool.cpp" />
    <ClCompile Include="FrontWallTypeOperator.cpp" />
    <ClCompile Include="GlobalInfoDataCommon.cpp" />
    <ClCompile Include="hall_room_part.cpp" />
    <ClCompile Include="hall_room_part_factory.cpp" />
    <ClCompile Include="jsoncpp\src\lib_json\json_reader.cpp" />
    <ClCompile Include="jsoncpp\src\lib_json\json_value.cpp" />
    <ClCompile Include="jsoncpp\src\lib_json\json_writer.cpp" />
    <ClCompile Include="material_channel_pool.cpp" />
    <ClCompile Include="netWork\HttpAsyncManage.cpp" />
    <ClCompile Include="parse_json\svr_car_wall.cpp" />
    <ClCompile Include="parse_json\svr_common_part.cpp" />
    <ClCompile Include="parse_json\svr_config.cpp" />
    <ClCompile Include="parse_json\svr_file_digital_info.cpp" />
    <ClCompile Include="parse_json\svr_json_helper.cpp" />
    <ClCompile Include="parse_json\svr_material.cpp" />
    <ClCompile Include="parse_json\svr_material_channel.cpp" />
    <ClCompile Include="parse_json\svr_material_special_rule.cpp" />
    <ClCompile Include="parse_json\svr_part_basic_info.cpp" />
    <ClCompile Include="parse_json\svr_part_material.cpp" />
    <ClCompile Include="parse_json\svr_part_model_info.cpp" />
    <ClCompile Include="parse_json\svr_wall_element.cpp" />
    <ClCompile Include="parse_json\svr_wall_size_info.cpp" />
    <ClCompile Include="PartOperatorManager.cpp" />
    <ClCompile Include="PartTypeManager.cpp" />
    <ClCompile Include="skirting_factory.cpp" />
    <ClCompile Include="skirting_part.cpp" />
    <ClCompile Include="stdafx.cpp" />
    <ClCompile Include="Util.cpp" />
    <ClCompile Include="util\lru_cache.cpp" />
    <ClCompile Include="util\sha256.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\DGBaseVisitor.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Hwndmsg.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Loading_Scence.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Other_Datas.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Select.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Car.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Hall.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Parameter.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\material_channel.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\ModelCarWallElem.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Accessory.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_CarIndicator.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Car_Shell.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Ceiling.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Cop.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_CopLcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Door_Imported.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Emids.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_FireBox.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Hall.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HandRail.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HI.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HILcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HL.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_HLLcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Jamb.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Lop.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_LopLcd.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Mirror.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Shaft.cpp" />
    <ClCompile Include="vr_visitor\VrAccessoryVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrBottomVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarConfigVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarDoorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarIndicatorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarShellVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCarWallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrChangeArg.cpp" />
    <ClCompile Include="vr_visitor\VrConfigInfo.cpp" />
    <ClCompile Include="vr_visitor\VrCopDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrCopVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrEmidsVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrFrontWallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrGlobalInfo.cpp" />
    <ClCompile Include="vr_visitor\VrHallConfigVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallDoorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallFireBoxVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallFloorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallIndicatorDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallIndicatorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallShaftVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHallWallVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrHandrailVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrJambVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLanternDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLanternVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLopDisplayVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrLopVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrMirrorVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrSightseeingVisitor.cpp" />
    <ClCompile Include="vr_visitor\VrTopVisitor.cpp" />
    <ClCompile Include="vr_visitor\vr_controller.cpp" />
    <ClCompile Include="vr_visitor\vr_visitor_factory.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="jsoncpp\src\lib_json\json_valueiterator.inl" />
    <None Include="jsoncpp\src\lib_json\version.h.in" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{827C4DB1-A8A1-4670-9DAD-585A96EFCFFD}</ProjectGuid>
    <RootNamespace>decoration_vr_nvidia_android</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|Tegra-Android'">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <AndroidTargetAPI>android-24</AndroidTargetAPI>
    <NdkToolchainVersion>gcc-4.9</NdkToolchainVersion>
    <AndroidMinAPI>android-24</AndroidMinAPI>
    <AndroidNativeAPI>UseTarget</AndroidNativeAPI>
    <AndroidArch>arm64-v8a</AndroidArch>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|Tegra-Android'">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <AndroidTargetAPI>android-26</AndroidTargetAPI>
    <AndroidMinAPI>android-24</AndroidMinAPI>
    <AndroidNativeAPI>UseMin</AndroidNativeAPI>
    <NdkToolchainVersion>gcc-4.9</NdkToolchainVersion>
    <AndroidArch>arm64-v8a</AndroidArch>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Releasev7a|Tegra-Android'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <AndroidTargetAPI>android-26</AndroidTargetAPI>
    <AndroidMinAPI>android-24</AndroidMinAPI>
    <AndroidNativeAPI>UseMin</AndroidNativeAPI>
    <NdkToolchainVersion>gcc-4.9</NdkToolchainVersion>
    <AndroidArch>armv7-a</AndroidArch>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Shipping|Tegra-Android'">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <AndroidTargetAPI>android-15</AndroidTargetAPI>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <PropertyGroup Label="UserMacros">
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Tegra-Android'">
    <OutDir>..\..\..\rse\lib_android\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>..\..\..\rse\temp_android\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Tegra-Android'">
    <IntDir>..\..\..\rse\temp_android\$(Platform)\$(Configuration)\</IntDir>
    <OutDir>..\..\..\rse\lib_android\$(Platform)\$(Configuration)\arm64-v8a</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Releasev7a|Tegra-Android'">
    <IntDir>..\..\..\rse\temp_android\$(Platform)\$(Configuration)\</IntDir>
    <OutDir>..\..\..\rse\lib_android\$(Platform)\$(Configuration)\armv7-a</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Tegra-Android'">
    <ClCompile>
      <CppLanguageStandard>gnu++11</CppLanguageStandard>
      <PreprocessorDefinitions>RENDER_SKETCHER_ANDROID;RSE_STATIC_LIB;DECORATION_VR_INTERFACELIB;__ANDROID__;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <GccExceptionHandling>true</GccExceptionHandling>
      <AdditionalIncludeDirectories>$(StlIncludeDirectories);$(VS_NdkRoot)\platforms\$(AndroidAPILevel)\arch-arm\usr\include;..\..\..\rse\tools\RseDiscoverGraph\RseDiscoverGraph\Include;.\include;.\util;.\vr_visitor\array_visitor;.\vr_visitor;.\jsoncpp\src\lib_json;.\jsoncpp\include;.\netWork;$(ProjectDir)</AdditionalIncludeDirectories>
      <CLanguageStandard>c99</CLanguageStandard>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Tegra-Android'">
    <ClCompile>
      <CppLanguageStandard>gnu++11</CppLanguageStandard>
      <PreprocessorDefinitions>RENDER_SKETCHER_ANDROID;RSE_STATIC_LIB;DECORATION_VR_INTERFACELIB;OPTIMIZATION;__ANDROID__;XIZI_OTIS</PreprocessorDefinitions>
      <GccExceptionHandling>true</GccExceptionHandling>
      <AdditionalIncludeDirectories>.\esca;$(StlIncludeDirectories);$(VS_NdkRoot)\platforms\$(AndroidAPILevel)\arch-arm\usr\include;..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;.\include;.\util;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\src\lib_json;.\jsoncpp\include;.\netWork;..\..\..\rse\engine_mt\include;$(ProjectDir)</AdditionalIncludeDirectories>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CLanguageStandard>c99</CLanguageStandard>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Releasev7a|Tegra-Android'">
    <ClCompile>
      <CppLanguageStandard>gnu++11</CppLanguageStandard>
      <PreprocessorDefinitions>RENDER_SKETCHER_ANDROID;TARGET_ARM_V7A;RSE_STATIC_LIB;DECORATION_VR_INTERFACELIB;OPTIMIZATION;__ANDROID__;XIZI_OTIS</PreprocessorDefinitions>
      <GccExceptionHandling>true</GccExceptionHandling>
      <AdditionalIncludeDirectories>.\esca;$(StlIncludeDirectories);$(VS_NdkRoot)\platforms\$(AndroidAPILevel)\arch-arm\usr\include;..\..\..\rse\tools\RseDiscoverGraph_mt\RseDiscoverGraph\Include;.\include;.\util;.\vr_visitor\array_visitor\esca;.\vr_visitor\array_visitor;.\vr_visitor\esca;.\vr_visitor;.\jsoncpp\src\lib_json;.\jsoncpp\include;.\netWork;..\..\..\rse\engine_mt\include;$(ProjectDir)</AdditionalIncludeDirectories>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <CLanguageStandard>c99</CLanguageStandard>
      <OptimizationLevel>O3</OptimizationLevel>
      <GenerateDebugInformation>false</GenerateDebugInformation>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Tegra-Android'">
    <ClCompile>
      <CppLanguageStandard>gnu++11</CppLanguageStandard>
    </ClCompile>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>