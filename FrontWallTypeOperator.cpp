#include "stdafx.h"
#include "FrontWallTypeOperator.h"
#include "ConfigAnalyzer.h"
#include "electric_part.h"

namespace decoration_vr_interface
{
	FrontWallTypeOperator::FrontWallTypeOperator()
	{
	}


	FrontWallTypeOperator::~FrontWallTypeOperator()
	{
	}

	bool FrontWallTypeOperator::SetPart(int part_type, int64 part_id)
	{
		auto part = ConfigAnalyzer::GetPart(PartTypeCop);
		if (part != nullptr)
		{
			static_cast<ElectricPart*>(part)->SetPanelType(part_id);
			return true;
		}
		return false;
	}

	bool FrontWallTypeOperator::SetPartByContent(int part_type, const rse::string& c)
	{
		auto part = ConfigAnalyzer::GetPart(PartTypeCop);
		if (part != nullptr)
		{
			//static_cast<ElectricPart*>(part)->SetPanelType(part_id);
			return true;
		}
		return false;
	}

	int FrontWallTypeOperator::GetNotifyPartType(int part_type)
	{
		return PartTypeFrontWall;
	}

	int64 FrontWallTypeOperator::GetCurrentPartId(int part_type)
	{
		auto part = ConfigAnalyzer::GetPart(PartTypeCop);
		if (part != nullptr)
		{
			return static_cast<ElectricPart*>(part)->GetPanelType();
		}

		return -1;
	}
	const char* FrontWallTypeOperator::GetCurrentPartName(int part_type)
	{
		return TSTR("");
	}
}
