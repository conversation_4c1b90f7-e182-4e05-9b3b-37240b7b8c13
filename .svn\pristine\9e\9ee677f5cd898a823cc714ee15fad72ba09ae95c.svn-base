#include "stdafx.h"
#include "EscalatorSize.h"

namespace decoration_vr_interface
{

	EscalatorSize EscalatorSize::GetEscalatorSizeFromDb(int id)
	{
		EscalatorSize esca_size;
		/*auto escalator_size_set = db_visitor::DBVisitorMgr::Instance()->GetVisitor()->LoadEscalatorSize(id);

		escalator_size_set.Reset();
		if (!escalator_size_set.IsEof())
		{

			esca_size.id_ = id;
			esca_size.data_name_ = Util::StringToTString(escalator_size_set.GetStringField("UC_DATA_NAME").c_str());
			esca_size.width_ = escalator_size_set.GetIntField("UC_WIDTH");
			esca_size.height_ = escalator_size_set.GetIntField("UC_DOOR_HEIGHT");
			esca_size.actual_id_ = escalator_size_set.GetIntField("UC_ACTUAL_ID");
		}*/

		esca_size.id_ = id;
		esca_size.width_ = 100;
		//esca_size.depth_ = 100;
		esca_size.height_ = 300;
		//esca_size.door_width_ = 0;
		//esca_size.door_height_ = 0;

		return std::move(esca_size);
	}

	EscalatorSize EscalatorSize::GetEscalatorSizeFromDb(int width, int height)
	{
		EscalatorSize esca_size;
		/*auto escalator_size_set = db_visitor::DBVisitorMgr::Instance()->GetVisitor()->LoadEscalatorSize(width, height);

		escalator_size_set.Reset();
		if (!escalator_size_set.IsEof())
		{
			esca_size.id_ = escalator_size_set.GetIntField("UC_ID");
			esca_size.data_name_ = Util::StringToTString(escalator_size_set.GetStringField("UC_DATA_NAME").c_str());
			esca_size.width_ = escalator_size_set.GetIntField("UC_WIDTH");
			esca_size.height_ = escalator_size_set.GetIntField("UC_DOOR_HEIGHT");
			esca_size.actual_id_ = escalator_size_set.GetIntField("UC_ACTUAL_ID");
		}*/

		esca_size.id_ = 0;
		esca_size.width_ = width;
		//esca_size.depth_ = depth;
		esca_size.height_ = height;
		//esca_size.door_width_ = 0;
		//esca_size.door_height_ = 0;

		return std::move(esca_size);
	}

}