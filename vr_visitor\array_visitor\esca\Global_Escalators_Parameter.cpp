#include "stdafx.h"
#include "Global_Escalators_Parameter.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		bool Global_Escalators_Parameter::Getselect(int row)
		{
			return visitor_->GetElementBool(row, 0);
		}

		void Global_Escalators_Parameter::Setselect(int row, bool val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Global_Escalators_Parameter::Getstates(int row)
		{
			return visitor_->GetElementInt(row, 3);
		}

		void Global_Escalators_Parameter::Setstates(int row, int val)
		{
			visitor_->SetElementValue(row, 3, val);
		}

		int Global_Escalators_Parameter::GetReflect(int row)
		{
			return visitor_->GetElementInt(row, 4);
		}

		void Global_Escalators_Parameter::SetReflect(int row, int val)
		{
			visitor_->SetElementValue(row, 4, val);
		}
	}

}