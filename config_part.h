#pragma once

#include "IConfig.h"

namespace decoration_vr_interface
{
	class Config : public IConfig
	{
		friend class ConfigFactory;
	public:
		Config();
		~Config();

	public:
		virtual int GetPartType() override { return part_type_; }
		virtual int64 GetPartId() override { return id_; }

		virtual tstring GetPartName() override { return TSTR(""); }
		virtual void SetPartName(const tstring &new_name) override { }

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

	public:
		virtual IElevatorPart* GetPart(int part_type) override;
		virtual void SetPart(int part_type, IElevatorPartPtr part) override;
		virtual void RemovePart(int part_type) override;

		virtual IElevatorConfig* GetElevatorConfig() override;
		void SetElevatorConfig(IElevatorConfig* material);

		virtual Variant* GetExtendProperty(ExtendPropertyKey key) override;
		virtual void SetExtendProperty(ExtendPropertyKey key, VariantPtr variant_ptr) override;
		  
	protected:
		int64 id_;
		int part_type_;

		rse::map<int, IElevatorPartPtr> parts_;
		rse::map<int, rse::vector<IElevatorPartPtr>> multi_parts_;
		rse::map<ExtendPropertyKey, VariantPtr> propertys_;

		IElevatorConfig* elevator_config_;

		bool is_reflect_;
	};
}
