#ifndef _VR_EscaBalustradeVisitor_H_
#define _VR_EscaBalustradeVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Balustrade.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaBalustradeVisitor : public BaseVisitor
	{
	public:
		VrEscaBalustradeVisitor();
		virtual ~VrEscaBalustradeVisitor();

		DEFINE_CREATE_FUN(VrEscaBalustradeVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<esca::Sel_Esca_Balustrade> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif //endif
