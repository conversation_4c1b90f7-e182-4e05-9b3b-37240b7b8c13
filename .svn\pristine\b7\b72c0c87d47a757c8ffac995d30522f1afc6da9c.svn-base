#include "stdafx.h"
#include "Sel_Esca_Access_Cover_Flag.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		rse::string Sel_Esca_Access_Cover_Flag::Getstr_Path(int row)
		{
			const char* val = visitor_->GetElementString(row, 0);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Access_Cover_Flag::Setstr_Path(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		float Sel_Esca_Access_Cover_Flag::Getfloat_Position_X(int row)
		{
			return visitor_->GetElementFloat(row, 1);
		}

		void Sel_Esca_Access_Cover_Flag::Setfloat_Position_X(int row, float val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		float Sel_Esca_Access_Cover_Flag::Getfloat_Position_Y(int row)
		{
			return visitor_->GetElementFloat(row, 2);
		}

		void Sel_Esca_Access_Cover_Flag::Setfloat_Position_Y(int row, float val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

		float Sel_Esca_Access_Cover_Flag::Getfloat_Size_X(int row)
		{
			return visitor_->GetElementFloat(row, 3);
		}

		void Sel_Esca_Access_Cover_Flag::Setfloat_Size_X(int row, float val)
		{
			visitor_->SetElementValue(row, 3, val);
		}

		float Sel_Esca_Access_Cover_Flag::Getfloat_Size_Y(int row)
		{
			return visitor_->GetElementFloat(row, 4);
		}

		void Sel_Esca_Access_Cover_Flag::Setfloat_Size_Y(int row, float val)
		{
			visitor_->SetElementValue(row, 4, val);
		}
	}


}