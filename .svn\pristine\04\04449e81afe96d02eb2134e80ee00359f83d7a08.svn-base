#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_Escalators_Parameter : public DGBaseVisitor
		{
		public:
			Global_Escalators_Parameter(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			bool Getselect(int row);
			void Setselect(int row, bool val);

			int Getstates(int row);
			void Setstates(int row, int val);

			int GetReflect(int row);
			void SetReflect(int row, int val);

		};
	}

}