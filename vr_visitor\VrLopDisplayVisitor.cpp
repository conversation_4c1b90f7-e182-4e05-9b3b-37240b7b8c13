#include "stdafx.h"
#include "VrLopDisplayVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_LopLcd.h"
#include "electric_part.h"

namespace decoration_vr_interface
{

	VrLopDisplayVisitor::VrLopDisplayVisitor()
	{
        part_type_ = PartTypeLopDisplay;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		InitializeAvailablePartTypes(types);

	} 

	VrLopDisplayVisitor::~VrLopDisplayVisitor()
	{
	}


	void VrLopDisplayVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_LopLcd>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}
	}

	bool VrLopDisplayVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		model_->Clear();
		auto elevator_list_ptr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->get_elevator_config_list_ptr();

		tstring dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		for (int elevator_i = 0; elevator_i < elevator_list_ptr->size(); ++elevator_i)
		{
			auto elevator = (*elevator_list_ptr)[elevator_i];
			IConfigPtr hall_config = elevator->GetHallConfig(0);

			auto part = static_cast<ElectricPart*>(hall_config->GetPart(PartTypeLop));
			if (part->GetSetupOrientation() == kCallSetupDoorParrellel && elevator_i % 2 != 0)
			{
				continue;
			}

			tstring lcd_path = part->GetLcdPath();
			int row = model_->AddRow();
			if (lcd_path == TSTR(""))
			{
				model_->Setm_bool_Select(row, false);
			}
			else
			{
				model_->Setm_bool_Select(row, true);
				auto path = Util::CombinePath(dir, lcd_path);
				AddFileToModelCache(part->GetLcdType(), part->GetLcd(), lcd_path, path);
				model_->Setm_str_Path(row, path.c_str());
				model_->Setm_int_Mark(row, 0);
			}
		}

		return true;
	}

	void VrLopDisplayVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
	}

}
    