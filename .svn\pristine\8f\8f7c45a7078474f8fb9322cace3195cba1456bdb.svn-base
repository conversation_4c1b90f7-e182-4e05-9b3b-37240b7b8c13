﻿#include "stdafx.h"
#include "PartTypeManager.h"
#include "VrGlobalInfo.h"
#include "dvi_tinyxml2.h"

namespace decoration_vr_interface
{

void PartTypeManager::Initialize()
{
	InitializePartTypeInfoList();
}

void PartTypeManager::InitializePartTypeInfoList()
{	
	tstring raw_path = Util::CombinePath(
		VrGlobalInfo::Instance()->get_config_info()->resouseFilePath, TSTR("partCfg.xml"));
	rse::string path = Util::TStringToString(raw_path.c_str());
	dvi_tinyxml2::XMLDocument doc;
	if (doc.LoadFile(path.c_str()) == dvi_tinyxml2::XML_NO_ERROR)
	{
		auto root = doc.RootElement();
		auto elem = root->FirstChildElement("Part");
		while (elem)
		{
			auto info = RSE_MAKE_SHARED<PartTypeInfo>();
			info->id_ = elem->IntAttribute("ID");
			info->parent_ = elem->IntAttribute("Parent");
			info->name_ = Util::StringToTString(elem->Attribute("Name"));
			//LOGI("PartTypeManager::InitializePartTypeInfoList nfo->name: %s\n", info->name.c_str());
			info->model_array_ = Util::StringToTString(elem->Attribute("ModelArrayName"));
			info->material_array_ = Util::StringToTString(elem->Attribute("MaterialArrayName"));
			info->vr_msg_ = Util::StringToTString(elem->Attribute("VRMessage"));
			info->vr_type_ = elem->IntAttribute("VRType");
			info->category_ = elem->IntAttribute("Category");	
			type_info_list_.push_back(info);

			elem = elem->NextSiblingElement("Part");

			
		}
	}
}

PartTypeInfo* PartTypeManager::GetTypeInfo(int part_type)
{
	auto it = std::find_if(type_info_list_.begin(), type_info_list_.end(), 
		[&part_type](const PartTypeInfoPtr& ptr){ return ptr->id_ == part_type; });

	if (it != type_info_list_.end())
	{
		return it->get();
	}
	LOGI("PartTypeManager::GetTypeInfo return null parttype: %d\n", part_type);
	return nullptr;
}    

PartTypeInfo* PartTypeManager::GetTypeInfo(tstring name)
{
	auto it = std::find_if(type_info_list_.begin(), type_info_list_.end(),
		[&name](const PartTypeInfoPtr& ptr) { return ptr->name_ == name; });

	if (it != type_info_list_.end())
	{
		return it->get();
	}

	return nullptr;
}

MaterialPartType PartTypeManager::GetMaterialPartType(int part_type)
{
	switch (part_type)
	{
	case PartTypeTop:
		return MPT_TOP;
	case PartTypeLeftWall:
	case PartTypeRightWall:
	case PartTypeBackWall:
		return MPT_WALL;

	case PartTypeButton:

	case PartTypeCop:
	case PartTypeAuxCop:
	case PartTypeHDCop:
	case PartTypeAuxHDCop:

	case PartTypeCopButton:
	case PartTypeAuxCopButton:
	case PartTypeHDCopButton:
	case PartTypeAuxHDCopButton:

	case PartTypeCopDisplay:
	case PartTypeAuxCopDisplay:
	case PartTypeHDCopDisplay:
	case PartTypeAuxHDCopDisplay:

	case PartTypeLantern:
	case PartTypeLanternDisplay:

	case PartTypeHallIndicator:
	case PartTypeHallIndicatorDisplay:
	case PartTypeHallIndicatorPlate:

	case PartTypeLop:
	case PartTypeLopButton:
	case PartTypeLopDisplay:

	case PartTypeCarIndicator:
	case PartTypeHallFireBox:
	case PartTypeEMIDS:
	case PartTypeEMIDSDisplay:
		return MPT_ELECTRIC;
	case PartTypeFrontWall:
	case PartTypeCopWall:
	case PartTypeBottom:
	case PartTypeDoorHeader:
	case PartTypeEntranceColumn:
	case PartTypeFrontWallSkirting:
	case PartTypeCarDoor:
	case PartTypeCarDoorTrim:
	case PartTypeTrim:
	case PartTypeHandrail:
	case PartTypeMirror:
	case PartTypeMonitor:
	case PartTypeHIBPlate:
	case PartTypeHallBottom:
	case PartTypeHallWall:
	case PartTypeJamb:
	case PartTypeHallDoor:
	case PartTypeSkirting:
	case PartTypeHallDoorTrim:
	case PartTypeLeftAccessory:
	case PartTypeBackAccessory:
	case PartTypeRightAccessory:
	case PartTypeBottomAccessory:
	case PartTypeFrontAccessory:
	case PartTypeLoadingSightseeing:
	case PartTypeCarShell:
	case PartTypeHallShaft:
	case PartTypeImportedCarDoor:
	case PartTypeImportedHallDoor:
	case PartTypeICCard:
	case PartTypeAuxICCard:
		return MPT_COMMON_PART;
	case PartTypeHall:
		return MPT_HALL_ROOM;
	case PartTypeEscaStep:
	case PartTypeEscaStepLighting:
	case PartTypeEscaHandrail:
	case PartTypeEscaHandrailGuid:
	case PartTypeEscaHandrailEnter:
	case PartTypeEscaAcessOver:
	case PartTypeEscaAcessOverFlag:
	case PartTypeEscaBalustrade:
	case PartTypeEscaComb:
	case PartTypeEscaCombLighting:
	case PartTypeEscaDecking:
	case PartTypeEscaSkirt:
	case PartTypeEscaSkirtLighting:
	case PartTypeEscaSkirtBrush:
	case PartTypeEscaTrafficLight:
	case PartTypeEscaSideCladding:
	case PartTypeEscaSideCladdingLighting:
	case PartTypeEscaPiece:
	case PartTypeEscaStepFloorNumber:
	case PartTypeEscaSideCladdingPit:
	case PartTypeEscaOtherParts:
	case PartTypeEscaAcessOverExpendType:
	case PartTypeEscaCenterBrace:
	case PartTypeEscaScenes:
	case PartTypeEscaScenesType:
	case PartTypeEscaPhotoelectric:
	case PartTypeEscaScenesArrangement:
	case PartTypeEscaDeckingCtrlBox:
	case PartTypeEscaTrussLighting:
	case HoistWayConfig:
	case TractionMachine:
	case ControlCabinet:
	case ImageGroup_HoistWay:
	case ImageGroup_Car:
	case ImageGroup_Hall:
		return MPT_ESCALATOR;
	}

	return MPT_UNKNOWN;
}

bool PartTypeManager::IsMaterialPart(int part_type)
{
	switch (part_type)
	{
	
	case PartTypeFrontWall:
	case PartTypeCopWall:
	case PartTypeBottom:
	case PartTypeDoorHeader:
	case PartTypeEntranceColumn:
	case PartTypeFrontWallSkirting:
	case PartTypeCarDoor:
	case PartTypeCarDoorTrim:
	case PartTypeTrim:
	case PartTypeHallBottom:
	case PartTypeHallWall:
	//case PartTypeJamb:
	case PartTypeHallDoor:
	//case PartTypeSkirting:
	case PartTypeHallDoorTrim:
		return true;
		break;
	default:
		return false;
		break;
	}
}

}