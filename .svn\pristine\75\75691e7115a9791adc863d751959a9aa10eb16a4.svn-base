#include "stdafx.h"
#include "VrHallFloorVisitor.h"
#include "VrGlobalInfo.h"
#include "material_channel.h"
#include "common_part.h"

namespace decoration_vr_interface
{
	VrHallFloorVisitor::VrHallFloorVisitor()
	{
		part_type_ = PartTypeHallBottom;

		available_parttypeid_list_.push_back(PartTypeHallBottom);

	}

	VrHallFloorVisitor::~VrHallFloorVisitor()
	{
	}

	void VrHallFloorVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto arr_name = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_)->material_array_;
		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(scene, arr_name.c_str());
		}
		else
		{
			material_->init(scene, arr_name.c_str());
		}
	}

	bool VrHallFloorVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (!vr_change)
		{
			return false;
		}

		CommonPart* part = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type_));
		if (!part)
		{
			return true;
		}

		bool is_reflect = part->GetIsHasReflect();

		rse::vector<MaterialInfo> material_infos;
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		auto edit_parts = part->GetEditPartsPtr();
		for (int i = 0, c = edit_parts->size(); i < c; ++i)
		{
			int id = edit_parts->at(i);
			material_infos.push_back(MaterialInfo(part->GetEditPartMaterial(id), id, type_info->vr_type_));
		}
		auto res = VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), type_info->vr_type_, material_infos, is_reflect);
		return res >= 0;
	}

	void VrHallFloorVisitor::PrintData(const tchar* file_name)
	{
		material_->PrintData(file_name);
	}

}