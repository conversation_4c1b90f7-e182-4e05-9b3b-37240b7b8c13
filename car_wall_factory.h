#ifndef _CARWALL_FACTORY_H_
#define _CARWALL_FACTORY_H_

#include "IElevatorPartFactory.h"

#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{
	struct CarWallElem;
	class CarWallFactory : public IElevatorPartFactory
	{
	public:
		CarWallFactory(int type);
		virtual ~CarWallFactory();

		void SetPartType(int type);
		virtual int GetPartType() override;

		virtual std::shared_ptr<IElevatorPart> CreatePart(FactoryArgs* param) override;
	
	protected:
		IElevatorPartPtr GetWallFromJsonConfig(FactoryArgs* args);
		IElevatorPartPtr GetWallFromJsonObject(FactoryArgs* args);

		void FillWallData(IElevatorPart* wall, const svr_data::SvrWallData& info, int car_size_id);
		void ConverterCarWallElem(CarWallElem* elem, const svr_data::SvrWallElement& raw_elem);
	private:
		int part_type_;
	};
}


#endif//_CARWALL_FACTORY_H_