#include "stdafx.h"
#include "lru_cache.h"

#include <vector>
#include <string>
#include <fstream>

namespace util
{
	lru_cache::lru_cache()
		:capacity_(1024*1024*500), used_(0)
	{
	}


	lru_cache::~lru_cache()
	{
	}

	void lru_cache::set_capacity(int32_t capacity)
	{
		capacity_ = capacity;
	}

	void lru_cache::set_config_file(const rse::string& file)
	{
		config_file_ = file;
	}

	bool lru_cache::load()
	{
		clear();

		std::ifstream in(config_file_.c_str());
		if (in)
		{
			rse::vector<rse::string>  words;
			rse::string line;
			while (getline(in, line))
			{
				words.push_back(line);
			}

            if(words.size() > 1)
            {
                for (size_t i = 0; i < words.size()-1; i+=2)
                {
                    put(words[i], atoi(words[i + 1].c_str()));
                }
            }
			in.close();
		}
		return true;
	}

	bool lru_cache::save()
	{
		std::ofstream out(config_file_.c_str());
		if (out)
		{
			for (auto it = lru_.rbegin(), ie = lru_.rend(); it != ie; ++it)
			{
				out << it->file_name << std::endl;
				out << it->file_size << std::endl;
			}

			out.close();
		}
		return true;
	}

	void lru_cache::clear()
	{
		hash_.clear();
		lru_.clear();
		used_ = 0;
	}

	int32_t lru_cache::get_capacity()
	{
		return capacity_;
	}

	int32_t lru_cache::get_used()
	{
		return used_;
	}

	int32_t lru_cache::get(const rse::string& key)
	{
		auto it = hash_.find(key);
		if (it != hash_.end())
		{
			int32_t value = it->second->file_size;
			lru_.splice(lru_.begin(), lru_, it->second);
			hash_[key] = lru_.begin();

			save();

			return value;
		}

		return -1;
	}

	void lru_cache::put(const rse::string& key, int32_t value)
	{
		auto it = hash_.find(key);
		if (it != hash_.end())
		{
			int32_t old_value = it->second->file_size;
			it->second->file_size = value;
			lru_.splice(lru_.begin(), lru_, it->second);
			hash_[key] = lru_.begin();

			used_ -= old_value;
		}
		else
		{
			lru_.push_front(cache_item(key, value));
			hash_[key] = lru_.begin();
		}

		used_ += value;
		while (used_ > capacity_)
		{
			const cache_item& item = lru_.back();
			used_ -= item.file_size;

			if (deleter != nullptr)
			{
				deleter(item.file_name, item.file_size);
			}

			hash_.erase(item.file_name);
			lru_.pop_back();
		}

		save();
	}

}
