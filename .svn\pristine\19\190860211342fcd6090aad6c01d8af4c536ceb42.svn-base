#include "stdafx.h"
#include "svr_material_channel.h"

#include "svr_json_helper.h"

namespace svr_data
{

	bool SvrMaterialChannel::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		IF_ERROR_RETURN(GetJsonValue(jv["partType"], PartType));
		IF_ERROR_RETURN(GetJsonValue(jv["channelId"], ChannelId));
		IF_ERROR_RETURN(GetJsonValue(jv["materialType"], MaterialType));
		IF_ERROR_RETURN(GetJsonValue(jv["channelColor"], ChannelColor));
		IF_ERROR_RETURN(GetJsonValue(jv["reflectColor"], ReflectColor));
		IF_ERROR_RETURN(GetJsonValue(jv["ambientColor"], AmbientColor));
		IF_ERROR_RETURN(GetJsonValue(jv["blendMode"], BlendMode));
		IF_ERROR_RETURN(GetJsonValue(jv["texturePath"], TexturePath));
		IF_ERROR_RETURN(GetJsonValue(jv["texMipLevel"], TexMipLevel));
		IF_ERROR_RETURN(GetJsonValue(jv["texSpecialMark"], TexSpecialMark));
		IF_ERROR_RETURN(GetJsonValue(jv["texMapMode"], TexMapMode));
		IF_ERROR_RETURN(GetJsonValue(jv["texUOffset"], TexUOffset));
		IF_ERROR_RETURN(GetJsonValue(jv["texVOffset"], TexVOffset));
		IF_ERROR_RETURN(GetJsonValue(jv["texUWrap"], TexUWrap));
		IF_ERROR_RETURN(GetJsonValue(jv["texVWrap"], TexVWrap));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapPath"], ChannelMapPath));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapMipLevel"], ChannelMapMipLevel));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapSpecialMask"], ChannelMapSpecialMask));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapMapMode"], ChannelMapMapMode));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapUOffset"], ChannelMapUOffset));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapVOffset"], ChannelMapVOffset));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapUWrap"], ChannelMapUWrap));
		IF_ERROR_RETURN(GetJsonValue(jv["channelMapVWrap"], ChannelMapVWrap));
		IF_ERROR_RETURN(GetJsonValue(jv["materialMark"], MaterialMark));

		return true;
	}

}
