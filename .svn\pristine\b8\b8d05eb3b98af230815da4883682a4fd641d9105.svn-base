#include "stdafx.h"
#include "VrGlobalInfo.h"
#include "esca_elevator_part.h"
#include "material_channel.h"

#include "EscaHandrailVisitor.h"

namespace decoration_vr_interface
{

	VrEscaHandrailVisitor::VrEscaHandrailVisitor()
	{
		part_type_ = PartTypeEscaHandrail;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrEscaHandrailVisitor::~VrEscaHandrailVisitor()
	{

	}

	void VrEscaHandrailVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<esca::Sel_Esca_Handrail>(scene, type_info->model_array_.c_str());
		}
		else
		{
			model_->init(scene, type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(scene, type_info->material_array_.c_str());
		}
		else
		{
			material_->init(scene, type_info->material_array_.c_str());
		}
	}

	bool VrEscaHandrailVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (!vr_change)
		{
			return false;
		}

		auto part = static_cast<EscalatorPart*>(vr_change->config_->GetPart(part_type_));
		if (!part)
		{
			return false;
		} 

		if (model_->GetRowCount() < 1)
		{
			model_->AddRow();
		}

		//2018
		/*auto visitor = db_visitor::DBVisitorMgr::Instance()->GetVisitor();
		auto material_set = visitor->LoadEscaMaterialBasicInfo(part->GetPartMaterial());
		if (!material_set.IsEof())
		{
			auto source_material = material_set.GetIntField("UC_SOURCE_MATERIAL");
			auto handrail_type_pos_property = RSE_MAKE_SHARED<Variant>();
			handrail_type_pos_property->i_ = source_material;
			vr_change->config_->SetExtendProperty(EPK_EscaHandrailTypeId, handrail_type_pos_property);
		}*/

		auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		auto model_file = part->LoadModel();
		auto model_path = Util::CombinePath(dir, model_file);
		AddFileToModelCache(part->GetPartType(), part->GetPartId(), model_file, model_path);
		model_->Setstr_Path(0, model_path.c_str());

		if (part->GetPartMaterial() > 0)
		{
			auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

			rse::vector<MaterialInfo> material_infos;
			material_infos.push_back(MaterialInfo(part->GetPartMaterial(), 1, type_info->vr_type_));

			VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), type_info->vr_type_, material_infos, false);
		}

		return true;
	}

	void VrEscaHandrailVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
		material_->PrintData(file_name);
	}

}