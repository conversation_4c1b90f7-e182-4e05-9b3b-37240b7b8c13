#ifndef _SKIRTING_PART_H_
#define _SKIRTING_PART_H_

#include "IElevatorPart.h"

namespace decoration_vr_interface
{
	class SkirtingPart :public IElevatorPart
	{
		friend class SkirtingPartFactory;
	public:
		SkirtingPart();
		virtual ~SkirtingPart();

		virtual int GetPartType() override;
		virtual int64 GetPartId() override;

		virtual tstring GetPartName() override;
		virtual void SetPartName(const tstring &new_name) override;

		virtual tstring LoadModel() override;

		virtual bool IsValidPart() override;

		void SetSkirtingMaterial(int64 mat);
		void SetSkirtingMaterial(int type, int64 mat);
		int64 GetSkirtingMaterial(int type);
		
		void CollectSkirtingMaterial();

		virtual bool GetIsHasReflect() override;
		virtual void SetIsHasReflect(const bool &is_reflect) override;

	protected:
		int part_type_;
		int64 part_id_;
		tstring part_name_;

		rse::map<int, int64> skirting_mat_;

		bool is_reflect_;
	};

	typedef std::shared_ptr<SkirtingPart> SkirtingPartPtr;
}

#endif//_SKIRTING_PART_H_