#ifndef _VR_FRONTWALL_VISITOR_H_
#define _VR_FRONTWALL_VISITOR_H_
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Accessory;
	class MaterialChannel;
	class VrFrontWallVisitor :public BaseVisitor
	{
	public:
		VrFrontWallVisitor();
		virtual ~VrFrontWallVisitor();

		DEFINE_CREATE_FUN(VrFrontWallVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void NotifyVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	private:
		std::shared_ptr<MaterialChannel> material_;
		std::shared_ptr<MaterialChannel> material_back_oppo_;
	private:
		std::shared_ptr<MaterialChannel> import_material_;
		std::shared_ptr<Sel_Accessory> import_model_;

		bool ImportPartUpdate(VrChangeArg* vr_change);
	};
}
#endif//_VR_FRONTWALL_VISITOR_H_

