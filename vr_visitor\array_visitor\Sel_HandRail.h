//
//  Sel_HandRail.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_HandRail : public DGBaseVisitor
{
public:
	Sel_HandRail(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	int GetPosition(int row);
	void SetPosition(int row, int val);

	int Getleft(int row);
	void Setleft(int row, int val);

	int Getright(int row);
	void Setright(int row, int val);

	int Getback(int row);
	void Setback(int row, int val);

};
}