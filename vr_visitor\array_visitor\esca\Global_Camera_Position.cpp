#include "stdafx.h"
#include "Global_Camera_Position.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Global_Camera_Position::GetNumber(int row)
		{
			return visitor_->GetElementInt(row, 0);
		}

		void Global_Camera_Position::Set<PERSON>umber(int row, int val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Global_Camera_Position::GetAspectRatio_X(int row)
		{
			return visitor_->GetElementInt(row, 4);
		}

		void Global_Camera_Position::SetAspectRatio_X(int row, int val)
		{
			visitor_->SetElementValue(row, 4, val);
		}

		int Global_Camera_Position::GetAspectRatio_Y(int row)
		{
			return visitor_->GetElementInt(row, 5);
		}

		void Global_Camera_Position::SetAspectRatio_Y(int row, int val)
		{
			visitor_->SetElementValue(row, 5, val);
		}
	}

}