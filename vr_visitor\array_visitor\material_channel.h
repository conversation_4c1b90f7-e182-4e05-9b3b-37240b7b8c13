//
//  Material_Cop.h
//  VrVisitor
//
//  Created by vrprg on 10:25:03.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class MaterialChannel : public DGBaseVisitor
{
public:
	MaterialChannel(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}

	int GetPartType(int row);
	void SetPartType(int row, int val);

	int GetPartId(int row);
	void SetPartId(int row, int val);

	int GetChannelId(int row);
	void SetChannelId(int row, int val);

	int GetMaterialType(int row);
	void SetMaterialType(int row, int val);

	rse::string GetChannelColor(int row);
	void SetChannelColor(int row, const tchar* val);

	rse::string GetReflectColor(int row);
	void SetReflectColor(int row, const tchar* val);

	rse::string GetAmbientColor(int row);
	void SetAmbientColor(int row, const tchar* val);

	int GetBlendMode(int row);
	void SetBlendMode(int row, int val);

	rse::string GetTexture(int row);
	void SetTexture(int row, const tchar* val);

	int GetTexMipLevel(int row);
	void SetTexMipLevel(int row, int val);

	int GetTexSpecial(int row);
	void SetTexSpecial(int row, int val);

	int GetTexMapMode(int row);
	void SetTexMapMode(int row, int val);

	float GetTexXOffset(int row);
	void SetTexXOffset(int row, float val);

	float GetTexYOffset(int row);
	void SetTexYOffset(int row, float val);

	float GetTexXCount(int row);
	void SetTexXCount(int row, float val);

	float GetTexYCount(int row);
	void SetTexYCount(int row, float val);

	rse::string GetChannelMap(int row);
	void SetChannelMap(int row, const tchar* val);

	int GetMaskMipLevel(int row);
	void SetMaskMipLevel(int row, int val);

	int GetMaskSpecial(int row);
	void SetMaskSpecial(int row, int val);

	int GetMaskMapMode(int row);
	void SetMaskMapMode(int row, int val);

	float GetMaskXOffset(int row);
	void SetMaskXOffset(int row, float val);

	float GetMaskYOffset(int row);
	void SetMaskYOffset(int row, float val);

	float GetMaskXCount(int row);
	void SetMaskXCount(int row, float val);

	float GetMaskYCount(int row);
	void SetMaskYCount(int row, float val);

	int GetMaterialMark(int row);
	void SetMaterialMark(int row, int val);

};

typedef decoration_vr_interface::MaterialChannel DGMaterialChannel;
}