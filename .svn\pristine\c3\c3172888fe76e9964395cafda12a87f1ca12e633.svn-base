//
//  Sel_Cop.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Cop : public DGBaseVisitor
{
public:
	Sel_Cop(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	rse::string GetAN_Path(int row);
	void SetAN_Path(int row, const tchar* val);

	int GetPos(int row);
	void SetPos(int row, int val);

	float GetPos_X(int row);
	void SetPos_X(int row, float val);

	float GetPos_Y(int row);
	void SetPos_Y(int row, float val);

	int GetCopForm(int row);
	void SetCopForm(int row, int val);

	int GetCopType(int row);
	void SetCopType(int row, int val);
};

class MaterialCopButton : public DGBaseVisitor
{
public:
	MaterialCopButton(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}

	rse::string GetPanelTexture(int row);
	void SetPanelTexture(int row, const tchar* val);
};

}