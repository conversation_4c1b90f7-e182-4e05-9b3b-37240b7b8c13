#pragma once

#include <string>

namespace decoration_vr_interface
{
	class DGBaseVisitor
	{
	public:
		DGBaseVisitor(IDGSceneEx* scene, const tchar* arr_name);
		~DGBaseVisitor();
		void init(IDGSceneEx* scene, const tchar* arr_name);
		int AddRow();
		int GetRowCount();
		void DeleteRow(int row);
		void Clear();
		void PrintData(const tchar* file_name);

	protected:
		tstring arr_name_;
		IDGArrayVisitor* visitor_;
	};
}



