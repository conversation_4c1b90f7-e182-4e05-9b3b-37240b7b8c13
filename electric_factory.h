#ifndef _ELECTRIC_FACTORY_H_
#define _ELECTRIC_FACTORY_H_

#include "IElevatorPartFactory.h"

#include "parse_json/svr_config.h"


namespace decoration_vr_interface
{
	class ElectricPartFactory :public IElevatorPartFactory
	{
	public:
		ElectricPartFactory(int type);
		virtual ~ElectricPartFactory();

		void SetPartType(int type);
		virtual int GetPartType() override;

		virtual IElevatorPartPtr CreatePart(FactoryArgs* param) override;

	protected:
		IElevatorPartPtr GetPartFromJsonConfig(FactoryArgs* args);
		IElevatorPartPtr GetPartFromJsonObject(FactoryArgs* args);

		void SetPartPosition(IElevatorPart* part, FactoryArgs* args);

		void FillPartBasicInfo(IElevatorPart* part, const svr_data::SvrPartBasicInfo& info,
			const rse::vector<svr_data::SvrCommonPart>& parts);
		void FillPartMaterial(IElevatorPart* part, const  rse::vector<svr_data::SvrPartMaterial>& mats);
	private:
		int part_type_;
	};
}

#endif//_ELECTRIC_FACTORY_H_
