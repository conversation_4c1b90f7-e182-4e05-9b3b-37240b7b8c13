//
//  Sel_HI.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Sel_HI.h"


namespace decoration_vr_interface
{

bool Sel_HI::GetIsHave(int row)
{
    return visitor_->GetElementBool(row, 0);
}

void Sel_HI::SetIsHave(int row, bool val)
{
    visitor_->SetElementValue(row, 0, val);
}

rse::string Sel_HI::GetPath(int row)
{
    const char* val = visitor_->GetElementString(row, 1);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void Sel_HI::SetPath(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 1, val);
}

int Sel_HI::GetLocation(int row)
{
    return visitor_->GetElementInt(row, 2);
}

void Sel_HI::SetLocation(int row, int val)
{
    visitor_->SetElementValue(row, 2, val);
}

}