//
//  Sel_Bottom_Accessory.h
//  VrVisitor
//
//  Created by <PERSON> on 2019-01-12 13:04:05.//. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Bottom_Accessory : public DGBaseVisitor
{
public:
	Sel_Bottom_Accessory(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name){}

	bool GetIsHave(int row);
	void SetIsHave(int row, bool val);

	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

	bool GetIsBoth(int row);
	void SetIsBoth(int row, bool val);
};
}