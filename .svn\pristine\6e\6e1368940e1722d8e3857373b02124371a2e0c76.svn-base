#include "stdafx.h"
#include "vr_visitor_factory.h"

namespace decoration_vr_interface
{
	VrVisitorFactory::VrVisitorFactory()
	{
	}


	VrVisitorFactory::~VrVisitorFactory()
	{
	}

	void VrVisitorFactory::Register(const tchar* class_name, CreateVisitorFunction call_back)
	{
		factory_map_[class_name] = call_back;
	}

	decoration_vr_interface::IVrVisitorPtr VrVisitorFactory::CreateVrVisitor(const tchar* class_name)
	{
		auto found_iter = factory_map_.find(class_name);
		if (found_iter == factory_map_.end())
		{
			DECORATION_ASSERT(false);
			return nullptr;
		}
		else
		{
			CreateVisitorFunction create_fun = found_iter->second;
			return create_fun();
		}
	}

}