#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_CopLcd;

class VrCopDisplayVisitor : public BaseVisitor
{
public:
	VrCopDisplayVisitor();
	~VrCopDisplayVisitor();

	DEFINE_CREATE_FUN(VrCopDisplayVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_CopLcd> model_;
};
}
