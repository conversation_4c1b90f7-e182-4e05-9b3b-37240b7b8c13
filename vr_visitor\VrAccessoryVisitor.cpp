﻿#include "stdafx.h"
#include "VrAccessoryVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Accessory.h"
#include "material_channel.h"
#include "common_part.h"
#include "car_top.h"

namespace decoration_vr_interface
{
	void VrAccessoryVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		auto mat_name = type_info->material_array_;
		auto model_name = type_info->model_array_;

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Accessory>(scene, model_name.c_str());
		} 
		else
		{
			model_->init(scene, model_name.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<DGMaterialChannel>(scene, mat_name.c_str());
		}
		else
		{
			material_->init(scene, mat_name.c_str());
		}
	}

	bool VrAccessoryVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		auto accessory = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type_));

		if (accessory)
		{
			model_->SetSelectable(row_, true);

			auto g_vr_info = VrGlobalInfo::Instance();
			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
			auto model_file = accessory->LoadModel();
			auto path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(accessory->GetPartType(), accessory->GetPartId(), model_file, path);
			model_->SetPath(row_, path.c_str());

			auto elev_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig();
			auto elev_size = elev_config->GetElevatorSize();
			auto top = static_cast<CarTop*>(elev_config->GetCarConfig()->GetPart(PartTypeTop));

			//外挂件驱动，需要传入宽度、高度、门宽参数
			model_->SetWidth(row_, elev_size->width_);
			model_->SetDoorWidth(row_, elev_size->door_width_);
			model_->SetHeight(row_, elev_size->height_ + top->GetThickness());

			GlobalInfoDataCommon::Instance()->LogErrorLn("accessory param width_ = %ld door_width_ = %ld  height_ = ",
				elev_size->width_, elev_size->door_width_, elev_size->height_ + top->GetThickness());


			GlobalInfoDataCommon::Instance()->LogErrorLn("write access param width_ = %ld door_width_ = %ld  height_ = ",
				model_->GetWidth(row_), model_->GetDoorWidth(row_), model_->GetHeight(row_));
			
			rse::vector<MaterialInfo> material_infos;
			auto epm = accessory->GetEditPartsPtr();
			for (auto it=epm->begin(), ie=epm->end(); it!=ie; ++it)
			{
				auto x = accessory->GetEditPartMaterial(*it);
				material_infos.push_back(MaterialInfo(x, *it, 0));
			}
			bool is_reflect = accessory->GetIsHasReflect();
			auto res = VrGlobalInfo::Instance()->WriteMaterialChannelInfo(material_.get(), part_type_, material_infos, is_reflect);
			return res >= 0;
		}
		else
		{
			model_->SetSelectable(row_, false);
		}

		return true;
	}

	void VrAccessoryVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
		material_->PrintData(file_name);
	}

	VrLeftAccessoryVisitor::VrLeftAccessoryVisitor()
	{
		part_type_ = PartTypeLeftAccessory;
		row_ = 1;

		available_parttypeid_list_.push_back(part_type_);
	}

	VrLeftAccessoryVisitor::~VrLeftAccessoryVisitor()
	{

	}

	VrBackAccessoryVisitor::VrBackAccessoryVisitor()
	{
		part_type_ = PartTypeBackAccessory;
		row_ = 0;

		available_parttypeid_list_.push_back(part_type_);
	}

	VrBackAccessoryVisitor::~VrBackAccessoryVisitor()
	{

	}

	VrRightAccessoryVisitor::VrRightAccessoryVisitor()
	{
		part_type_ = PartTypeRightAccessory;
		row_ = 2;

		available_parttypeid_list_.push_back(part_type_);
	}

	VrRightAccessoryVisitor::~VrRightAccessoryVisitor()
	{

	}


	//////////////////////////////////////////////////////////////////////////////////////////////
	///
	VrBottomAccessoryVisitor::VrBottomAccessoryVisitor()
	{
		part_type_ = PartTypeBottomAccessory;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrBottomAccessoryVisitor::~VrBottomAccessoryVisitor()
	{
	}
	void VrBottomAccessoryVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		auto mat_name = type_info->material_array_;
		auto model_name = type_info->model_array_;

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Hung_Part>(scene, model_name.c_str());
		}
		else
		{
			model_->init(scene, model_name.c_str());
		}		
	}

	bool VrBottomAccessoryVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		for (int row = model_->GetRowCount() - 1; row >= 0; --row)
		{
			if (model_->GetPartType(row) == type_info->vr_type_)
			{
				model_->DeleteRow(row);
			}
		}

		auto accessory = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type_));
		if (accessory)
		{
			int row = model_->AddRow();

			model_->SetSelectable(row, true);

			auto g_vr_info = VrGlobalInfo::Instance();
			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
			auto model_file = accessory->LoadModel();
			auto path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(accessory->GetPartType(), accessory->GetPartId(), model_file, path);
			model_->SetPath(row, path.c_str());

			model_->SetPartType(row, type_info->vr_type_);
		}

		return true;
	}
	void VrBottomAccessoryVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
	}

	//
	VrICCardVisitor::VrICCardVisitor()
	{
		part_type_ = PartTypeICCard;
		available_parttypeid_list_.push_back(part_type_);
		available_parttypeid_list_.push_back(PartTypeAuxICCard);
	}

	VrICCardVisitor::~VrICCardVisitor()
	{

	}

	void VrICCardVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		auto mat_name = type_info->material_array_;
		auto model_name = type_info->model_array_;

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Hung_Part>(scene, model_name.c_str());
		}
		else
		{
			model_->init(scene, model_name.c_str());
		}
	}

	bool VrICCardVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);
		for (int row = model_->GetRowCount() - 1; row >= 0; --row)
		{
			auto type = model_->GetPartType(row);
			if(type == PartTypeICCard || type == PartTypeAuxICCard)
			{
				model_->DeleteRow(row);
			}
		}
		for (auto part_type : available_parttypeid_list_)
		{
			auto part = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type));
			if (part && part->GetPartId() > 0 && part->GetSetupPos() > 0)
			{
				int row = model_->AddRow();
				model_->SetSelectable(row, true);

				auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
				auto model_file = part->LoadModel();
				auto path = Util::CombinePath(dir, model_file);
				AddFileToModelCache(part_type, part->GetPartId(), model_file, path);
				model_->SetPath(row, path.c_str());
				model_->SetPartType(row, part_type);
				model_->SetLocation(row, ConstValueMap::ToGeneralVrCopSetupPos(part->GetSetupPos()));
				model_->SetOffsetX(row, part->GetPosX());
				model_->SetOffsetY(row, part->GetPosY());
				int extra1 = GlobalInfoDataCommon::Instance()->GetGlobalData(part_type);
				model_->SetExtra1(row, extra1);
			}
		}
		auto car_depth = vr_change->config_->GetElevatorConfig()->GetElevatorSize()->depth_;
		//
		//if (GlobalInfoDataCommon::Instance()->IsOppositeDoor())
		//{
		//	for (auto part_type : available_parttypeid_list_)
		//	{
		//		auto part = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type));
		//		if (part && part->GetPartId() > 0 && part->GetSetupPos() > 0)
		//		{
		//			int row = model_->AddRow();

		//			model_->SetSelectable(row, true);
		//			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		//			auto model_file = part->LoadModel();
		//			auto path = Util::CombinePath(dir, model_file);
		//			AddFileToModelCache(part_type, part->GetPartId(), model_file, path);
		//			model_->SetPath(row, path.c_str());

		//			model_->SetPartType(row, part_type);

		//			int setup_pos = part->GetSetupPos();
		//			float pos_x = part->GetPosX();
		//			float pos_y = part->GetPosY();
		//			switch (setup_pos)
		//			{
		//			case kCopWall:
		//			case kCopWallLeft:
		//				setup_pos = kBackWall;
		//				break;
		//			case kFrontWallRight:
		//			case kFrontWall:
		//				setup_pos = kBackWallLeft;
		//				break;
		//			case kLeftWall:
		//			case kRightWall:
		//				pos_x = car_depth - pos_x;
		//				break;
		//			case kBackWall:
		//				setup_pos = kCopWall;
		//				break;
		//			case kBackWallLeft:
		//				setup_pos = kFrontWall;
		//				break;
		//			case -1:
		//				setup_pos = kCopWall;
		//				break;
		//			}

		//			model_->SetLocation(row, ConstValueMap::ToGeneralVrCopSetupPos(setup_pos));
		//			model_->SetOffsetX(row, pos_x);
		//			model_->SetOffsetY(row, pos_y);
		//			int extra1 = GlobalInfoDataCommon::Instance()->GetGlobalData(part_type);
		//			model_->SetExtra1(row, extra1);
		//		}
		//	}
		//}
		
		return true;
	}

	void VrICCardVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
	}
}