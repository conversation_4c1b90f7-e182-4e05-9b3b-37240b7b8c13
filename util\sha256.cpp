#include "stdafx.h"
#include "sha256.h"

#include <string.h>

namespace util
{
	static const uint32_t K256[64] =
	{
		0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5,
		0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
		0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
		0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
		0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,
		0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
		0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7,
		0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
		0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
		0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
		0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3,
		0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
		0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5,
		0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
		0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
		0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
	};

#define rol(value, bits) (((value) << (bits)) | ((value) >> (32 - (bits))))

#define Ch(x,y,z)   (((x) & ((y) ^ (z))) ^ (z))
#define Maj(x,y,z)  ((((x) | (y)) & (z)) | ((x) & (y)))

#define Sigma0_256(x)   (rol((x), 30) ^ rol((x), 19) ^ rol((x), 10))
#define Sigma1_256(x)   (rol((x), 26) ^ rol((x), 21) ^ rol((x),  7))
#define sigma0_256(x)   (rol((x), 25) ^ rol((x), 14) ^ ((x) >> 3))
#define sigma1_256(x)   (rol((x), 15) ^ rol((x), 13) ^ ((x) >> 10))

#ifndef RB32
#   define RB32(x)                                \
    (((uint32_t)((const uint8_t*)(x))[0] << 24) |    \
               (((const uint8_t*)(x))[1] << 16) |    \
               (((const uint8_t*)(x))[2] <<  8) |    \
                ((const uint8_t*)(x))[3])
#endif
#ifndef WB32
#   define WB32(p, val) do {                 \
        uint32_t d = val;                       \
        ((uint8_t*)(p))[3] = (d);               \
        ((uint8_t*)(p))[2] = (d)>>8;            \
        ((uint8_t*)(p))[1] = (d)>>16;           \
        ((uint8_t*)(p))[0] = (d)>>24;           \
    } while(0)
#endif

#define blk0(i) (block[i] = RB32(buffer + 4 * (i)))
#define blk(i)  (block[i] = block[i - 16] + sigma0_256(block[i - 15]) + \
                            sigma1_256(block[i - 2]) + block[i - 7])

#define ROUND256(a,b,c,d,e,f,g,h)   \
    T1 += (h) + Sigma1_256(e) + Ch((e), (f), (g)) + K256[i]; \
    (d) += T1; \
    (h) = T1 + Sigma0_256(a) + Maj((a), (b), (c)); \
    i++

#define ROUND256_0_TO_15(a,b,c,d,e,f,g,h)   \
    T1 = blk0(i); \
    ROUND256(a,b,c,d,e,f,g,h)

#define ROUND256_16_TO_63(a,b,c,d,e,f,g,h)   \
    T1 = blk(i); \
    ROUND256(a,b,c,d,e,f,g,h)

	static void sha256_transform(uint32_t *state, const uint8_t buffer[64])
	{
		unsigned int i, a, b, c, d, e, f, g, h;
		uint32_t block[64];
		uint32_t T1;

		a = state[0];
		b = state[1];
		c = state[2];
		d = state[3];
		e = state[4];
		f = state[5];
		g = state[6];
		h = state[7];
		for (i = 0; i < 16;)
		{
			ROUND256_0_TO_15(a, b, c, d, e, f, g, h);
			ROUND256_0_TO_15(h, a, b, c, d, e, f, g);
			ROUND256_0_TO_15(g, h, a, b, c, d, e, f);
			ROUND256_0_TO_15(f, g, h, a, b, c, d, e);
			ROUND256_0_TO_15(e, f, g, h, a, b, c, d);
			ROUND256_0_TO_15(d, e, f, g, h, a, b, c);
			ROUND256_0_TO_15(c, d, e, f, g, h, a, b);
			ROUND256_0_TO_15(b, c, d, e, f, g, h, a);
		}

		for (; i < 64;)
		{
			ROUND256_16_TO_63(a, b, c, d, e, f, g, h);
			ROUND256_16_TO_63(h, a, b, c, d, e, f, g);
			ROUND256_16_TO_63(g, h, a, b, c, d, e, f);
			ROUND256_16_TO_63(f, g, h, a, b, c, d, e);
			ROUND256_16_TO_63(e, f, g, h, a, b, c, d);
			ROUND256_16_TO_63(d, e, f, g, h, a, b, c);
			ROUND256_16_TO_63(c, d, e, f, g, h, a, b);
			ROUND256_16_TO_63(b, c, d, e, f, g, h, a);
		}
		state[0] += a;
		state[1] += b;
		state[2] += c;
		state[3] += d;
		state[4] += e;
		state[5] += f;
		state[6] += g;
		state[7] += h;
	}


	int sha256_init(sha256_ctx *ctx)
	{
		ctx->digest_len = 8;
		ctx->state[0] = 0x6A09E667;
		ctx->state[1] = 0xBB67AE85;
		ctx->state[2] = 0x3C6EF372;
		ctx->state[3] = 0xA54FF53A;
		ctx->state[4] = 0x510E527F;
		ctx->state[5] = 0x9B05688C;
		ctx->state[6] = 0x1F83D9AB;
		ctx->state[7] = 0x5BE0CD19;
		ctx->count = 0;
		return 0;
	}

	void sha256_update(sha256_ctx* ctx, const uint8_t* data, unsigned int len)
	{
		unsigned int i, j;

		j = ctx->count & 63;
		ctx->count += len;
		if ((j + len) > 63)
		{
			memcpy(&ctx->buffer[j], data, (i = 64 - j));
			sha256_transform(ctx->state, ctx->buffer);
			for (; i + 63 < len; i += 64)
			{
				sha256_transform(ctx->state, &data[i]);
			}
			j = 0;
		}
		else
		{
			i = 0;
		}
		memcpy(&ctx->buffer[j], &data[i], len - i);
	}

#define SWAP16(x) (((x) << 8 & 0xff00)  | ((x) >> 8 & 0x00ff))
#define SWAP32(x) (SWAP16(x) << 16 | SWAP16((x) >> 16))

	static inline uint32_t swap32(uint32_t x)
	{
		return SWAP32(x);
	}

	static inline uint64_t swap64(uint64_t x)
	{
		return (uint64_t)swap32(x) << 32 | swap32(x >> 32);
	}

	void sha256_final(sha256_ctx* ctx, uint8_t *digest)
	{
		int i;
		uint64_t finalcount = swap64(ctx->count << 3);

		sha256_update(ctx, (const uint8_t *)"\200", 1);
		while ((ctx->count & 63) != 56)
			sha256_update(ctx, (const uint8_t *)"", 1);
		sha256_update(ctx, (uint8_t *)&finalcount, 8);
		for (i = 0; i < ctx->digest_len; i++)
			WB32(digest + i * 4, ctx->state[i]);
	}
}
