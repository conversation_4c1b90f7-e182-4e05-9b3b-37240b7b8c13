﻿#include "stdafx.h"
#include "VrCopVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Cop.h"
#include "material_channel.h"
#include "electric_part.h"
#include "decoration_vr_interface_lib.h"

namespace decoration_vr_interface
{

	VrCopVisitor::VrCopVisitor()
	{
        part_type_ = PartTypeCop;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		types.push_back(PartTypeAuxCop);
		types.push_back(PartTypeHDCop);
		types.push_back(PartTypeAuxHDCop);
		InitializeAvailablePartTypes(types);

	} 

	VrCopVisitor::~VrCopVisitor()
	{
	}


	void VrCopVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Cop>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

		if (!material_)
		{
			material_ = RSE_MAKE_SHARED<MaterialChannel>(vr_glob->get_dg_scene(), part_type_info->material_array_.c_str());
		}

		if (!material_button_)
		{
			material_button_ = RSE_MAKE_SHARED<MaterialCopButton>(vr_glob->get_dg_scene(), TSTR("Material_CopButton"));
		}
	}

	bool VrCopVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		auto config = vr_change->config_;

		auto part = static_cast<ElectricPart*>(config->GetPart(part_type_));
		if (!part)
		{
			return false;
		}

		auto vr_global_info = VrGlobalInfo::Instance();

		//
		auto car_depth = config->GetElevatorConfig()->GetElevatorSize()->depth_;

		//设置按钮数量
		auto floor_count = config->GetElevatorConfig()->GetElevatorSetting()->floor_count_;
		vr_global_info->get_select_array_visitor()->SetCopButtonCount(0, floor_count);

		//收集cop
		auto type_mgr = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
		rse::vector<int> vr_type_list;
		rse::vector<IElevatorPart*> cop_list;

		cop_list.push_back(part);
		vr_type_list.push_back(type_mgr->GetTypeInfo(part_type_)->vr_type_);

		auto aux_cop = config->GetPart(PartTypeAuxCop);
		if (aux_cop)
		{
			cop_list.push_back(aux_cop);
			vr_type_list.push_back(type_mgr->GetTypeInfo(PartTypeAuxCop)->vr_type_);
		}

		auto hdcop = config->GetPart(PartTypeHDCop);
		if (hdcop)
		{
			cop_list.push_back(hdcop);
			vr_type_list.push_back(type_mgr->GetTypeInfo(PartTypeHDCop)->vr_type_);
		}
		auto auxhdcop = config->GetPart(PartTypeAuxHDCop);
		if (auxhdcop)
		{
			cop_list.push_back(auxhdcop);
			vr_type_list.push_back(type_mgr->GetTypeInfo(PartTypeAuxHDCop)->vr_type_);
		}

		model_->Clear();
		material_button_->Clear();
		auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		rse::vector<MaterialInfo> mat_info_list;
		rse::vector<bool> part_type_is_reflect;

		for (int cop_i = 0; cop_i < cop_list.size(); ++cop_i)
		{
			ElectricPart* cop = static_cast<ElectricPart*>(cop_list[cop_i]);
			auto row = model_->AddRow();
			material_button_->AddRow();
			
			auto model_file = cop->LoadModel();
			auto path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(cop->GetPartType(), cop->GetPartId(), model_file, path);
			model_->SetPath(row, path.c_str());

			auto btn_model_file = cop->GetButtonPath();
			auto btn_path = Util::CombinePath(dir, btn_model_file);
			AddFileToModelCache(cop->GetButtonType(), cop->GetButton(), btn_model_file, btn_path);	
			model_->SetAN_Path(row, btn_path.c_str());

			int setup_pos = cop->GetSetupOrientation();

			//Modify by syp675 2018-10-17
			//对于标准版 刷新整厢时Cop的安装位置始终为-1，导致整箱中没有Cop，所以提供默认值setup_pos为4100
			if (part_type_ == PartTypeId::PartTypeCop)
			{
				if (setup_pos == -1)
				{
					setup_pos = 4;
				}
			}
			//end

			model_->SetPos(row, ConstValueMap::ToGeneralVrCopSetupPos(setup_pos));
			model_->SetPos_X(row, cop->GetPosX());
			model_->SetPos_Y(row, cop->GetPosY());

			model_->SetCopType(row, vr_type_list[cop_i]);
			model_->SetCopForm(row, cop->GetPanelType());

			MaterialInfo mat_info(cop->GetPartMaterial(), 1, cop_i);
			mat_info_list.push_back(mat_info);

			if (cop->GetPartType() == PartTypeCop)
			{
				VrGlobalInfo::Instance()->get_select_array_visitor()->SetFrontWallType(0, cop->GetPanelType());
				if (GlobalInfoDataCommon::Instance()->IsOppositeDoor())
				{
					VrGlobalInfo::Instance()->get_select_array_visitor()->SetThroughBackWallType(0, cop->GetPanelType());
				}
				else
				{
					VrGlobalInfo::Instance()->get_select_array_visitor()->SetThroughBackWallType(0, 0);
				}
			}

			bool is_reflect = cop->GetIsHasReflect();
			part_type_is_reflect.push_back(is_reflect);

			if (GlobalInfoDataCommon::Instance()->cop_btn_mat_id_ > 0)
			{
				auto path = GetButtonPanelTexture(GlobalInfoDataCommon::Instance()->cop_btn_mat_id_);
				material_button_->SetPanelTexture(row, path.c_str());
			}
		}

		//
		//if (GlobalInfoDataCommon::Instance()->IsOppositeDoor())
		//{
		//	for (int cop_i = 0; cop_i < cop_list.size(); ++cop_i)
		//	{
		//		vr_type_list.push_back(vr_type_list[cop_i]);
		//		ElectricPart* cop = static_cast<ElectricPart*>(cop_list[cop_i]);
		//		auto row = model_->AddRow();
		//		material_button_->AddRow();

		//		auto model_file = cop->LoadModel();
		//		auto path = Util::CombinePath(dir, model_file);
		//		AddFileToModelCache(cop->GetPartType(), cop->GetPartId(), model_file, path);
		//		model_->SetPath(row, path.c_str());

		//		auto btn_model_file = cop->GetButtonPath();
		//		auto btn_path = Util::CombinePath(dir, btn_model_file);
		//		AddFileToModelCache(cop->GetButtonType(), cop->GetButton(), btn_model_file, btn_path);
		//		model_->SetAN_Path(row, btn_path.c_str());

		//		int setup_pos = cop->GetSetupOrientation();
		//		float pos_x = cop->GetPosX();
		//		float pos_y = cop->GetPosY();
		//		switch (setup_pos)
		//		{
		//			case kCopWall:
		//			case kCopWallLeft:
		//				setup_pos = kBackWall;
		//				break;
		//			case kFrontWallRight:
		//			case kFrontWall:
		//				setup_pos = kBackWallLeft;
		//				break;
		//			case kLeftWall:
		//			case kRightWall:
		//				pos_x = car_depth - pos_x;
		//				break;
		//			case kBackWall:
		//				setup_pos = kCopWall;
		//				break;
		//			case kBackWallLeft:
		//				setup_pos = kFrontWall;
		//				break;
		//			case -1:
		//				setup_pos = kCopWall;
		//				break;
		//		}

		//		model_->SetPos(row, ConstValueMap::ToGeneralVrCopSetupPos(setup_pos));
		//		model_->SetPos_X(row, pos_x);
		//		model_->SetPos_Y(row, pos_y);

		//		model_->SetCopType(row, vr_type_list[cop_i]);
		//		model_->SetCopForm(row, cop->GetPanelType());

		//		MaterialInfo mat_info(cop->GetPartMaterial(), 1, cop_i);
		//		mat_info_list.push_back(mat_info);

		//		bool is_reflect = cop->GetIsHasReflect();
		//		part_type_is_reflect.push_back(is_reflect);

		//		if (GlobalInfoDataCommon::Instance()->cop_btn_mat_id_ > 0)
		//		{
		//			auto path = GetButtonPanelTexture(GlobalInfoDataCommon::Instance()->cop_btn_mat_id_);
		//			material_button_->SetPanelTexture(row, path.c_str());
		//		}
		//	}
		//}

		//

		for (int material_i = 0; material_i < mat_info_list.size(); ++material_i)
		{
			rse::vector<MaterialInfo> infos;
			infos.push_back(mat_info_list[material_i]);
			//vr_glob->WriteMaterialChannelInfo(material_.get(), list_cop_type[material_i], infos);

			if (!material_i)
			{
				if (vr_global_info->WriteMaterialChannelInfo(material_.get(), vr_type_list[material_i], infos, part_type_is_reflect[material_i]) < 0)
					return false;
			}
			else
			{
				if (vr_global_info->AppendMaterialChannelInfo(material_.get(), vr_type_list[material_i], infos, part_type_is_reflect[material_i]) < 0)
					return false;
			}
		}

		return true;
	}

	void VrCopVisitor::PrintData(const tchar* file_name)
	{

		model_->PrintData(file_name);

		material_->PrintData(file_name);

	}

	tstring VrCopVisitor::GetButtonPanelTexture(int flag)
	{
		switch (flag)
		{
		//MT42
		case 200://HL01
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW.bmp");
		case 210://HL01-TD
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_TD.bmp");
			break;
		case 220://HL01-TG
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_TG.bmp");
		case 230://HL01-BR
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_BR.bmp");
		case 240://HL01-TR
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_TR.bmp");
		case 250://HL01-TC
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_TC.bmp");

		//AN180
		case 300://HL01
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y.bmp");
		case 310://HL01-TD
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y_TD.bmp");
			break;
		case 320://HL01-TG
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y_TG.bmp");
		case 330://HL01-BR
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y_BR.bmp");
		case 340://HL01-TR
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y_TR.bmp");
		case 350://HL01-TC
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y_TC.bmp");
		default:
			return TSTR("AppDefine/ButtonMaterial/copbutton_FW_Y.bmp");
		}
	}

}
    