#include "stdafx.h"
#include "Global_PartType_Info.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Global_PartType_Info::GetnPartType(int row)
		{
			return visitor_->GetElementInt(row, 0);
		}

		void Global_PartType_Info::SetnPartType(int row, int val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		rse::string Global_PartType_Info::Getparttypename(int row)
		{
			const char* val = visitor_->GetElementString(row, 1);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_PartType_Info::Setparttypename(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		rse::string Global_PartType_Info::GetstrMsgName(int row)
		{
			const char* val = visitor_->GetElementString(row, 2);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_PartType_Info::SetstrMsgName(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

		rse::string Global_PartType_Info::GetActivatScriptName(int row)
		{
			const char* val = visitor_->GetElementString(row, 3);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_PartType_Info::SetActivatScriptName(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 3, val);
		}

		rse::string Global_PartType_Info::Getdelscript_arrName(int row)
		{
			const char* val = visitor_->GetElementString(row, 4);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Global_PartType_Info::Setdelscript_arrName(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 4, val);
		}

		int Global_PartType_Info::GetCopyObjectMain(int row)
		{
			return visitor_->GetElementInt(row, 5);
		}

		void Global_PartType_Info::SetCopyObjectMain(int row, int val)
		{
			visitor_->SetElementValue(row, 5, val);
		}

		int Global_PartType_Info::GetCopyObjectMaterial(int row)
		{
			return visitor_->GetElementInt(row, 6);
		}

		void Global_PartType_Info::SetCopyObjectMaterial(int row, int val)
		{
			visitor_->SetElementValue(row, 6, val);
		}
	}

}