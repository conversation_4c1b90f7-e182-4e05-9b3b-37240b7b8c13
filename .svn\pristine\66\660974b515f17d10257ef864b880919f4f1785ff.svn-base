#pragma once
 
#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Sel_Esca_Side_Cladding_Lighting : public DGBaseVisitor
		{
		public:
			Sel_Esca_Side_Cladding_Lighting(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			bool Getbool_Select(int row);
			void Setbool_Select(int row, bool val);

			rse::string Getstr_Path(int row);
			void Setstr_Path(int row, const tchar* val);

		};
	}

}