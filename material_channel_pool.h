#pragma once

#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{
	class MaterialChannelPool
	{
	public:
		MaterialChannelPool();
		~MaterialChannelPool();

		//static MaterialChannelPool* Instance();

		bool GetMaterialChannel(rse::vector<svr_data::SvrMaterialChannel>& rt, int64 mat_id, int channel_type = 0);

		void AddMaterialInfo(int64 mat_id, const svr_data::SvrMaterialInfo& mat_info);
		void RemoveMaterialInfo(int64 mat_id);

		void Clear();
	private:
		rse::map<int64, svr_data::SvrMaterialInfo> svr_materials_;
	};
}

