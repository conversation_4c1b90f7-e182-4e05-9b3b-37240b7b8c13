#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_HLLcd;

class VrLanternDisplayVisitor : public BaseVisitor
{
public:
	VrLanternDisplayVisitor();
	~VrLanternDisplayVisitor();

	DEFINE_CREATE_FUN(VrLanternDisplayVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_HLLcd> model_;
};
}
