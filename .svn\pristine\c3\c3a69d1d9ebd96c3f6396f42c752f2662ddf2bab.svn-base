#include "stdafx.h"
#include "esca_elevator_part.h"
#include "GlobalInfoDataCommon.h"

namespace decoration_vr_interface
{

	tstring EscalatorPart::LoadModel()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentEscalatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(GetPartType(), GetPartId(), size_id);
	}

	/*EscaHandrailEnter::EscaHandrailEnter()
		:enter_type_(-1)
	{

	}*/

	EscaHandrailEnter::EscaHandrailEnter()
	{

	}

	EscaHandrailEnter::~EscaHandrailEnter()
	{

	}

	/*int EscaHandrailEnter::GetEnterType()
	{
		return enter_type_;
	}

	void EscaHandrailEnter::SetEnterType(int et)
	{
		enter_type_ = et;
	}*/

	/*EscaAcessOver::EscaAcessOver()
		:is_set_floor_number_(false), up_floor_number_(-1), down_floor_number_(-1)
	{

	}*/

	EscaAcessOver::EscaAcessOver()
	{

	}

	EscaAcessOver::~EscaAcessOver()
	{

	}

	/*bool EscaAcessOver::IsSetFloorNumber()
	{
		return is_set_floor_number_;
	}

	int EscaAcessOver::GetUpFloorNumber()
	{
		return up_floor_number_;
	}

	void EscaAcessOver::SetUpFloorNumber(int n)
	{
		up_floor_number_ = n;
		is_set_floor_number_ = true;
	}

	int EscaAcessOver::GetDownFloorNumber()
	{
		return down_floor_number_;
	}

	void EscaAcessOver::SetDownFloorNumber(int n)
	{
		down_floor_number_ = n;
		is_set_floor_number_ = true;
	}*/

	/*EscaBalustrade::EscaBalustrade()
		:piece_pattern_(-1), material_type_(-1)
	{

	}*/

	EscaBalustrade::EscaBalustrade()
	{

	}

	EscaBalustrade::~EscaBalustrade()
	{

	}

	//tstring EscaBalustrade::LoadModel()
	//{
	//	auto model_path = EscalatorPart::LoadModel();

	//	return model_path;
	//}

	//int EscaBalustrade::GetPiecePattern()
	//{
	//	return piece_pattern_;
	//}

	//void EscaBalustrade::SetPiecePattern(int n)
	//{
	//	piece_pattern_ = n;
	//}

	//int EscaBalustrade::GetMaterialType()
	//{
	//	if (GetPartMaterial() > 0)
	//	{
	//		material_type_ = 0;//db_visitor::DBVisitorMgr::Instance()->GetVisitor()->GetMaterialType(mat_id_);
	//	}
	//	else
	//	{
	//		material_type_ = 0;
	//	}
	//	return material_type_;
	//}

	//void EscaBalustrade::SetMaterialType(int n)
	//{
	//	material_type_ = n;
	//}

	/*EscaSkirtLighting::EscaSkirtLighting()
		:pattern_(-1)
	{

	}*/

	EscaSkirtLighting::EscaSkirtLighting()
	{

	}

	EscaSkirtLighting::~EscaSkirtLighting()
	{

	}

	/*int EscaSkirtLighting::GetPattern()
	{
		return pattern_;
	}

	void EscaSkirtLighting::SetPattern(int n)
	{
		pattern_ = n;
	}*/

	/*EscaSideCladding::EscaSideCladding()
		:piece_pattern_(-1), pit_pattern_(-1), lighting_pattern_(-1)
	{

	}*/

	EscaSideCladding::EscaSideCladding()
	{

	}

	EscaSideCladding::~EscaSideCladding()
	{

	}

	tstring EscaSideCladding::LoadModel()
	{
		auto model_path = EscalatorPart::LoadModel();

		return model_path;
	}

	//int EscaSideCladding::GetPiecePattern()
	//{
	//	return piece_pattern_;
	//}

	//void EscaSideCladding::SetPiecePattern(int n)
	//{
	//	piece_pattern_ = n;
	//}

	//int EscaSideCladding::GetPitPattern()
	//{
	//	return pit_pattern_;
	//}

	//void EscaSideCladding::SetPitPattern(int n)
	//{
	//	pit_pattern_ = n;
	//}

	//int EscaSideCladding::GetLightingPattern()
	//{
	//	return lighting_pattern_;
	//}

	//void EscaSideCladding::SetLightingPattern(int n)
	//{
	//	lighting_pattern_ = n;
	//}

	//int EscaSideCladding::GetMaterialType()
	//{
	//	if (GetPartMaterial() > 0)
	//	{
	//		material_type_ = 0;//db_visitor::DBVisitorMgr::Instance()->GetVisitor()->GetMaterialType(mat_id_);
	//	}
	//	else
	//	{
	//		material_type_ = 0;
	//	}
	//	return material_type_;
	//}

	//void EscaSideCladding::SetMaterialType(int n)
	//{
	//	material_type_ = n;
	//}

	/*EscaScenes::EscaScenes()
		:type_(-1), arrangement_(-1)
	{

	}*/

	EscaScenes::EscaScenes()
	{

	}

	EscaScenes::~EscaScenes()
	{

	}

	/*int EscaScenes::GetScenesType()
	{
		return type_;
	}

	void EscaScenes::SetScenesType(int t)
	{
		type_ = t;
	}

	int EscaScenes::GetScenesArrangement()
	{
		return arrangement_;
	}

	void EscaScenes::SetScenesArrangement(int t)
	{
		arrangement_ = t;
	}*/

}
