#ifndef IELEVALTOR_PART_H_17961AD6_22AB_47A4_B192_24459405827A
#define IELEVALTOR_PART_H_17961AD6_22AB_47A4_B192_24459405827A

namespace decoration_vr_interface
{
class IElevatorPart : public std::enable_shared_from_this<IElevatorPart>
{
public:
	virtual int GetPartType() = 0;
	virtual int64 GetPartId() = 0;

	virtual tstring GetPartName() = 0;
	virtual void SetPartName(const tstring &new_name) = 0;

	virtual tstring LoadModel() = 0;

	virtual bool IsValidPart() { return false; }

	virtual bool GetIsHasReflect() = 0;
	virtual void SetIsHasReflect(const bool &is_reflect) = 0;
};
typedef std::shared_ptr<IElevatorPart> IElevatorPartPtr;
}
#endif