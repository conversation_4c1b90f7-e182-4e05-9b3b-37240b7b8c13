#ifndef _VR_EscaSideCladdingVisitor_H_
#define _VR_EscaSideCladdingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Side_Cladding.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaSideCladdingVisitor : public BaseVisitor
	{
	public:
		VrEscaSideCladdingVisitor();
		virtual ~VrEscaSideCladdingVisitor();

		DEFINE_CREATE_FUN(VrEscaSideCladdingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Side_Cladding> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
