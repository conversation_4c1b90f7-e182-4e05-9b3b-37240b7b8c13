﻿#include "stdafx.h"
#include "common_part_factory.h"
#include "common_part.h"

namespace decoration_vr_interface
{
	CommonPartFactory::CommonPartFactory(int part_type)
		:part_type_(part_type)
	{

	}

	CommonPartFactory::~CommonPartFactory()
	{
	}

	void CommonPartFactory::SetPartType(int ty)
	{
		part_type_ = ty;
	}

	int CommonPartFactory::GetPartType()
	{
		return part_type_;
	}

	decoration_vr_interface::IElevatorPartPtr CommonPartFactory::CreatePart(FactoryArgs* args)
	{
		switch (args->createType)
		{
		case PCT_CREATE_FROM_JSON_CONFIG:
			return GetPartFromJsonConfig(args);

		case PCT_CREATE_FROM_JSON:
			return GetPartFromJsonObject(args);

		default:
			return nullptr;
		}
	}

	decoration_vr_interface::IElevatorPartPtr CommonPartFactory::GetPartFromJsonConfig(FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJsonConfig*>(args);
		if (json_arg != nullptr)
		{
			//auto raw_config = static_cast<svr_data::SvrExhibitCarConfig*>(json_arg->raw_config_obj_);
			//if (raw_config != nullptr)
			{
				//const auto& children = raw_config->ChildParts;
				//auto it = std::find_if(children.begin(), children.end(), [this](const svr_data::SvrCommonPart& child) {
				//	return child.basic_info_.PartType == this->GetPartType();
				//});
				//if (it != children.end())
				{
					auto child_part = reinterpret_cast<svr_data::SvrCommonPart*>(json_arg->raw_part_obj_);
					auto common_part = RSE_MAKE_SHARED<CommonPart>();
					SetPartInfo(common_part.get(), child_part);
					SetMaterial(common_part.get(), child_part->material_infos_);
					SetPartPosition(common_part.get(), json_arg);
					return common_part;
				}
			}
		}

		return nullptr;
	}

	decoration_vr_interface::IElevatorPartPtr CommonPartFactory::GetPartFromJsonObject(FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJson*>(args);
		if (json_arg != nullptr && json_arg->raw_obj_ != nullptr)
		{
			auto raw_part = static_cast<svr_data::SvrExhibitCommonPart*>(json_arg->raw_obj_);
			if (raw_part != nullptr)
			{
				int real_type = raw_part->part_info_.basic_info_.PartType;
				if (real_type <= 0) real_type = GetPartType();
				if (real_type == PartTypeHandrail)
				{//单独变化扶手时
					auto car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
					auto id = raw_part->part_info_.basic_info_.Id;
					auto pos = atoi(raw_part->part_info_.basic_info_.StringVal1.c_str());
					if (pos >= 0 && pos <= 7)
					{//变化位置时
						auto var = RSE_MAKE_SHARED<Variant>();
						var->i_ = pos;
						car_config->SetExtendProperty(EPK_HandrailSetupPos, var);
					}
					if (id <= 0)
					{
						auto handrail = car_config->GetPart(PartTypeHandrail);
						if (handrail)
						{//若原来有扶手，返回(可能位置发生变化)
							return handrail->shared_from_this();
						}
						else
						{//若原来无扶手，返回默认
							auto common_part = RSE_MAKE_SHARED<CommonPart>();
							common_part->part_type_ = real_type;//必须赋值
							return common_part;
						}
					}
				}
				else if (real_type == PartTypeMirror)
				{//单独变化镜子时
					auto car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
					auto id = raw_part->part_info_.basic_info_.Id;
					auto pos = raw_part->part_info_.basic_info_.IntVal3;
					switch (pos)
					{//变化位置时
					case kConstMirrorSetupPosBackWall:
					case kConstMirrorSetupPosLeftWall:
					case kConstMirrorSetupPosRightWall:
					case kHandrailPosNone:
					{
						auto var = RSE_MAKE_SHARED<Variant>();
						var->i_ = pos;
						car_config->SetExtendProperty(EPK_MirrorSetupPos, var);
					}
						break;
					default:
						break;
					}
					if (id <= 0)
					{
						auto mirror = car_config->GetPart(PartTypeMirror);
						if (mirror)
						{//若原来有镜子，返回(可能位置发生变化)
							return mirror->shared_from_this();
						}
						else
						{//若原来无镜子，返回默认
							auto common_part = RSE_MAKE_SHARED<CommonPart>();
							common_part->part_type_ = real_type;//必须赋值
							return common_part;
						}
					}
				}
				else if (real_type == PartTypeICCard || real_type == PartTypeAuxICCard)
				{//
					auto car_config = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorConfig()->GetCarConfig();
					auto id = raw_part->part_info_.basic_info_.Id;
					auto pos = raw_part->part_info_.basic_info_.IntVal3;
					if (id <= 0)
					{
						auto common_part = RSE_MAKE_SHARED<CommonPart>();
						common_part->part_type_ = real_type;//必须赋值
						return common_part;
					}
				}
				else if (real_type == PartTypeImportedHallDoor
					|| real_type == PartTypeImportedCarDoor
					|| real_type == PartTypeFrontAccessory)
				{//单独变化导入门,导入前壁时
					auto id = raw_part->part_info_.basic_info_.Id;
					if (id <= 0)
					{//直接返回
						auto common_part = RSE_MAKE_SHARED<CommonPart>();
						common_part->part_type_ = real_type;
						return common_part;
					}
				}
				auto common_part = RSE_MAKE_SHARED<CommonPart>();
				SetPartInfo(common_part.get(), &(raw_part->part_info_));
				SetMaterial(common_part.get(), raw_part->part_info_.material_infos_);
				return common_part;
			}
		}

		return nullptr;
	}

	void CommonPartFactory::SetPartPosition(IElevatorPart* part, FactoryArgs* args)
	{
		auto json_arg = static_cast<FactoryArgsOfLoadingFromJsonConfig*>(args);
		auto config = json_arg->config_;
		auto raw_config = static_cast<svr_data::SvrExhibitCarConfig*>(json_arg->raw_config_obj_);

		//const auto& datas = raw_config->SetupPositionDatas;
		//auto it = std::find_if(datas.begin(), datas.end(), [part](const svr_data::SvrExhibitIntLongKeyValData& data) {
		//	return data.IntKey == part->GetPartType();
		//});
		//if (it != datas.end())
		{
			switch (part->GetPartType())
			{
			case PartTypeHandrail:
			{
				auto var = RSE_MAKE_SHARED<Variant>();
				var->i_ = raw_config->SetupPositionDatas[json_arg->order_id_];
				config->SetExtendProperty(EPK_HandrailSetupPos, var);
			}
				break;
			case PartTypeMirror:
			{
				auto var = RSE_MAKE_SHARED<Variant>();
				var->i_ = raw_config->SetupPositionDatas[json_arg->order_id_];
				config->SetExtendProperty(EPK_MirrorSetupPos, var);
			}
				break;
			default:
				break;
			}
		}
	}

	void CommonPartFactory::SetPartInfo(IElevatorPart* part, svr_data::SvrCommonPart* svr_part)
	{
		auto common_part = static_cast<CommonPart*>(part);
		common_part->part_type_ = svr_part->basic_info_.PartType;
		common_part->part_id_ = svr_part->basic_info_.Id;

		common_part->setup_pos_ = svr_part->basic_info_.IntVal1;
		common_part->pos_x_ = svr_part->basic_info_.FloatVal1;
		common_part->pos_y_ = svr_part->basic_info_.FloatVal2;
	}

	void CommonPartFactory::SetMaterial(IElevatorPart* part, const rse::vector<svr_data::SvrPartMaterial>& edit_mats)
	{
		auto common_part = static_cast<CommonPart*>(part);

		for (auto it = edit_mats.begin(), ie = edit_mats.end(); it != ie; ++it)
		{
			if (it->MaterialId > 0)
			{
				common_part->editparts_.push_back(it->EditPartId);
				common_part->editpart_materials_[it->EditPartId] = it->MaterialId;
			}
		}
	}

}

