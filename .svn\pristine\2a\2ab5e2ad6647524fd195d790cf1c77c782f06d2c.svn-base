#ifndef PART_TYPE_MANAGE_H_9485FBC3_7CC4_42E3_B514_0DC75E7AAFC9
#define PART_TYPE_MANAGE_H_9485FBC3_7CC4_42E3_B514_0DC75E7AAFC9

namespace decoration_vr_interface
{
	enum MaterialPartType
	{
		MPT_UNKNOWN,
		MPT_COMMON_PART,
		MPT_ELECTRIC,
		MPT_WALL,
		MPT_TOP,
		MPT_ESCALATOR,
		MPT_HALL_ROOM
	};

	enum CategoryType
	{
		CarType,
		HallType,
		EscaConfig,
	};
	struct PartTypeInfo
	{
		int id_;
		int parent_;
		tstring name_;

		tstring model_array_;
		tstring material_array_;

		tstring vr_msg_;
		int vr_type_;

		int category_;
	};
	typedef std::shared_ptr<PartTypeInfo> PartTypeInfoPtr;

	class PartTypeManager
	{
	public:
		void Initialize();

		PartTypeInfo* GetTypeInfo(int part_type);
		PartTypeInfo* GetTypeInfo(tstring name);

		virtual MaterialPartType GetMaterialPartType(int part_type);
		bool IsMaterialPart(int part_type);
	protected:
		void InitializePartTypeInfoList();

	protected:
		rse::vector<PartTypeInfoPtr> type_info_list_;
	};

}

#endif