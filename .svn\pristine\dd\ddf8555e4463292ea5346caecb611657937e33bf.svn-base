#include "stdafx.h"
#include "VrMirrorVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_Mirror.h"
#include "material_channel.h"
#include "common_part.h"
#include "car_wall_part.h"

namespace decoration_vr_interface
{

	VrMirrorVisitor::VrMirrorVisitor()
	{
        part_type_ = PartTypeMirror;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		InitializeAvailablePartTypes(types);

	} 

	VrMirrorVisitor::~VrMirrorVisitor()
	{
	}


	void VrMirrorVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_Mirror>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}

	}

	bool VrMirrorVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		model_->Clear();
		auto row = model_->AddRow();

		auto part = static_cast<CommonPart*>(vr_change->config_->GetPart(part_type_));

		auto config = vr_change->config_;
		auto setup_pos_property = config->GetExtendProperty(EPK_MirrorSetupPos);
		int setup_pos = 0;
		if (setup_pos_property)
		{
			setup_pos = setup_pos_property->i_;
		}

		if (!part || part->GetPartId() <= 0 || setup_pos == 0)
		{
			model_->SetBool(row, false);
			return true;
		}

		int wall_part_type = PartTypeBackWall;
		switch (setup_pos)
		{
		case kConstMirrorSetupPosBackWall:
			wall_part_type = PartTypeBackWall;
			break;
		case kConstMirrorSetupPosLeftWall:
			wall_part_type = PartTypeLeftWall;
			break;
		case kConstMirrorSetupPosRightWall:
			wall_part_type = PartTypeRightWall;
			break;
		default:
			break;
		}

		auto wall_part = static_cast<CarWallPart*>(config->GetPart(wall_part_type));
		float pos_x = wall_part->GetWidth() * 0.5f;
		float pos_y = 0;

        auto vr_glob = VrGlobalInfo::Instance();

		auto model_file = part->LoadModel();
		auto path = Util::CombinePath(vr_glob->get_config_info()->get_model_dir(), model_file);
		AddFileToModelCache(part->GetPartType(), part->GetPartId(), model_file, path);

		model_->SetPath(row, path.c_str());
		model_->SetPos_X(row, pos_x);
		model_->SetPos_Y(row, pos_y);
		model_->SetBool(row, true);
		model_->SetLocation(row, setup_pos);

		return true;
	}

	void VrMirrorVisitor::PrintData(const tchar* file_name)
	{

		model_->PrintData(file_name);

	}

}
    