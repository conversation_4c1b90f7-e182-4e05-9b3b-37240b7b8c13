#include "stdafx.h"
#include "ElevatorConfigManager.h"
#include "IConfig.h"
#include "IElevatorConfig.h"

namespace decoration_vr_interface
{
void ElevatorConfigManager::SetCurrentWorkingElevatorConfig(int index)
{
	if(index >= 0 && index < elevator_config_list_.size())
	{
		current_working_config_index_ = index;
	}
}

int ElevatorConfigManager::GetCurrentElevatorId()
{
	return current_working_config_index_;
}

IConfigPtr ElevatorConfigManager::GetCarConfig(int elev_order_id)
{
	return elevator_config_list_[elev_order_id]->GetCarConfig();
}

IConfigPtr ElevatorConfigManager::GetHallConfig(int elev_order_id)
{
	return elevator_config_list_[elev_order_id]->GetHallConfig(1);
}

decoration_vr_interface::IConfigPtr ElevatorConfigManager::GetEscalatorConfig(int elev_order_id)
{
	return elevator_config_list_[elev_order_id]->GetEscalatorConfig();
}

IElevatorConfigPtr ElevatorConfigManager::GetCurrentElevatorConfig()
{
	return elevator_config_list_[current_working_config_index_];
}

IConfigPtr ElevatorConfigManager::GetCurrentCarConfig()
{
	return GetCarConfig(current_working_config_index_);
}

IConfigPtr ElevatorConfigManager::GetCurrentHallConfig()
{
	return GetHallConfig(current_working_config_index_);
}

ElevatorSize* ElevatorConfigManager::GetCurrentElevatorSize()
{
	return GetCurrentElevatorConfig()->GetElevatorSize();
}

EscalatorSize* ElevatorConfigManager::GetCurrentEscalatorSize()
{
	return GetCurrentElevatorConfig()->GetEscalatorSize();
}

decoration_vr_interface::IConfigPtr ElevatorConfigManager::GetCurrentEscalatorConfig()
{
	return GetEscalatorConfig(current_working_config_index_);
}

void ElevatorConfigManager::Finalize()
{
	elevator_config_list_.clear();
	current_working_config_index_ = -1;
}

}