#pragma once
#include "esca_elevator_part_factory.h"
namespace decoration_vr_interface
{
	class EscaHandrailEnterFactory : public EscalatorPartFactory
	{
	public:
		EscaHandrailEnterFactory(int part_type)
			:EscalatorPartFactory(part_type)
		{}
		virtual ~EscaHandrailEnterFactory(){}
	protected:
		virtual IElevatorPartPtr GetPart(FactoryArgsOfLoadingFromDb* args);
		virtual IElevatorPartPtr GetPart(FactoryArgsOfConfigLoading* args);
	};

}