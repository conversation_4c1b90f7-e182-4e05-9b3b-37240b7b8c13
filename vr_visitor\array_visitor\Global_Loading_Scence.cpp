//
//  Global_Select.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Global_Loading_Scence.h"


namespace decoration_vr_interface
{

	bool Global_Loading_Scence::GetSelected(int row)
	{
		return visitor_->GetElementInt(row, 0);
	}

	void Global_Loading_Scence::SetSelected(int row, bool val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	rse::string Global_Loading_Scence::GetPath(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Global_Loading_Scence::SetPath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	int Global_Loading_Scence::GetScenePartType(int row)
	{
		return visitor_->GetElementInt(row, 5);
	}

	void Global_Loading_Scence::SetScenePartType(int row, int val)
	{
		visitor_->SetElementValue(row, 5, val);
	}

	rse::string Global_Loading_Scence::GetScenePath(int row)
	{
		const char* val = visitor_->GetElementString(row, 2);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Global_Loading_Scence::SetScenePath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 2, val);
	}

}