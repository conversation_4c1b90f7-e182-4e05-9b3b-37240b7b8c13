﻿#pragma once
namespace decoration_vr_interface
{
	static const int kConstTypeElevator = 1;
	static const int kConstTypeEscalator = 2;

	static const int kConstElevatorSizeWidth = 160;
	static const int kConstElevatorSizeDepth = 210;

	static const int kHandrailPosNone = 0; //无
	static const int kHandrailPosBack = 1; //后
	static const int kHandrailPosLeft = 2; //左
	static const int kHandrailPosRight = 4; //右

	static const int kConstMirrorSetupPosBackWall = 2000; //后
	static const int kConstMirrorSetupPosLeftWall = 1000; //左
	static const int kConstMirrorSetupPosRightWall = 3000; //右

	static const int kCallSetupDoorLeft = 0; // 门左
	static const int kCallSetupDoorRight = 1; // 门右
	static const int kCallSetupDoorParrellel = 2; // 群控位置

	//ConstSetupPosForDb
	static const int kDoorHeader = 1;
	static const int kFrontWall = 2;
	static const int kCopWall = 4;
	static const int kLeftWall = 8;
	static const int kRightWall = 16;
	static const int kBackWall = 32;
	static const int kFrontWallRight = 64;
	static const int kCopWallLeft = 128;
	static const int kBackWallLeft = 256;
	static const int kBackWallCenter = 512;

	//ConstCopSetupPosition
	static const int kCspCopLeft = 4200; //操纵壁左侧
	static const int kCspCopCenter = 4100; //操纵壁中心
	static const int kCspFrontRight = 4000; //前壁左侧
	static const int kCspFrontCenter = 4050; //前壁中心
	static const int kCspLeft = 1000; //左壁
	static const int kCspBack = 2000; //后壁
	static const int kCspBackLeft = 2500; //暂时不用
	static const int kCspBackCenter = 2100; //暂时不用
	static const int kCspRight = 3000; //右壁

	enum ConstHandrailTypeForCop
	{
		Nothing = 0,
		HaveMainCop = 1,
		HaveAuxCop = 2,
		HaveHdCop = 4,
	};

	static const int HandrailType_BackWallPanelCount_3 = 101; //后壁壁板3块
	static const int HandrailType_BackWallPanelCount_2 = 102; //后壁壁板2块

	static const int HandrailType_SideWallPanelCount_3_NullCop_NullHdCop = 110; //（左、右）侧壁壁板3块，无COP，无残操
	static const int HandrailType_SideWallPanelCount_3_HasCop_NullHdCop = 111; //（左、右）侧壁壁板3块，有COP,无残操
	static const int HandrailType_SideWallPanelCount_3_HasCop_HaveHdCop = 112; //（右）侧壁壁板3块，有COP,有残操
	static const int HandrailType_SideWallPanelCount_3_NullCop_HaveHdCop = 113; //（右）侧壁壁板3块，无COP,有残操

	static const int HandrailType_SideWallPanelCount_2_NullCop_NullHdCop = 114; //（左、右）侧壁壁板2块，无COP,无残操
	static const int HandrailType_SideWallPanelCount_2_HaveCop_NullHdCop = 115; //（左、右）侧壁壁板2块，有COP,无残操
	static const int HandrailType_SideWallPanelCount_2_NullCop_HaveHdCop = 116; //（右）侧壁壁板2块，无COP,有残操

	/////////////////////////add by xqxqx 2013-8-16////////////////////////////////////////////////////////////
	//宽轿厢，切换为N系列COP时，已经将侧壁尺寸变为三分割，所以只需要定义轿壁3块板常量
	static const int HandrailType_SideWallPanelCount_3_HasCop_NullHdCop_IsNCop = 117; //（左、右）侧壁壁板3块，有COP,无残操
	static const int HandrailType_SideWallPanelCount_3_HasCop_HaveHdCop_IsNCop = 118; //（右）侧壁壁板3块，有COP,有残操
	static const int HandrailType_SideWallPanelCount_3_NullCop_HaveHdCop_IsNCop = 119; //（右）侧壁壁板3块，无COP,有残操
	//////////////////////////////////////////////////////////////////////////////////////////////////////////////
}