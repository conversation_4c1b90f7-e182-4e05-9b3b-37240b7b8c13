#ifndef _SVR_MATERIAL_H_
#define _SVR_MATERIAL_H_

#include <vector>

#include "svr_base_data.h"

#include "svr_part_basic_info.h"
#include "svr_material_channel.h"
#include "svr_material_special_rule.h"
#include "svr_file_digital_info.h"

namespace svr_data
{
	class SvrMaterialInfo : public ISvrBaseData
	{
	public:
		bool Parse(const Json::Value& jobj) override;

	public:
		SvrPartBasicInfo basic_info_;
		rse::vector<SvrMaterialChannel> channels_;
		rse::vector<SvrMaterialSpecialRule> rules_;
		rse::vector<SvrFileDigitalInfo> file_digitals_;

	private:
		bool ParseMaterialChannels(const Json::Value& jobj);
		bool ParseMaterialSpecialRules(const Json::Value& jobj);
		bool ParseFileDigitals(const Json::Value& jobj);
	};

	class SvrExhibitMaterialInfo : public SvrMaterialInfo
	{

	};
}
#endif
