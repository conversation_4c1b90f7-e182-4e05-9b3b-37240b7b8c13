#ifndef _VR_EscaStepVisitor_H_
#define _VR_EscaStepVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Step.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaStepVisitor : public BaseVisitor
	{
	public:
		VrEscaStepVisitor();
		virtual ~VrEscaStepVisitor();

		DEFINE_CREATE_FUN(VrEscaStepVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Step> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
