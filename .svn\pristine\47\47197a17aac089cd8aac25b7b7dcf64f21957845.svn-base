#pragma once
namespace decoration_vr_interface
{
	class DownloadAssist
	{
	public:
		DownloadAssist();
		~DownloadAssist();

		//static DownloadAssist* Instance();

		void AddVRInfo(const IVrVisitorPtr& visitor, const VrChangeArg& arg);
		bool AddDownloadInfo(const rse::string& url, const rse::string& file, const rse::string& fd);
		bool DownloadFileCompleted(const rse::string& file);
		void SendMessage();
		//void Finalize();
	private:
		bool FileExists(const rse::string& file);	
		//static DownloadAssist *ins_;

	private:
		struct DownloadInfo
		{
			rse::string url;
			rse::string file;
			rse::string file_digital;
		};
		rse::vector<DownloadInfo> download_list_;

		rse::vector<std::shared_ptr<IVrVisitor>> notify_visitors_;
		rse::vector<VrChangeArg> notify_args_;
	};


}

