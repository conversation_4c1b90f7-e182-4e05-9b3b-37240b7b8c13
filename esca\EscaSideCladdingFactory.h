#pragma once
#include "esca_elevator_part_factory.h"
namespace decoration_vr_interface
{
	class EscaSideCladdingFactory : public EscalatorPartFactory
	{
	public:
		EscaSideCladdingFactory(int part_type)
			:EscalatorPartFactory(part_type)
		{}
		virtual ~EscaSideCladdingFactory(){}
	protected:
		virtual IElevatorPartPtr GetPart(FactoryArgsOfLoadingFromDb* args);
		virtual IElevatorPartPtr GetPart(FactoryArgsOfConfigLoading* args);
	};
}
