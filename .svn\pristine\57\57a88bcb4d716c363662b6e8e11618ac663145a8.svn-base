#include "stdafx.h"
#include "VrLanternDisplayVisitor.h"

#include "VrGlobalInfo.h"
#include "Sel_HLLcd.h"
#include "material_channel.h"
#include "electric_part.h"

namespace decoration_vr_interface
{

	VrLanternDisplayVisitor::VrLanternDisplayVisitor()
	{
        part_type_ = PartTypeLanternDisplay;
		rse::vector<PartTypeId> types;
		types.push_back(part_type_);
		InitializeAvailablePartTypes(types);

	} 

	VrLanternDisplayVisitor::~VrLanternDisplayVisitor()
	{
	}


	void VrLanternDisplayVisitor::Initialize()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto part_type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<Sel_HLLcd>(vr_glob->get_dg_scene(), part_type_info->model_array_.c_str());
		}
	}

	bool VrLanternDisplayVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		model_->Clear();
		auto elevator_list_ptr = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->get_elevator_config_list_ptr();

		tstring dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
		for (int elevator_i = 0; elevator_i < elevator_list_ptr->size(); ++elevator_i)
		{
			auto elevator = (*elevator_list_ptr)[elevator_i];
			IConfigPtr hall_config = elevator->GetHallConfig(0);

			auto hl = static_cast<ElectricPart*>(hall_config->GetPart(PartTypeLantern));
			if (!hl) continue;
			tstring lcd_path = hl->GetLcdPath();

			int row = model_->AddRow();

			if (lcd_path != TSTR(""))
			{
				model_->Setm_bool_Select(row, false);
				auto path = Util::CombinePath(dir, lcd_path);
				AddFileToModelCache(hl->GetLcdType(), hl->GetLcd(), lcd_path, path);
				model_->Setm_str_Path(row, path.c_str());
				model_->Setm_int_Mark(row, 0);
			}
			else
			{
				model_->Setm_bool_Select(row, true);
			}
		}

		return true;
	}

	void VrLanternDisplayVisitor::PrintData(const tchar* file_name)
	{

		model_->PrintData(file_name);

	}

}
    