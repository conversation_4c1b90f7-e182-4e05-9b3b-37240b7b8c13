#include "stdafx.h"
#include "Global_Control_Flag.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		int Global_Control_Flag::GetDoorState(int row)
		{
			return visitor_->GetElementInt(row, 0);
		}

		void Global_Control_Flag::SetDoorState(int row, int val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Global_Control_Flag::GetDoorCommand(int row)
		{
			return visitor_->GetElementInt(row, 1);
		}

		void Global_Control_Flag::SetDoorCommand(int row, int val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		int Global_Control_Flag::GetCameraState(int row)
		{
			return visitor_->GetElementInt(row, 2);
		}

		void Global_Control_Flag::SetCameraState(int row, int val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

		int Global_Control_Flag::GetShowHideFlag(int row)
		{
			return visitor_->GetElementInt(row, 3);
		}

		void Global_Control_Flag::SetShowHideFlag(int row, int val)
		{
			visitor_->SetElementValue(row, 3, val);
		}

		bool Global_Control_Flag::GetReachedFloorNotify(int row)
		{
			return visitor_->GetElementBool(row, 4);
		}

		void Global_Control_Flag::SetReachedFloorNotify(int row, bool val)
		{
			visitor_->SetElementValue(row, 4, val);
		}

	}
}