#pragma once

#include "IVrVisitor.h"

namespace decoration_vr_interface
{
	typedef IVrVisitorPtr (*CreateVisitorFunction)();

	class VrVisitorFactory
	{
	public:
		VrVisitorFactory();
		~VrVisitorFactory();
		void Register(const tchar* class_name, CreateVisitorFunction);
		IVrVisitorPtr CreateVrVisitor(const tchar* class_name);
	protected:
		rse::map<tstring, CreateVisitorFunction> factory_map_;
	};
}
