#pragma once
#include "esca_elevator_part_factory.h"
namespace decoration_vr_interface
{
	class EscaBalustradeFactory : public EscalatorPartFactory
	{
	public:
		EscaBalustradeFactory(int part_type)
			:EscalatorPartFactory(part_type)
		{}
		virtual ~EscaBalustradeFactory(){}
	protected:
		virtual IElevatorPartPtr GetPart(FactoryArgsOfLoadingFromDb* args);
		virtual IElevatorPartPtr GetPart(FactoryArgsOfConfigLoading* args);
	};

}