#ifndef _VR_EscaPhotoelectricVisitor_H_
#define _VR_EscaPhotoelectricVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Photoelectric.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaPhotoelectricVisitor : public BaseVisitor
	{
	public:
		VrEscaPhotoelectricVisitor();
		virtual ~VrEscaPhotoelectricVisitor();

		DEFINE_CREATE_FUN(VrEscaPhotoelectricVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Photoelectric> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
