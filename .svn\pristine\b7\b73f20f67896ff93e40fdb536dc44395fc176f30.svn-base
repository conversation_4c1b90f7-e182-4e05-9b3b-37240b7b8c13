#ifndef _SVR_MATERIAL_CHANNEL_H_
#define _SVR_MATERIAL_CHANNEL_H_

#include "svr_base_data.h"

namespace svr_data
{

	class SvrMaterialChannel : public ISvrBaseData
	{
	public:
		virtual bool Parse(const Json::Value& jobj) override;

	public:
		int32_t PartType;
		int32_t ChannelId;
		int32_t MaterialType;
		int32_t ChannelColor;
		int32_t ReflectColor;
		int32_t AmbientColor;
		int32_t BlendMode;
		rse::string TexturePath;
		int32_t TexMipLevel;
		int32_t TexSpecialMark;
		int32_t TexMapMode;
		float TexUOffset;
		float TexVOffset;
		float TexUWrap;
		float TexVWrap;
		rse::string ChannelMapPath;
		int32_t ChannelMapMipLevel;
		int32_t ChannelMapSpecialMask;
		int32_t ChannelMapMapMode;
		float ChannelMapUOffset;
		float ChannelMapVOffset;
		float ChannelMapUWrap;
		float ChannelMapVWrap;
		int32_t MaterialMark;
	};

}//namespace svr_data

#endif//_MATERIAL_CHANNEL_H_

