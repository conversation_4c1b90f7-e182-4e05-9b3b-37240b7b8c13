#pragma once

//#include "stdafx.h"
#include "parse_json/svr_base_data.h"

typedef int64_t int64;

namespace decoration_vr_interface
{
	class IDbVisitor
	{
	public:
		virtual svr_data::IDataBasePtr LoadMaterial(int64 mat_id) = 0;
		virtual svr_data::IDataBasePtr LoadPart(int part_type, int64 part_id) = 0;

		virtual svr_data::IDataBasePtr LoadMaterialByGoodsId(int64 part_id) = 0;
		virtual svr_data::IDataBasePtr LoadPartByGoodsId(int part_type, int64 part_id) = 0;

		virtual svr_data::IDataBasePtr LoadPartByString(int part_type, const rse::string& c) = 0;


		virtual svr_data::IDataBasePtr LoadBootomMaterialByGoodsId(int64 goods_id, int width, int depth, int doorWidth, bool isThroughDoor) = 0;
		//virtual svr_data::IDataBasePtr LoadMaterialByVersionId(int64 version_id) = 0;
		//virtual svr_data::IDataBasePtr LoadPartByVersionId(int part_type, int64 version_id) = 0;
	};
}
