#pragma once

#include "DGBaseVisitor.h"
namespace decoration_vr_interface
{
	class Sel_Shaft : public DGBaseVisitor
	{
	public:
		Sel_Shaft(IDGSceneEx* scene, const tchar* arr_name);
			
		bool Get_Select(int row);
		void Set_Select(int row, bool val);

		rse::string Get_Path(int row);
		void Set_Path(int row, const tchar* val);

		void Set_Width(int row, float val);
		void Set_Depth(int row, float val);

		//int Get_Mark(int row);
		//void Set_Mark(int row, int val);
	};
}

