#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Emids;
class Sel_CopLcd;
class MaterialChannel;

class VrEMIDSVisitor : public BaseVisitor
{
public:
	VrEMIDSVisitor();
	~VrEMIDSVisitor();

	DEFINE_CREATE_FUN(VrEMIDSVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	std::shared_ptr<Sel_Emids> model_;
	std::shared_ptr<Sel_CopLcd> lcd_;
	std::shared_ptr<MaterialChannel> material_;
};
}
