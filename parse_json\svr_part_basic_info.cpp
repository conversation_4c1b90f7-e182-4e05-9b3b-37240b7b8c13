#include "stdafx.h"
#include "svr_part_basic_info.h"

#include "svr_json_helper.h"

namespace svr_data
{
	bool SvrPartBasicInfo::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		IF_ERROR_RETURN(GetJsonValue(jv["partType"], PartType));
		IF_ERROR_RETURN(GetJsonValue(jv["id"], Id));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal1"], IntVal1));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal2"], IntVal2));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal3"], IntVal3));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal4"], IntVal4));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal5"], IntVal5));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal6"], IntVal6));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal7"], IntVal7));
		IF_ERROR_RETURN(GetJsonValue(jv["intVal8"], IntVal8));
		IF_ERROR_RETURN(GetJsonValue(jv["floatVal1"], FloatVal1));
		IF_ERROR_RETURN(GetJsonValue(jv["floatVal2"], FloatVal2));
		IF_ERROR_RETURN(GetJsonValue(jv["floatVal3"], FloatVal3));
		IF_ERROR_RETURN(GetJsonValue(jv["floatVal4"], FloatVal4));
		IF_ERROR_RETURN(GetJsonValue(jv["stringVal1"], StringVal1));
		IF_ERROR_RETURN(GetJsonValue(jv["stringVal2"], StringVal2));
		IF_ERROR_RETURN(GetJsonValue(jv["stringVal3"], StringVal3));
		IF_ERROR_RETURN(GetJsonValue(jv["stringVal4"], StringVal4));
		IF_ERROR_RETURN(GetJsonValue(jv["stringVal5"], StringVal5));
		IF_ERROR_RETURN(GetJsonValue(jv["name"], Name));
		IF_ERROR_RETURN(GetJsonValue(jv["langStringVal1"], LangStringVal1));
		IF_ERROR_RETURN(GetJsonValue(jv["langStringVal2"], LangStringVal2));
		IF_ERROR_RETURN(GetJsonValue(jv["langStringVal3"], LangStringVal3));
		IF_ERROR_RETURN(GetJsonValue(jv["langStringVal4"], LangStringVal4));
		IF_ERROR_RETURN(GetJsonValue(jv["langStringVal5"], LangStringVal5));
		IF_ERROR_RETURN(GetJsonValue(jv["note"], Note));
		IF_ERROR_RETURN(GetJsonValue(jv["picPath"], PicPath));
		IF_ERROR_RETURN(GetJsonValue(jv["editUserForRead"], EditUserForRead));
		IF_ERROR_RETURN(GetJsonValue(jv["editDateForRead"], EditDateForRead));
		IF_ERROR_RETURN(GetJsonValue(jv["editStateForRead"], EditStateForRead));
		IF_ERROR_RETURN(GetJsonValue(jv["ownerLibraryForRead"], OwnerLibraryForRead));
		IF_ERROR_RETURN(GetJsonValue(jv["referenceUserForRead"], ReferenceUserForRead));

		return true;
	}

}
