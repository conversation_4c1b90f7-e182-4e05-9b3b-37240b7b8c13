//
//  Sel_Ceiling.h
//  VrVisitor
//
//  Created by vrprg on 10:25:05.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Ceiling : public DGBaseVisitor
{
public:
	Sel_Ceiling(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name){}
	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

};
}