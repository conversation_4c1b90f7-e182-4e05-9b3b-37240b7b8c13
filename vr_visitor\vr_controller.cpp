#include "stdafx.h"
#include <algorithm>

#include "vr_controller.h"
#include "BaseVisitor.h"
#include "VrGlobalInfo.h"
#include "file_pool.h"
#include "material_channel_pool.h"
#include "download_assist.h"
#ifdef RENDER_SKETCHER_HTML
#include "decoration_vr_callback.h"
#endif

#ifdef RENDER_SKETCHER_ANDROID
//#include "sqlite3_aasset.h"
#endif

#define MSG_ENTER_AR_MODE_ID 201893
#define MSG_LEAVE_AR_MODE_ID 201894
#define MSG_AR_MODE_DOOR_SIZE_ID 201895

namespace decoration_vr_interface
{
	void ContextCallBack(int msg_id, int i2, const char* c)
	{
		switch (msg_id)
		{

		case MSG_ENTER_AR_MODE_ID:
		{
			VrGlobalInfo::Instance()->SetRunningMode(AR_RUNNING_MODE);
			VrGlobalInfo::Instance()->SetDoorWidth(0.0f);
			VrGlobalInfo::Instance()->SetDoorHeight(0.0f);
		}
		break;

		case MSG_AR_MODE_DOOR_SIZE_ID:
		{
			//float* a = reinterpret_cast<float*>(const_cast<char*>(c));
			float* a = (float*)c;
			float w = a[0];
			VrGlobalInfo::Instance()->SetDoorWidth(w);
			float h = a[1];
			VrGlobalInfo::Instance()->SetDoorHeight(h);

			LOGI("ContextCallBack w=%f, h=%f\n", w, h);
		}
		break;

		case MSG_LEAVE_AR_MODE_ID:
		{
			VrGlobalInfo::Instance()->SetRunningMode(DEFAULT_RUNNING_MODE);
			VrGlobalInfo::Instance()->SetDoorWidth(0.0f);
			VrGlobalInfo::Instance()->SetDoorHeight(0.0f);
		}
		break;

		default:
		{
			auto vr_callback = VrGlobalInfo::Instance()->get_vr_controller()->GetVrCallBack();
			if (vr_callback)
			{
				vr_callback->LoadCompleteCallBack(msg_id, i2, c);
			}
		}
		break;

		}
	}

	VrController::VrController()
		: is_load_vr_(false)
		, vr_callback_(nullptr)
		, context_(nullptr)
		, need_finalize_(true)
	{
		VrGlobalInfo::Create();
		VrGlobalInfo::Instance()->set_vr_controller(this);
	}

	VrController::~VrController()
	{
	}

	void VrController::Initialize()
	{
#ifdef XIZI_OTIS
		InitDTMap();
#endif // XIZI_OTIS
		
 		LoadVrVisitors();
 		LoadTogetherBuilder();
	}

	void VrController::Initialize(void* main_hwnd)
	{
		need_finalize_ = false;
		is_load_vr_ = true;

		auto vr_glob = VrGlobalInfo::Instance();
		vr_glob->set_main_hwnd(main_hwnd);
		vr_glob->set_parent_hwnd(main_hwnd);

		auto lib_mgr = fnGetLigMgr();
		vr_glob->set_dg_lib_mgr(lib_mgr);

		context_ = lib_mgr->GetContext();
		//context_->Create();

		auto config_info = vr_glob->get_config_info();
		IDGPathManager *pathMgr = context_->GetPathManager();
		pathMgr->AddBitmapSearchPath(config_info->get_texture_dir().c_str());
		pathMgr->AddDataSearchPath(config_info->resouseFilePath.c_str());
		
		Initialize();

		auto scene = context_->GetCurrentScene();
		vr_glob->set_dg_scene(scene);
		vr_glob->Initialize(scene);

		InitializeScene();
	}

	void VrController::Initialize(void* am, void* main_hwnd, void* parent_hwnd)
	{
		is_load_vr_ = true;
		VrGlobalInfo::Instance()->set_main_hwnd(main_hwnd);
		VrGlobalInfo::Instance()->set_parent_hwnd(parent_hwnd);

		if (CreateVrContext(am))
		{
			GlobalInfoDataCommon::Instance()->Initialize();
			Initialize();
			LoadScene();
			InitializeScene();
			context_->SetTextureSizeIsPower2(true);
			context_->SetContextCallBack(ContextCallBack);
			context_->Play();
		}
	}

	bool VrController::InitEngine()
	{
		auto vr_global_info = VrGlobalInfo::Instance();

		auto lib_mgr = fnGetLigMgr();
		if (lib_mgr->Initialize(1))
		{
			vr_global_info->set_dg_lib_mgr(lib_mgr);

			context_ = lib_mgr->GetContext();
			vr_global_info->set_dg_context(context_);

			return true;
		}

		return false;
	}

	void VrController::SetResourcesPath(const char* path)
	{
		if (!context_)
		{
			return;
		}

		auto real_path = Util::StringToTString(path);
		while (real_path.back() == '\\' || real_path.back() == '/')
		{
			real_path = real_path.substr(0, real_path.length() - 1);
		}

		auto vr_global_info = VrGlobalInfo::Instance();
		auto config_info = vr_global_info->get_config_info();
		config_info->resouseFilePath = real_path;

		auto path_mgr = context_->GetPathManager();
		path_mgr->AddDataSearchPath(config_info->resouseFilePath.c_str());
		path_mgr->AddDataSearchPath(config_info->get_texture_dir().c_str());
		path_mgr->AddDataSearchPath(config_info->get_model_dir().c_str());
		path_mgr->AddDataSearchPath(config_info->get_shader_dir().c_str());
	}

	bool VrController::CreateRender(void* hwnd, int w, int h, int dt)
	{
		is_load_vr_ = false;
		if (context_)
		{
			IDGContext::RenderDeviceParam param = { hwnd, w, h };
			IDGContext::RenderDeviceType device_type = IDGContext::DG_RDT_GLES;
			rse::string hmd;
#ifdef RENDER_SKETCHER_WINDOWS
			ReadRenderSetting(device_type, hmd);
#endif
			if (dt == 0)
			{
				device_type = IDGContext::DG_RDT_NULL;
			}
			is_load_vr_ = context_->CreateRender(&param, device_type, hmd.c_str());
		}
		
		return is_load_vr_;
	}

	bool VrController::InitVrController()
	{
		if (is_load_vr_)
		{
			GlobalInfoDataCommon::Instance()->Initialize();
			Initialize();
			LoadScene();
			InitializeScene();

			context_->SetTextureSizeIsPower2(true);
			context_->SetContextCallBack(ContextCallBack);
			context_->Play();
		}
		return is_load_vr_;
	}

	void VrController::FinalizeVrController()
	{
		if (!is_load_vr_)
		{
			return;
		}
		GlobalInfoDataCommon::Instance()->Finalize();
		together_builder_.clear();
		changes_.clear();
		changes_.shrink_to_fit();
		visitor_dispatchers_.clear();
		visitor_.clear();
		visitor_.shrink_to_fit();

		if (context_)
		{
			context_->Finilize();
			//context_ = nullptr;
		}
		is_load_vr_ = false;
	}

	void VrController::FinalizeEngine()
	{
		FinalizeVrController();
		LOGI("FinalizeVrController end");
		VrGlobalInfo::Instance()->get_dg_lib_mgr()->Finilize();
		context_ = nullptr;
	}

	void VrController::AddChange(const VrChangeArg& arg)
	{
		if (!IsExsitInChangedList(arg.part_type_))
		{
			changes_.push_back(arg);
		}
	}

	int VrController::DoChanges()
	{
		if (!IsLoadVr())
		{
			ClearChange();
			return 1;
		}

		GlobalInfoDataCommon::Instance()->GetFilePool()->ClearDownloadFileList();
		rse::vector<IVrVisitorPtr> notify_visitors;
		rse::vector<VrChangeArg> notify_args;

		rse::vector<VrChangeArg> append_changes;

		std::for_each(changes_.begin(), changes_.end(), [this, &append_changes](VrChangeArg& arg)
		{
			auto found_iter = together_builder_.find(arg.part_type_);
			if (found_iter != together_builder_.end())
			{
				rse::vector<PartTypeId>* together_types = &found_iter->second;

				auto together_types_iter = together_types->begin();
				auto together_types_iter_end = together_types->end();
				
				for( ; together_types_iter < together_types_iter_end ; ++together_types_iter )
				{
					PartTypeId id = *together_types_iter;
					if (!IsExsitInChangedList(id))
					{
						append_changes.push_back(arg.Clone(id, false));
					}
				}
			}
		});

		changes_.insert(changes_.end(), append_changes.begin(), append_changes.end());

		for (auto it = changes_.begin(), ie = changes_.end(); it != ie; ++it)
		{
			auto vr_type = GetVrVisitorType(it->part_type_);
			auto found_iter = visitor_dispatchers_.find((PartTypeId)vr_type);
			if (found_iter == visitor_dispatchers_.end())
			{
				int part_type = it->part_type_;
				auto part_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type);
				if (part_info && part_info->parent_ > 0)
				{
					found_iter = visitor_dispatchers_.find((PartTypeId)part_info->parent_);
				}
			}
			if (found_iter != visitor_dispatchers_.end())
			{
				IVrVisitorPtr visitor = found_iter->second;
				if (!visitor->UpdateVr(&(*it)))
				{
					GlobalInfoDataCommon::Instance()->LogErrorLn("PartType=%d, VrVisitor failed to update vr", it->part_type_);
					ClearChange();
					return 2;
				}

				if (it->need_notify_)
				{
					notify_visitors.push_back(visitor);
					notify_args.push_back(*it);

#if defined(RENDER_SKETCHER_HTML) || defined(RENDER_SKETCHER_ANDROID) || defined(RENDER_SKETCHER_APPLE)
					decoration_vr_interface::GlobalInfoDataCommon::Instance()->GetDownLoadAssist()->AddVRInfo(visitor, *it);
#endif
				}
			}
		}

		//PrintDataArrayXml(TSTR("D:\\TestToolsMainXmls"));

		if (GlobalInfoDataCommon::Instance()->GetFilePool()->HasError())
		{
			ClearChange();
			return 3;
		}

		if (!GlobalInfoDataCommon::Instance()->GetFilePool()->DownloadFiles())
		{
			ClearChange();
			GlobalInfoDataCommon::Instance()->GetFilePool()->ClearDownloadFileList();
			return 4;
		}

#if !defined(RENDER_SKETCHER_HTML) && !defined(RENDER_SKETCHER_ANDROID) && !defined(RENDER_SKETCHER_APPLE)
		for (int i = 0, s = notify_visitors.size(); i < s; ++i)
		{
			notify_visitors[i]->NotifyVr(&(notify_args[i]));
		}
#else
		decoration_vr_interface::GlobalInfoDataCommon::Instance()->GetDownLoadAssist()->SendMessage();
#endif

		ClearChange();

		return 0;
	}

	void VrController::ClearChange()
	{
		changes_.clear();
	}

	void VrController::Initialize(void* main_hwnd, void* parent_hwnd)
	{
		is_load_vr_ = true;
		VrGlobalInfo::Instance()->set_main_hwnd(main_hwnd);
		VrGlobalInfo::Instance()->set_parent_hwnd(parent_hwnd);
		
		if (CreateVrContext())
		{
			Initialize();
			LoadScene();
			InitializeScene();

			context_->SetTextureSizeIsPower2(true);
			context_->SetContextCallBack(ContextCallBack);
			context_->Play();
		}
	}

	void VrController::Finalize()
	{
		together_builder_.clear();
		changes_.clear();
		visitor_dispatchers_.clear();
		visitor_.clear();

		if (need_finalize_ && context_)
		{
			context_->Finilize();
			context_ = nullptr;
			VrGlobalInfo::Instance()->get_dg_lib_mgr()->Finilize();
		}

		VrGlobalInfo::Instance()->Finalize();
		VrGlobalInfo::Destory();
	}

	void VrController::RegisterVrVisitor(IVrVisitorPtr visitor_ptr)
	{
		visitor_.push_back(visitor_ptr);

		const rse::vector<PartTypeId>& available_ids = visitor_ptr->GetAvailablePartTypeId();
		std::for_each(available_ids.begin(), available_ids.end(), [this, visitor_ptr](PartTypeId part_type)
		{
			visitor_dispatchers_[part_type] = visitor_ptr;
		});
	}

	void VrController::NotifyChangeCameraRatio(float width, float height)
	{
		if (!is_load_vr_) return;

		Global_Setting_Parameter* setting_parameters = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		setting_parameters->Setscreen_X(0, width);
		setting_parameters->Setscreen_Y(0, height);
		SendMessageToVr(TSTR("msg_ratiochange"), 0);
	}

	void VrController::SendMessageToVr(const tchar* msg, int delay_frame)
	{
		VrGlobalInfo::Instance()->get_dg_user_msg_receiver()->SendVrMessage(msg, delay_frame);
	}

	void VrController::SendMessageToVr(const tchar* msg, MsgParaValue *arr, int arr_length, int delay_frame)
	{
		VrGlobalInfo::Instance()->get_dg_user_msg_receiver()->SendVrMessage(msg, arr, arr_length, delay_frame);
	}

	void VrController::DoVrProcess()
	{
		if (context_)
		{
			context_->DoVrProcess();
		}
	}
    
    bool VrController::InitializeForRenderFrameC()
    {
        bool ret = false;
        if (context_)
        {
			ret = context_->WaitRenderFrameC();
        }
        
        return ret;
    }
    
    int VrController::RenderFrameC()
    {
        int ret = 0;
        if (context_)
        {
            ret = context_->RenderFrameC();
        }
        
        return ret;
    }
    
	void VrController::PrintDataArrayXml(const tstring& path)
	{
		std::for_each(visitor_.begin(), visitor_.end(), [&path](IVrVisitorPtr visitor)
		{
			visitor->PrintData(path.c_str());
		});
	}

	void VrController::LoadVrVisitors()
	{
		dvi_tinyxml2::XMLDocument xml_doc;
		tstring vrvisitor_xmlpath = GetRealPath(TSTR("VrVisitorManager.xml"));

		const char* xml_content = context_->LoadFileToString(vrvisitor_xmlpath.c_str());
		int xml_error = xml_doc.Parse(xml_content);
		fnGetLigMgr()->ReleaseLibBuf(xml_content);

		if (xml_error)
		{
			DECORATION_ASSERT(false);
			return;
		}

		dvi_tinyxml2::XMLElement* xml_effect = xml_doc.RootElement();
		dvi_tinyxml2::XMLElement* xml_element = xml_effect->FirstChildElement("Item");
		while (xml_element)
		{
			const char* elem_val = xml_element->GetText();
			tstring vivistor_name = TSTR("Vr") + Util::StringToTString(elem_val) + TSTR("Visitor");
			auto visitor = GlobalInfoDataCommon::Instance()->get_vr_visitor_factory()->CreateVrVisitor(vivistor_name.c_str());
			if (visitor)
			{ 
				RegisterVrVisitor(visitor);
			}
			
			xml_element = xml_element->NextSiblingElement("Item");
		}
	}

	void VrController::LoadTogetherBuilder()
	{
		dvi_tinyxml2::XMLDocument xml_doc;
		if (!LoadVrConfigXml(xml_doc))
		{
			return;
		}

		dvi_tinyxml2::XMLElement* xml_effect = xml_doc.RootElement();
		dvi_tinyxml2::XMLElement* xml_parttype_element = xml_effect->FirstChildElement();
		auto part_type_manager = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
		while (xml_parttype_element)
		{
			const char* elem_name = xml_parttype_element->Name();
			tstring vivistor_name = Util::StringToTString(elem_name);
			auto type_info = part_type_manager->GetTypeInfo(vivistor_name);

			if (type_info)
			{
				PartTypeId visitor_part_type = (PartTypeId)type_info->id_;

				dvi_tinyxml2::XMLElement* xml_together_element = xml_parttype_element->FirstChildElement("ChangeTogether");
				if (xml_together_element)
				{
					dvi_tinyxml2::XMLElement* xml_together_item_element = xml_together_element->FirstChildElement();
					while (xml_together_item_element)
					{
						const char* item_name = xml_together_item_element->Name();
						tstring together_visitor_name = Util::StringToTString(item_name);
						PartTypeId together_visitor_part_type = (PartTypeId)part_type_manager->GetTypeInfo(together_visitor_name)->id_;
						together_builder_[visitor_part_type].push_back(together_visitor_part_type);
						xml_together_item_element = xml_together_item_element->NextSiblingElement();
					}
				}
			}

			xml_parttype_element = xml_parttype_element->NextSiblingElement();
		}
	}

	bool VrController::CreateVrContext(void* am)
	{
		auto lib_mgr = fnGetLigMgr();
		VrGlobalInfo::Instance()->set_dg_lib_mgr(lib_mgr);
		lib_mgr->Initialize(1);
		context_ = lib_mgr->GetContext();
		auto vr_glob = VrGlobalInfo::Instance();
        vr_glob->set_dg_context(context_);
#ifndef RENDER_SKETCHER_ANDROID
		IDGContext::RenderDeviceType device_type;
		rse::string hmd;
		ReadRenderSetting(device_type, hmd);
		bool ret = context_->CreateRender(static_cast<IDGContext::RenderDeviceParam*>(vr_glob->get_main_hwnd()), device_type, hmd.c_str());
#else
		rse::string hmd;
		bool ret = context_->CreateRender(static_cast<IDGContext::RenderDeviceParam*>(vr_glob->get_main_hwnd()), IDGContext::DG_RDT_GLES, hmd.c_str());
		if (ret && am)
		{
			context_->setAssertsManager(am);
			//if (SQLITE_OK != sqlite3_ndk_init((AAssetManager*)am))
			//{
			//	LOGI("sqlite3_ndk_init failed");
			//}
		}
#endif
		if (ret)
		{
			auto config_info = vr_glob->get_config_info();
			IDGPathManager *pathMgr = context_->GetPathManager();
			pathMgr->AddBitmapSearchPath(config_info->get_texture_dir().c_str());
			pathMgr->AddDataSearchPath(config_info->resouseFilePath.c_str());
			auto path = Util::CombinePath(config_info->resouseFilePath, TSTR("/media"));
			pathMgr->AddDataSearchPath(path.c_str());
			pathMgr->AddDataSearchPath("");
			path = Util::CombinePath(config_info->resouseFilePath, TSTR("/Model"));
			pathMgr->AddDataSearchPath(path.c_str());

			
		}

		return ret;
	}

	void VrController::LoadScene()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		auto lib_mgr = fnGetLigMgr();
		auto scene = lib_mgr->CreateVrScene(0, nullptr, false);
		vr_glob->set_dg_scene(scene);
		auto config_info = VrGlobalInfo::Instance()->get_config_info();
		auto path = Util::CombinePath(config_info->resouseFilePath, config_info->sceneFilePath);
		bool ss = scene->LoadFromFile(path.c_str());

		//LOGI("LoadScene path = %s ss = %d\n", path.c_str(), ss);

		vr_glob->Initialize(scene);
	}

	void VrController::InitializeScene()
	{
		auto vr_glob = VrGlobalInfo::Instance();
		vr_glob->set_dg_user_msg_receiver(vr_glob->get_dg_scene()->GetSceneMsgReceiver(TSTR("Message_Receiver")));
		std::for_each(visitor_.begin(), visitor_.end(), [](IVrVisitorPtr visitor)
		{
			visitor->Initialize();
		});
	}

	bool VrController::IsExsitInChangedList(PartTypeId parttype)
	{
		auto target = std::find_if(changes_.begin(), changes_.end(), [parttype](VrChangeArg enum_arg)
		{
			return enum_arg.part_type_ == parttype;
		});

		return target != changes_.end();
	}
	    
	tstring VrController::GetRealPath(const tstring& path)
	{
		bool is_esca = GlobalInfoDataCommon::Instance()->GetElevatorType() == kConstTypeEscalator;
		tstring res = (is_esca? TSTR("Esca") : TSTR("")) + path;

		auto resouce_file_path = VrGlobalInfo::Instance()->get_config_info()->resouseFilePath;
		if (resouce_file_path != TSTR(""))
		{
			res = resouce_file_path + TSTR("/") + res;
		}

		return res;
	}

	int VrController::GetVrVisitorType(int part_type)
	{
		switch (part_type)
		{
		case PartTypeFrontWall:
		case PartTypeCopWall:
		case PartTypeDoorHeader:
		case PartTypeEntranceColumn:
		case PartTypeFrontAccessory:
			return PartTypeFrontWall;

		case PartTypeCop:
		case PartTypeAuxCop:
		case PartTypeHDCop:
		case PartTypeAuxHDCop:

		case PartTypeCopButton:
		case PartTypeAuxCopButton:
		case PartTypeHDCopButton:
		case PartTypeAuxHDCopButton:
			return PartTypeCop;

		case PartTypeCopDisplay:
		case PartTypeAuxCopDisplay:
		case PartTypeHDCopDisplay:
			return PartTypeCopDisplay;

		case PartTypeLop:
		case PartTypeLopButton:
			return PartTypeLop;
		case PartTypeImportedCarDoor:
			return PartTypeCarDoor;
		case PartTypeImportedHallDoor:
			return PartTypeHallDoor;
		default:
			return part_type;
		}
	}

	IVrVisitor* VrController::GetVrVisitor(int part_type)
	{
		auto vt = GetVrVisitorType(part_type);
		for (auto it = visitor_.begin(), ie = visitor_.end(); it != ie; ++it)
		{
			auto p = static_cast<BaseVisitor*>(it->get());
			if (p->GetType() == vt)
			{
				return p;
			}
		}

		return nullptr;
	}

#if defined(XIZI_OTIS)
	void VrController::InitDTMap()
	{
		dt_map_.clear();

		dvi_tinyxml2::XMLDocument xml_doc;
		auto xml_path = GetRealPath(TSTR("xio.xml"));
		auto xml_content = context_->LoadFileToString(xml_path.c_str());
		int xml_error = xml_doc.Parse(xml_content);
		fnGetLigMgr()->ReleaseLibBuf(xml_content);
		if (xml_error)
		{
			return;
		}

		auto root = xml_doc.RootElement();
		auto item = root->FirstChildElement("Item");
		while (item)
		{
			auto val = item->IntAttribute("dt");
			auto e = item->FirstChildElement("GoodId");
			while (e)
			{
				//auto dt = item->IntAttribute("goodId");
				auto key = Util::AToInt64(e->Attribute("realVal"));
				dt_map_[key] = val;
				e = e->NextSiblingElement("GoodId");
			}
			item = item->NextSiblingElement("Item");
		}
	}
#endif

	StereoMode VrController::GetStereoMode()
	{
		return context_->GetStereoMode();
	}
	void VrController::SetStereoMode(StereoMode val)
	{
		context_->SetStereoMode(val);
	}

	float VrController::GetEyeDistance()
	{
		return context_->GetEyeDistance();
	}
	void VrController::SetEyeDistance(float val)
	{
		context_->SetEyeDistance(val);
	}

	float VrController::GetFocusLength()
	{
		return context_->GetFocusLength();
	}
	void VrController::SetFocusLength(float val)
	{
		context_->SetFocusLength(val);
	}

	void VrController::SetDefaultCameraAspectRatio(float ratio)
	{
		context_->SetDefaultCameraAspectRatio(ratio);
	}

	void VrController::ChangeCameraFace(CameraPos pos)
	{
		if (pos < CP_CAR_MAX)
		{
			VrGlobalInfo::Instance()->get_setting_parameter_array_visitor()->Setcam_Pos(0, pos);
			SendMessageToVr(TSTR("msg_fixview"), 0);
		}
		else
		{
			//VrGlobalInfo::Instance()->get_esca_setting_parameter_array_visitor()->SetFixedViewType(0, pos - CP_ESCALATOR_VIEW1);
			//SendMessageToVr(TSTR("msg_fixed_positon_change_animation"), 0);
		}
	}

	bool VrController::CreateSlaveRenderWindow(void* parentWnd)
	{
		return context_->CreateSlaveRenderWindow(parentWnd, 100, 100);
	}

	void VrController::ResizeRender(int w, int h)
	{
		if(context_) context_->ResizeRender(w, h);
	}

	void VrController::RenderLargeImage(const char* path, float width, float height)
	{
		if (context_)
		{
			context_->GetRenderLarge()->StartRenderLargePic(path, width, height);
		}
	}

	void VrController::OnExternalDeviceLost()
	{
		if (context_)
		{
			context_->OnExternalDeviceLost();
		}
	}

	void VrController::OnExternalDeviceReCreate(void* hwnd, int w, int h)
	{
		if (context_)
		{
			IDGContext::RenderDeviceParam param = { hwnd, w, h };
			context_->OnExternalDeviceReCreate(&param);
		}
	}

	void VrController::OnPlatformAsyncCallBack(int function_code, int wparam, int lparam, const char *extra_data)
	{
		if (context_)
		{
			context_->OnPlatformAsyncCallBack(function_code, wparam, lparam, extra_data);
		}
	}

    void* VrController::GetPluginInterfaceObject(const char *guid_text)
    {
        void *ret = nullptr;
        if (context_)
        {
            ret = context_->GetPluginInterfaceObject(guid_text);
        }
        
        return ret;
    }
    
	void VrController::WaitRenderThreadQuited()
	{
		if (context_)
		{
			context_->WaitRenderThreadQuited();
		}
	}

	void VrController::SignalRenderThreadQuited()
	{
		if (context_)
		{
			context_->SignalRenderThreadQuited();
		}
	}

	/*void VrController::ExitRenderThread()
	{
		//if (context_) context_->ExitRenderThread();
	}*/

	bool VrController::LoadVrConfigXml(dvi_tinyxml2::XMLDocument &xml_doc)
	{
		auto together_xmlpath = GetRealPath(TSTR("VrConfig.xml"));

		const char* xml_content = context_->LoadFileToString(together_xmlpath.c_str());
		int xml_error = xml_doc.Parse(xml_content);
		fnGetLigMgr()->ReleaseLibBuf(xml_content);

		if (xml_error)
		{
			DECORATION_ASSERT(false);
			return false;
		}

		return true;
	}

	void VrController::ReadRenderSetting(IDGContext::RenderDeviceType &dt, rse::string &hmd)
	{
		dvi_tinyxml2::XMLDocument xml_doc;
		if (!LoadVrConfigXml(xml_doc))
		{
			return;
		}
		dvi_tinyxml2::XMLElement* xml_effect = xml_doc.RootElement();
		rse::string dtn = "GLES";

		auto p = xml_effect->Attribute("device_type");
		if (p)
		{
			dtn = p;
		}

		p = xml_effect->Attribute("hmd");
		if (p)
		{
			hmd = p;
		}

		if (dtn == "D3D11")
		{
			dt = IDGContext::DG_RDT_D3D11;
		}
		else if (dtn == "D3D9")
		{
			dt = IDGContext::DG_RDT_D3D9;
		}
		else
		{
			dt = IDGContext::DG_RDT_GLES;
		}
	}

	void VrController::SetPlatformOperation(void* operation)
	{
		if (context_)
		{
			context_->SetPlatformOperation(operation);
		}
	}

	/*void VrController::SetLogMark(int mark)
	{
		//if (context_) context_->SetLogMark(mark);
	}
	void VrController::PrintLogByMark(int mark, const char *fold_path)
	{
		//if (context_) context_->PrintLogByMark(mark, fold_path);
	}
	void VrController::SetValidCounterRang(bool isEnable, int min, int max)
	{
		//if (context_) context_->SetValidCounterRang(isEnable, min, max);
	}
	void VrController::StopLogCallStack()
	{
		//if (context_) context_->StopLogCallStack();
	}*/
}
