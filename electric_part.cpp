#include "stdafx.h"
#include "electric_part.h"

#include "file_pool.h"

namespace decoration_vr_interface
{
	ElectricPart::ElectricPart()
		:part_type_(0), part_id_(0), part_name_(TSTR("")),
		panel_type_(0), setup_orientation_(0), pos_x_(0.0f), pos_y_(0.0f), 
		lcd_(0), button_(0), lcd_type_(0), button_type_(0), is_reflect_(false)
	{
	}


	ElectricPart::~ElectricPart()
	{
	}

	int ElectricPart::GetPartType()
	{
		return part_type_;
	}

	int64 ElectricPart::GetPartId()
	{
		return part_id_;
	}

	tstring ElectricPart::GetPartName()
	{
		return part_name_;
	}

	void ElectricPart::SetPartName(const tstring &new_name)
	{
		if (part_name_ != new_name)
		{
			part_name_ = new_name;
		}
	}

	tstring ElectricPart::GetLcdPath()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(lcd_type_, lcd_, size_id, 0, false);
	}

	tstring ElectricPart::LoadModel()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(part_type_, part_id_, size_id, 0);
	}

	bool ElectricPart::IsValidPart()
	{
		auto gd = GlobalInfoDataCommon::Instance();

		if (part_id_ <= 0)
		{
			if (part_type_ == PartTypeHallFireBox || panel_type_ == PartTypeHallIndicator || panel_type_ == PartTypeLantern
				|| panel_type_ == PartTypeEMIDS)
			{
				return true;
			}
			gd->LogErrorLn("ElectricPart isn't valid part: type=%d, part id < 0", part_type_);
			return false;
		}

		if (lcd_type_ > 0 && lcd_ <= 0)
		{
			gd->LogErrorLn("ElectricPart LCD isn't valid part: part_type=%d, part id=%ld, lcd type=%d", 
				part_type_, part_id_, lcd_type_);
			return false;
		}

		if (button_type_ > 0 && button_ <= 0)
		{
			gd->LogErrorLn("ElectricPart Button isn't valid part: part_type=%d, part id=%ld, button type=%d",
				part_type_, part_id_, button_type_);
			return false;
		}

		for (auto it = editpart_materials_.begin(), ie = editpart_materials_.end(); it != ie; ++it)
		{
			if (it->second <= 0)
			{
				gd->LogErrorLn("ElectricPart's material isn't valid part: type=%d, part id=%ld, material id for edit part id=%d less 0",
					part_type_, part_id_, it->first);
				return false;
			}
		}

		return true;
	}

	int64 ElectricPart::GetPartMaterial()
	{
		return GetEditPartMaterial(1);
	}

	void ElectricPart::SetPartMaterial(int64 mat)
	{
		SetEditPartMaterial(1, mat);
	}

	int64 ElectricPart::GetEditPartMaterial(int id)
	{
		auto it = editpart_materials_.find(id);
		if (it != editpart_materials_.end())
		{
			return it->second;
		}

		return -1;
	}

	bool ElectricPart::SetEditPartMaterial(int id, int64 mat)
	{
		auto it = editpart_materials_.find(id);
		if (it != editpart_materials_.end())
		{
			it->second = mat;
			return true;
		}
		return false;
	}

	tstring ElectricPart::GetButtonPath()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(button_type_, button_, size_id, 0);
	}

	bool ElectricPart::GetIsHasReflect()
	{
		return is_reflect_;
	}

	void ElectricPart::SetIsHasReflect(const bool &is_reflect)
	{
		is_reflect_ = is_reflect;
	}
}