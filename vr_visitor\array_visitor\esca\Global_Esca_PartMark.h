#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_Esca_PartMark : public DGBaseVisitor
		{
		public:
			Global_Esca_PartMark(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			int Getarr_PartMark(int row);
			void Setarr_PartMark(int row, int val);

			float Getfloat_offsetX(int row);
			void Setfloat_offsetX(int row, float val);

			bool GetSelect(int row);
			void SetSelect(int row, bool val);

		};
	}
}