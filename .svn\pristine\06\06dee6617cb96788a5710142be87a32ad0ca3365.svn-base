#ifndef _VR_EscaTrussLightingVisitor_H_
#define _VR_EscaTrussLightingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Truss_Lighting.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaTrussLightingVisitor : public BaseVisitor
	{
	public:
		VrEscaTrussLightingVisitor();
		virtual ~VrEscaTrussLightingVisitor();

		DEFINE_CREATE_FUN(VrEscaTrussLightingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Truss_Lighting> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
