#pragma once

#include "IVrVisitor.h"

namespace decoration_vr_interface
{
#define DEFINE_CREATE_FUN(class_type)\
static IVrVisitorPtr Create()\
{\
	return RSE_MAKE_SHARED<class_type>();\
}

#define ADD_VISITOR_FACTORY_CALL_BACK(class_type)\
GlobalInfoDataCommon::Instance()->get_vr_visitor_factory()->Register(TSTR(#class_type), &class_type::Create);

	class BaseVisitor : public IVrVisitor
	{
	public:
		BaseVisitor();
		~BaseVisitor();

		void InitializeAvailablePartTypes(const rse::vector<PartTypeId>& typs);

		virtual int GetType() override;
		virtual void Initialize() override;
		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void NotifyVr(VrChangeArg* vr_change) override;
		virtual const rse::vector<PartTypeId>& GetAvailablePartTypeId() override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		void AddFileToModelCache(int part_type, int64 id, const tstring& file, const tstring& local_path);

		PartTypeId part_type_;
		rse::vector<PartTypeId> available_parttypeid_list_;
	};
}
