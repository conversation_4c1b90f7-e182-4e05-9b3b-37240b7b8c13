#include "stdafx.h"
#include "EscaSideCladdingFactory.h"

#include "esca_elevator_part.h"

namespace decoration_vr_interface
{

	decoration_vr_interface::IElevatorPartPtr EscaSideCladdingFactory::GetPart(FactoryArgsOfLoadingFromDb* args)
	{
		if (args == nullptr)
		{
			return nullptr;
		}
		else
		{
			auto part = RSE_MAKE_SHARED<EscaSideCladding>();
			part->part_type_ = part_type_;
			part->part_id_ = args->part_id_;

			//RseDataBaseRecordSet rs = db_visitor::DBVisitorMgr::Instance()->GetVisitor()->LoadEscaPart(part->part_id_, part->part_type_);
			//if (!rs.IsEof())
			//{
			//	part->mat_id_ = rs.GetIntField("UC_MATERIAL_ID");
			//	part->piece_pattern_ = rs.GetIntField("UC_PART_IDATA1");
			//	part->pit_pattern_ = rs.GetIntField("UC_PART_IDATA2");
			//	part->lighting_pattern_ = rs.GetIntField("UC_PART_SDATA1");
			//}

			return part;
		}
	}

	decoration_vr_interface::IElevatorPartPtr EscaSideCladdingFactory::GetPart(FactoryArgsOfConfigLoading* args)
	{
		if (args == nullptr)
		{
			return nullptr;
		}
		else
		{
			auto iter = std::find_if(args->items_.begin(), args->items_.end(), [this](const ConfigDataItemPtr& item) {
				return item->part_type_ == this->part_type_;
			});
			if (iter == args->items_.end()) return nullptr;

			auto part = RSE_MAKE_SHARED<EscaSideCladding>();
			part->part_type_ = part_type_;
			part->part_id_ = iter->get()->part_id_;
			//part->mat_id_ = iter->get()->part_mat_id_;

			//rse::map<tstring, int> extra_data_map;
			//ParseExtraData(extra_data_map, iter->get()->extra_data_);
			//part->piece_pattern_ = extra_data_map[TSTR("EscaSideCladdingPiecePattern")];
			//part->pit_pattern_ = extra_data_map[TSTR("EscaSideCladdingPitPattern")];
			//part->lighting_pattern_ = extra_data_map[TSTR("EscaSideCladdingLightingPattern")];
			return part;
		}
	}

}