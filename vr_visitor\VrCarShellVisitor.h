#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Car_Shell;
	class MaterialChannel;
	class Sel_Hall_Door_Window;
	class VrCarShellVisitor : public BaseVisitor
	{
	public:
		VrCarShellVisitor();
		~VrCarShellVisitor();

		DEFINE_CREATE_FUN(VrCarShellVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	private:
#if defined(XIZI_OTIS)
		void SetCarShellGlassDoor(IConfig* config);
#endif
	protected:
		std::shared_ptr<Sel_Car_Shell> model_;
		std::shared_ptr<MaterialChannel> material_;
		std::shared_ptr<Sel_Hall_Door_Window> sel_carshell_glassdoor_;
	};

}