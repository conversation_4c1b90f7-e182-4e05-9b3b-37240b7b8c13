#include "stdafx.h"
#include "car_top.h"

#include "GlobalInfoDataCommon.h"
#include "file_pool.h"

namespace decoration_vr_interface
{
	CarTop::CarTop()
		:part_id_(-1), height_(-1), thickness_(-1)
		, front_height_(-1), left_height_(-1)
		, right_height_(-1), back_height_(-1), is_reflect_(false)
	{
		editparts_.clear();
		editpart_materials_.clear();
	}


	CarTop::~CarTop()
	{        
	}

	int CarTop::GetPartType()
	{
		return PartTypeTop;
	}

	int64 CarTop::GetPartId()
	{
		return part_id_;
	}

	tstring CarTop::GetPartName()
	{
		return TSTR("");
	}

	void CarTop::SetPartName(const tstring &new_name)
	{

	}

	tstring CarTop::LoadModel()
	{
		auto size_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize()->id_;
		return GlobalInfoDataCommon::Instance()->GetFilePool()->GetModelPath(GetPartType(), GetPartId(), size_id, 0);
	}

	bool CarTop::IsValidPart()
	{
		auto gd = GlobalInfoDataCommon::Instance();
		if (part_id_ <= 0)
		{
			gd->LogErrorLn("Cartop isn't valid : part id < 0");
			return false;
		}

		if (thickness_ < 0)
		{
			gd->LogErrorLn("Cartop isn't valid, part id = %ld: thickness < 0", part_id_);
			return false;
		}
		if (height_ <= 0 || left_height_ <= 0 || right_height_ <= 0
			|| front_height_ <= 0 || back_height_ <= 0)
		{
			gd->LogErrorLn("Cartop isn't valid, part id = %ld: one of heigths < 0", part_id_);
			return false;
		}

		for (auto it = editpart_materials_.begin(), ie = editpart_materials_.end(); it != ie; ++it)
		{
			if (it->second <= 0)
			{
				gd->LogErrorLn("Cartop's material isn't valid part: part id=%ld, material id for edit part id=%d less 0",
					part_id_, it->first);
				return false;
			}
		}

		return true;
	}

	int64 CarTop::GetEditPartMaterial(int editpart_id)
	{
		auto it = editpart_materials_.find(editpart_id);
		if (it != editpart_materials_.end())
		{
			return it->second;
		}
		return -1;
	}

	bool CarTop::SetEditPartMaterial(int editpart_id, int64 mat)
	{
		auto it = editpart_materials_.find(editpart_id);
		if (it != editpart_materials_.end())
		{
			it->second = mat;
			return true;
		}
		return false;
	}

	bool CarTop::GetIsHasReflect()
	{
		return is_reflect_;
	}

	void CarTop::SetIsHasReflect(const bool &is_reflect)
	{
		is_reflect_ = is_reflect;
	}
}
