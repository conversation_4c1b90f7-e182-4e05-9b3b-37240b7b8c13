#ifndef _VR_EscaTrafficLightVisitor_H_
#define _VR_EscaTrafficLightVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Traffic_Light.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaTrafficLightVisitor : public BaseVisitor
	{
	public:
		VrEscaTrafficLightVisitor();
		virtual ~VrEscaTrafficLightVisitor();

		DEFINE_CREATE_FUN(VrEscaTrafficLightVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Traffic_Light> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
