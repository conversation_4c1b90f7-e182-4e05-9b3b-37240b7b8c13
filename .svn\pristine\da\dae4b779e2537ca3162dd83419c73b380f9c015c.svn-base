#include "stdafx.h"
#include "Global_Escalators_Rung_Parameter.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		bool Global_Escalators_Rung_Parameter::GetSelect(int row)
		{
			return visitor_->GetElementBool(row, 0);
		}

		void Global_Escalators_Rung_Parameter::SetSelect(int row, bool val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		int Global_Escalators_Rung_Parameter::Getstates(int row)
		{
			return visitor_->GetElementInt(row, 3);
		}

		void Global_Escalators_Rung_Parameter::Setstates(int row, int val)
		{
			visitor_->SetElementValue(row, 3, val);
		}
	}

}