#include "stdafx.h"
#include "svr_json_helper.h"

#include "Util.h"

namespace svr_data
{
	bool GetJsonValue(const Json::Value& jv, rse::string& val, bool allow_empty)
	{
		if (allow_empty)
		{
			val = decoration_vr_interface::Util::ToTString(jv.asString());
			return true;
		}

		if (jv.isString())
		{
			val = decoration_vr_interface::Util::ToTString(jv.asString());
			return true;
		}
			
		return false;
	}

	bool GetJsonValue(const Json::Value& jv, double& val)
	{
		if (jv.isDouble())
		{
			val = jv.asDouble();
			return true;
		}

		return false;
	}

	bool GetJsonValue(const Json::Value& jv, float& val)
	{
		if (jv.isDouble())
		{
			val = jv.asFloat();
			return true;
		}

		return false;
	}

	bool GetJsonValue(const Json::Value& jv, int64_t& val)
	{
		if (jv.isInt64())
		{
			val = jv.asInt64();
			return true;
		}

		return false;
	}

	bool GetJsonValue(const Json::Value& jv, int32_t& val)
	{
		if (jv.isInt())
		{
			val = jv.asInt();
			return true;
		}

		return false;
	}

	bool IsNullJsonValue(const Json::Value& jv)
	{
		return jv.isNull();
	}

	bool GetJsonValue(const Json::Value& jv, bool& val)
	{
		if (jv.isBool())
		{
			val = jv.asBool();
			return true;
		}

		return false;
	}

}
