#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_FireBox;
class MaterialChannel;

class VrHallFireBoxVisitor : public BaseVisitor
{
public:
	VrHallFireBoxVisitor();
	~VrHallFireBoxVisitor();

	DEFINE_CREATE_FUN(VrHallFireBoxVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:
	int GetSetupPos(int pos, int ele_id);
	std::shared_ptr<Sel_FireBox> model_;
	std::shared_ptr<MaterialChannel> material_;
};
}
