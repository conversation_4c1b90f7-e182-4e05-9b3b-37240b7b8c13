#pragma once

#include "DGBaseVisitor.h"
namespace decoration_vr_interface
{
	class Sel_Car_Shell : public DGBaseVisitor
	{
	public:
		Sel_Car_Shell(IDGSceneEx* scene, const tchar* arr_name);
			
		bool Get_Select(int row);
		void Set_Select(int row, bool val);

		rse::string Get_Path(int row);
		void Set_Path(int row, const tchar* val);

		//int Get_Mark(int row);
		//void Set_Mark(int row, int val);
	};

	class Sel_Hall_Door_Window : public DGBaseVisitor
	{
	public:
		Sel_Hall_Door_Window(IDGSceneEx* scene, const tchar* arr_name);

		bool GetIsHave(int row);
		void SetIsHave(int row, bool val);

		rse::string GetPath(int row);
		void SetPath(int row, const tchar* val);

		int GetGlassDoorType(int row);
		void SetGlassDoorType(int row, int val);
	};
}

