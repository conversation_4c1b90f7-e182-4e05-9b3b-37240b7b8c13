//
//  Sel_Hall.h
//  VrVisitor
//
//  Created by vrprg on 17:21:14.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_Hall : public DGBaseVisitor
{
public:
	Sel_Hall(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	rse::string GetPath(int row);
	void SetPath(int row, const tchar* val);

};
}