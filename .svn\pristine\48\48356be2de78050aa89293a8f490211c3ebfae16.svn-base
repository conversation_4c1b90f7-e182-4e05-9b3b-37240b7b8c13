#include "stdafx.h"
#include "decoration_vr_interface_lib.h"
#include "decoration_vr_interface.h"
#include "GlobalInfoDataCommon.h"
#include "VrGlobalInfo.h"

#if defined(RENDER_SKETCHER_HTML)
#include "html_decoration_vr_interface.h"
#endif


namespace decoration_vr_interface
{
#if defined(RENDER_SKETCHER_HTML)
	std::shared_ptr<HTMLDecorationVrInterface> html_vr_interface;
	DECORATION_VR_INTERFACE_API IHTMLDecorationVrInterface* GetHTMLDecorationVrInterface()
	{
		if(!html_vr_interface)
		{
			html_vr_interface  = RSE_MAKE_SHARED<HTMLDecorationVrInterface>();
		}

		return html_vr_interface.get();
	}
	DECORATION_VR_INTERFACE_API void DeleteHTMLDecorationVrInterface()
	{
		html_vr_interface.reset();
	}
#endif
    std::shared_ptr<DecorationVrInterface> vr_interface;

	DECORATION_VR_INTERFACE_API IDecorationVrInterface* GetDecorationVrInterface()
	{
        if(!vr_interface)
        {
            vr_interface  = RSE_MAKE_SHARED<DecorationVrInterface>();
        }
        
		return vr_interface.get();
	}
    
    DECORATION_VR_INTERFACE_API void DeleteDecorationVrInterface()
    {
        vr_interface.reset();
    }
    
	DECORATION_VR_INTERFACE_API IDecorationVrInterfaceExtend* GetDecorationVrInterfaceExtend()
	{		
		return (IDecorationVrInterfaceExtend*)vr_interface.get();
	}

	IDGContext * GlobalInfoDataCommon::GetDGContext()
	{
		if (vr_interface)
		{
			return vr_interface->GetDGContext();
		}
		else
		{
			return nullptr;
		}
	}

	IDecorationVrCallBack *GlobalInfoDataCommon::GetVrCallBack()
	{
		if (vr_interface)
		{
			return vr_interface->GetVrCallBack();
		}
		else
		{
			return nullptr;
		}
	}
}
