#pragma once
#include "IElevatorConfig.h"
#include "IConfig.h"
namespace decoration_vr_interface
{
	class ElevatorConfig :
		public IElevatorConfig
	{
	public:
		ElevatorConfig();
		~ElevatorConfig();

		virtual IConfigPtr GetCarConfig() override;
		virtual void SetCarConfig(IConfigPtr material) override;

		virtual IConfigPtr GetHallConfig(int pos) override;
		virtual void SetHallConfig(int pos, IConfigPtr material) override;

		virtual IConfigPtr GetEscalatorConfig() override;
		virtual void SetEscalatorConfig(IConfigPtr material) override;

		virtual ElevatorSize* GetElevatorSize() override;
		virtual void SetElevatorSize(const ElevatorSize& size) override;

		virtual EscalatorSize* GetEscalatorSize() override;
		virtual void SetEscalatorSize(const EscalatorSize& size) override;

		virtual ElevatorSetting* GetElevatorSetting() override;
		virtual void SetElevatorSetting(const ElevatorSetting& setting) override;

		virtual ElevatorSetting* GetEscalatorSetting() override;
		virtual void SetEscalatorSetting(const ElevatorSetting& setting) override;

	protected:
		IConfigPtr car_;
		IConfigPtr hall_;
		IConfigPtr escalator_;

		ElevatorSize elevator_size_;
		EscalatorSize escalator_size_;
		ElevatorSetting elevator_setting_;
		ElevatorSetting escalator_setting_;
	};
}
