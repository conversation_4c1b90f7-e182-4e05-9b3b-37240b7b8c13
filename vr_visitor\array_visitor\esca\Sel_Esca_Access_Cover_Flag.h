#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Sel_Esca_Access_Cover_Flag : public DGBaseVisitor
		{
		public:
			Sel_Esca_Access_Cover_Flag(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			rse::string Getstr_Path(int row);
			void Setstr_Path(int row, const tchar* val);

			float Getfloat_Position_X(int row);
			void Setfloat_Position_X(int row, float val);

			float Getfloat_Position_Y(int row);
			void Setfloat_Position_Y(int row, float val);

			float Getfloat_Size_X(int row);
			void Setfloat_Size_X(int row, float val);

			float Getfloat_Size_Y(int row);
			void Setfloat_Size_Y(int row, float val);

		};
	}

}