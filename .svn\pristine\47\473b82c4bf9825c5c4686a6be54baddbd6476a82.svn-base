//
//  Global_Hwndmsg.h
//  VrVisitor
//
//  Created by vrprg on 11:32:56.//  Copyright (c) 2015??? vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{

	class Global_Hwndmsg : public DGBaseVisitor
	{
	public:
		Global_Hwndmsg(IDGSceneEx* scene, const tchar* arr_name)
			:DGBaseVisitor(scene, arr_name) {}

		int Gethwnd(int row);
		void Sethwnd(int row, int val);

		int Getmsg(int row);
		void Setmsg(int row, int val);

		int Getcampos(int row);
		void Setcampos(int row, int val);

		int GetPartChange(int row);
		void SetPartChange(int row, int val);

		int GetCam(int row);
		void SetCam(int row, int val);

		int Geterror(int row);
		void Seterror(int row, int val);

		int Getcol6(int row);
		void Setcol6(int row, int val);

	};
}