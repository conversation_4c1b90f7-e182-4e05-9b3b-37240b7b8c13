//
//  Material_Cop.h
//  VrVisitor
//
//  Created by vrprg on 10:25:03.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//

#include "stdafx.h"
#include "material_channel.h"

namespace decoration_vr_interface
{

int MaterialChannel::GetPartType(int row)
{
    return visitor_->GetElementInt(row, 0);
}

void MaterialChannel::SetPartType(int row, int val)
{
    visitor_->SetElementValue(row, 0, val);
}

int MaterialChannel::GetPartId(int row)
{
    return visitor_->GetElementInt(row, 1);
}

void MaterialChannel::SetPartId(int row, int val)
{
    visitor_->SetElementValue(row, 1, val);
}

int MaterialChannel::GetChannelId(int row)
{
    return visitor_->GetElementInt(row, 2);
}

void MaterialChannel::SetChannelId(int row, int val)
{
    visitor_->SetElementValue(row, 2, val);
}

int MaterialChannel::GetMaterialType(int row)
{
    return visitor_->GetElementInt(row, 3);
}

void MaterialChannel::SetMaterialType(int row, int val)
{
    visitor_->SetElementValue(row, 3, val);
}

rse::string MaterialChannel::GetChannelColor(int row)
{
    const char* val = visitor_->GetElementCompound(row, 4, PTG_COLOR);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void MaterialChannel::SetChannelColor(int row, const tchar* val)
{
    visitor_->SetElementCompound(row, 4, PTG_COLOR, val);
}

rse::string MaterialChannel::GetReflectColor(int row)
{
    const char* val = visitor_->GetElementCompound(row, 5, PTG_COLOR);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void MaterialChannel::SetReflectColor(int row, const tchar* val)
{
    visitor_->SetElementCompound(row, 5, PTG_COLOR, val);
}

rse::string MaterialChannel::GetAmbientColor(int row)
{
    const char* val = visitor_->GetElementCompound(row, 6, PTG_COLOR);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void MaterialChannel::SetAmbientColor(int row, const tchar* val)
{
    visitor_->SetElementCompound(row, 6, PTG_COLOR, val);
}

int MaterialChannel::GetBlendMode(int row)
{
    return visitor_->GetElementInt(row, 7);
}

void MaterialChannel::SetBlendMode(int row, int val)
{
    visitor_->SetElementValue(row, 7, val);
}

rse::string MaterialChannel::GetTexture(int row)
{
    const char* val = visitor_->GetElementString(row, 8);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void MaterialChannel::SetTexture(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 8, val);
}

int MaterialChannel::GetTexMipLevel(int row)
{
    return visitor_->GetElementInt(row, 9);
}

void MaterialChannel::SetTexMipLevel(int row, int val)
{
    visitor_->SetElementValue(row, 9, val);
}

int MaterialChannel::GetTexSpecial(int row)
{
    return visitor_->GetElementInt(row, 10);
}

void MaterialChannel::SetTexSpecial(int row, int val)
{
    visitor_->SetElementValue(row, 10, val);
}

int MaterialChannel::GetTexMapMode(int row)
{
    return visitor_->GetElementInt(row, 11);
}

void MaterialChannel::SetTexMapMode(int row, int val)
{
    visitor_->SetElementValue(row, 11, val);
}

float MaterialChannel::GetTexXOffset(int row)
{
    return visitor_->GetElementFloat(row, 12);
}

void MaterialChannel::SetTexXOffset(int row, float val)
{
    visitor_->SetElementValue(row, 12, val);
}

float MaterialChannel::GetTexYOffset(int row)
{
    return visitor_->GetElementFloat(row, 13);
}

void MaterialChannel::SetTexYOffset(int row, float val)
{
    visitor_->SetElementValue(row, 13, val);
}

float MaterialChannel::GetTexXCount(int row)
{
    return visitor_->GetElementFloat(row, 14);
}

void MaterialChannel::SetTexXCount(int row, float val)
{
    visitor_->SetElementValue(row, 14, val);
}

float MaterialChannel::GetTexYCount(int row)
{
    return visitor_->GetElementFloat(row, 15);
}

void MaterialChannel::SetTexYCount(int row, float val)
{
    visitor_->SetElementValue(row, 15, val);
}

rse::string MaterialChannel::GetChannelMap(int row)
{
    const char* val = visitor_->GetElementString(row, 16);
    rse::string ret = val;
    fnGetLigMgr()->ReleaseLibBuf(val);
    return ret;
}

void MaterialChannel::SetChannelMap(int row, const tchar* val)
{
    visitor_->SetElementValue(row, 16, val);
}

int MaterialChannel::GetMaskMipLevel(int row)
{
    return visitor_->GetElementInt(row, 17);
}

void MaterialChannel::SetMaskMipLevel(int row, int val)
{
    visitor_->SetElementValue(row, 17, val);
}

int MaterialChannel::GetMaskSpecial(int row)
{
    return visitor_->GetElementInt(row, 18);
}

void MaterialChannel::SetMaskSpecial(int row, int val)
{
    visitor_->SetElementValue(row, 18, val);
}

int MaterialChannel::GetMaskMapMode(int row)
{
    return visitor_->GetElementInt(row, 19);
}

void MaterialChannel::SetMaskMapMode(int row, int val)
{
    visitor_->SetElementValue(row, 19, val);
}

float MaterialChannel::GetMaskXOffset(int row)
{
    return visitor_->GetElementFloat(row, 20);
}

void MaterialChannel::SetMaskXOffset(int row, float val)
{
    visitor_->SetElementValue(row, 20, val);
}

float MaterialChannel::GetMaskYOffset(int row)
{
    return visitor_->GetElementFloat(row, 21);
}

void MaterialChannel::SetMaskYOffset(int row, float val)
{
    visitor_->SetElementValue(row, 21, val);
}

float MaterialChannel::GetMaskXCount(int row)
{
    return visitor_->GetElementFloat(row, 22);
}

void MaterialChannel::SetMaskXCount(int row, float val)
{
    visitor_->SetElementValue(row, 22, val);
}

float MaterialChannel::GetMaskYCount(int row)
{
    return visitor_->GetElementFloat(row, 23);
}

void MaterialChannel::SetMaskYCount(int row, float val)
{
    visitor_->SetElementValue(row, 23, val);
}

int MaterialChannel::GetMaterialMark(int row)
{
    return visitor_->GetElementInt(row, 24);
}

void MaterialChannel::SetMaterialMark(int row, int val)
{
    visitor_->SetElementValue(row, 24, val);
}

}