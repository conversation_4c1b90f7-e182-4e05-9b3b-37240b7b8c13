#ifndef _VR_HALLCONFIG_VISITOR_H_
#define _VR_HALLCONFIG_VISITOR_H_

#include "BaseVisitor.h"

namespace decoration_vr_interface
{
	class Sel_Hall;
	class VrHallConfigVisitor :public BaseVisitor
	{
	public:
		VrHallConfigVisitor();
		virtual ~VrHallConfigVisitor();

		DEFINE_CREATE_FUN(VrHallConfigVisitor);
		virtual void Initialize() override;
		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<Sel_Hall> model_;
	};
}
#endif//_VR_HALLCONFIG_VISITOR_H_
