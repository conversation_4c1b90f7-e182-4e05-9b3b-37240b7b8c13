﻿#pragma once
#ifdef UNICODE
typedef wchar_t tchar;
#else
typedef char tchar;
#endif

#include <string>
#include "part_type.h"
#include "i_decoration_vr_callback.h"
#include "MsgParaValue.h"
namespace decoration_vr_interface
{
	//typedef rse::string (*FileLoadCallBack)(const char* filename);

	class DECORATION_VR_INTERFACE_API IDecorationVrInterface
	{
	public:
		enum InitType
		{
			IT_NEED_CREATE,
			IT_NEED_INIT,
			IT_DIRECT_USE
		};
	public:
		virtual bool IsInitialize() = 0;
		virtual void InitializeElevator(void* hwnd, int flag) = 0;
		virtual void InitializeElevator(void* hwnd, int elev_size, int width, int depth, int door_width, int door_height, int flag) = 0;
		virtual void InitializeElevator(void* hwnd, const ElevatorSpecification* elev_spec, int flag) = 0;
		virtual void InitializeElevator(void* hwnd, void* am, const ElevatorSpecification* elev_spec, int flag) = 0;
		virtual void SetDir(const char* app_dir, const char* home_dir) = 0;
		virtual void InitializeEscalator(void* hwnd, int width, int height, int flag) = 0;
		virtual void ChangeElevatorSpecification(const ElevatorSpecification* elev_spec) = 0;
		virtual void ChangeEscalatorSpecification(int size_id, int width, int height) = 0;
		virtual void Finalize() = 0;

		virtual bool InitEngine() = 0;
		virtual void SetResourcesPath(const char* path) = 0;
		//device_type = 0：NULL Device
		//other:depend on platform
		virtual bool CreateRender(void* hwnd, int w, int h, int device_type) = 0;
		virtual void LoadScene() = 0;
		virtual void InitElevator(const ElevatorSpecification* elev_spec) = 0;
		virtual void UnloadScene() = 0;
		virtual void FinalizeEngine() = 0;
		virtual bool DownloadFileCompleted(const char* file) = 0;

		//virtual void SetPartByVersionId(int part_type, int64 version_id, bool notify) = 0;
		//virtual void SetMaterialByVersionId(int part_type, int64 version_id, bool notify) = 0;
		//virtual bool SetSubitemMaterialByVersionId(int part_type, int part_mark, int64 version_id, bool notify) = 0;
		//virtual bool SetWallElemMaterialByVersionId(int part_type, int elem_id, int64 version_id, bool notify) = 0;

		virtual void SetPart(int part_type, int64 part_id, bool notify) = 0;
		virtual void SetMaterial(int part_type, int64 material_id, bool notify) = 0;
		virtual void SetHandrailPos(int/*HandrailSetupPos*/ pos, bool notify) = 0;
		//virtual void SetIsHasAuxCop(bool has, bool notify) = 0;
		//virtual void SetIsHasHdCop(bool has, bool notify) = 0;
		//virtual void SetIsHasAuxHdCop(bool has, bool notify) = 0;
		virtual void SetCopButtonCount(int btn_count, bool notify) = 0;
		virtual bool SetSubitemMaterial(int part_type, int part_mark, int64 material_id, bool notify) = 0;

		virtual void SetElectricPartOrientation(int part_type, int orient, bool notify) = 0;
		virtual void SetElectricPartPos(int part_type, float x, float y, bool notify) = 0;
		//elevator_id: -1, current elevator; range, [0, elevator_count]
		virtual void SetDoorOpenType(int door_type, int elevator_id, bool notify) = 0;

		//virtual bool UpdateConstructByElemCount(int part_type, int unit_count) = 0;

		//set handrail pos mark
		virtual void SetHandrailPosMark(int ori, int mark) = 0;
		//////////////////////////////////////////////////////////////////////////
		
		virtual void Resize(float width, float height) = 0;
		virtual void NotifyVr() = 0;
        virtual bool DoChange(int part_type, bool need_notify) = 0;

		//virtual void SetSkipDoChange(bool val) = 0;
		//virtual bool IsSkipDoChange() = 0;

		virtual void AutoSetFCopSetupPos(bool val) = 0;
		virtual bool IsAutoSetFCopSetupPos() = 0;

		//////////////////////////////////////////////////////////////////////////
		virtual void SetRemoveSkirting(bool val) = 0;
		virtual bool IsRemoveSkirting() = 0;

		virtual void SetMirrorSetupInfo(int orient, float x, float y, bool notify) = 0;
		virtual void ClearOptionalPart(int type, bool notify) = 0;
		//////////////////////////////////////////////////////////////////////////

        virtual void GoToCar() = 0;
        virtual void GoToHall() = 0;

		virtual void DoVrProcess() = 0;
        //It's used in multithread rendering mode.
        //return 0 if encounter quit command or context isn't initialize.
        virtual int RenderFrameC() = 0;
        virtual bool InitializeForRenderFrameC() = 0;
        
        virtual void SendVrMessage(const char* msg, int delay_frame) = 0;
		virtual void SendVrMessage(const char* msg, MsgParaValue *arr, int arr_length, int delay_frame) = 0;

		virtual void SetSceneFilePath(const char* scene_path) = 0;
		//virtual void SetResousePath(const char* res_path) = 0;
		//virtual void SetDataBasePath(const char* db_path) = 0;
        
		virtual void SetDecorationVrCallBack(IDecorationVrCallBack* callback) = 0;

		virtual void GestureDetector(int gesture_type, int gesture_state, float x, float y) = 0;

		virtual void OnTouch(int finger_id, int x, int y) = 0;

		//virtual int GetStereoMode() = 0;
		//virtual void SetStereoMode(int val) = 0;

		//virtual float GetEyeDistance() = 0;
		//virtual void SetEyeDistance(float val) = 0;

		//virtual float GetFocusLength() = 0;
		//virtual void SetFocusLength(float val) = 0;

		//virtual void SetDefaultCameraAspectRatio(float ratio) = 0;
		//virtual void ChangeCameraFace(int/*CameraPos*/ pos) = 0;
		//virtual bool CreateSlaveRenderWindow(void* parentWnd) = 0;
		//virtual bool CreateSlaveRenderWindow() = 0;
		//virtual bool DestroySlaveRenderWindow() = 0;

		virtual void SetSnapPos(int pos) = 0;

		virtual void ResizeRender(int w, int h) = 0;
        
        //virtual bool EnterMojing() = 0;
        //virtual void ExitMojing() = 0;

		virtual void RenderLargeImage(const char* path, float width, float height) = 0;
		virtual void RenderPanoramaImages(const char*  path) = 0;
		virtual void RenderPanoramaImages(const char* path, bool is_plat) = 0;
		virtual void RenderLargeImageWithFixedViewPos(int width, int height, int fixed_view_pos) = 0;
		virtual void Print(const char* path)  = 0;

		////判断指定类型和ID的部件是否存在
		//virtual bool IsExistedOfPartId(int part_type, int64 part_id) = 0;

		virtual IDecorationVrArrayVisitor* GetSceneArrayVisitor(const char* name) = 0;

		virtual void ReleaseBuffer(const void* buf) = 0;

		///展示工具接口 add by xqxqx 2018-1-8
		virtual void SetPartByGoods(int part_type, int64 goods_id, bool notify) = 0;
		virtual void SetMaterialByGoods(int part_type, int64 goods_id, bool notify) = 0;
		virtual void SetDownloadServerURL(const char *url, const char *down_file_root) = 0;
		virtual void SetLoginInfo(int64 nLibraryId, int nLangId, int64 nUserId) = 0;
		virtual void SetContentString(const char* content) = 0;
		//设置是否为单机版，默认为网络版
		virtual void SetIsStandalongVersion(bool val) = 0;

		/////////////////////Get Visitor
		virtual int64 GetCurrentPartId(int part_type) = 0;
		virtual const char* GetCurrentPartName(int part_type) = 0;
		virtual int64 GetCurrentMaterialId(int part_type) = 0;
		virtual int GetHandrailPos() = 0;
		virtual int GetCopButtonCount() = 0;
		virtual int64 GetSubitemMaterial(int part_type, int part_mark) = 0;
		virtual int GetElectricPartOrientation(int part_type) = 0;
		virtual float GetElectricPartPosX(int part_type) = 0;
		virtual float GetElectricPartPosY(int part_type) = 0;
		virtual int GetElevatorSizeId() = 0;
		virtual ElevatorSpecification* GetCurrentElevatorSpecification() = 0;
		virtual int GetCarHeight() = 0;
		virtual int GetWallElemCount(int part_type) = 0;

		virtual void OnExternalDeviceLost() = 0;
		virtual void OnExternalDeviceReCreate(void* hwnd, int w, int h) = 0;

		virtual void OnPlatformAsyncCallBack(int function_code, int wparam, int lparam, const char *extra_data) = 0;
		virtual void SetPlatformOperation(void* operation) = 0;
        
        virtual void* GetPluginInterfaceObject(const char *guid_text) = 0;

		virtual void WaitRenderThreadQuited() = 0;
		virtual void SignalRenderThreadQuited() = 0;

		//ParaJsonObject：{ ParaName:String, ParaType:Int, ParaIntValue:Int, ParaFloatValue:Float, ParaStrValue:String}
		//ParaType
		//MPVT_INT32 = 0,
		//MPVT_FLOAT32 = 2,
		//MPVT_STRING = 4,
		//for example
		//[
		//{"ParaName":"p1", "ParaType":1, "ParaIntValue":0, "ParaFloatValue":0, "ParaStrValue":""},
		//{"ParaName":"p2", "ParaType":2, "ParaIntValue":0, "ParaFloatValue":1.0, "ParaStrValue":""}
		//]
		virtual bool SendVrMessageWithParas(const char *msg, const char *param_json_text, int json_text_len, int delay_frame) = 0;
		/*virtual void ExitRenderThread() = 0;

		virtual void SetLogMark(int mark) = 0;
		virtual void PrintLogByMark(int mark, const char *fold_path) = 0;
		virtual void SetValidCounterRang(bool isEnable, int min, int max) = 0;
		virtual void StopLogCallStack() = 0;*/

		//2021.01.13
		//获取cop, auxcop, hcop型式
		//一体式：100
		//外挂式：200
		//嵌入式：500
		virtual int GetCopPanelType(int part_type) = 0;

		virtual void RenderSmallImage(const char* path, float width, float height) = 0;

	};
}
