#ifndef _VR_EscaCombVisitor_H_
#define _VR_EscaCombVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Comb.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	
	class VrEscaCombVisitor : public BaseVisitor
	{
	public:
		VrEscaCombVisitor();
		virtual ~VrEscaCombVisitor();

		DEFINE_CREATE_FUN(VrEscaCombVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<esca::Sel_Esca_Comb> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif //endif
