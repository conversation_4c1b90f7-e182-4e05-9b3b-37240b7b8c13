#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Sel_RGB_Share : public DGBaseVisitor
		{
		public:
			Sel_RGB_Share(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			int Getbackground_r(int row);
			void Setbackground_r(int row, int val);

			int Getbackground_g(int row);
			void Setbackground_g(int row, int val);

			int Getbackground_b(int row);
			void Setbackground_b(int row, int val);

		};
	}

}