#include "stdafx.h"
#include "ConfigAnalyzer.h"
#include "VrGlobalInfo.h"

namespace decoration_vr_interface
{
	decoration_vr_interface::IConfigPtr ConfigAnalyzer::GetConfig(int part_type)
	{
		auto parttype_manager = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
		auto part_type_info = parttype_manager->GetTypeInfo(part_type);
		switch ((CategoryType)part_type_info->category_)
		{
		case CarType:
		case PartTypeAuxCop:
		case PartTypeCop:
		case PartTypeHDCop:
		case PartTypeAuxHDCop:
			return GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentCarConfig();
		case HallType:
		case PartTypeLop:
			return GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentHallConfig();
		default:
			break;
		}

		return GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentEscalatorConfig();
	}

	IElevatorPart* ConfigAnalyzer::GetPart(int part_type)
	{
		auto parttype_manager = GlobalInfoDataCommon::Instance()->GetPartTypeManager();
		//auto current_id = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorId();
		auto config = GetConfig(part_type);
		
		return config->GetPart(part_type);
	}

	void ConfigAnalyzer::AddChange(VrChangeArg* arg)
	{
		VrGlobalInfo::Instance()->get_vr_controller()->AddChange(*arg);
	}

}