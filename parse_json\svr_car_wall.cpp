#include "stdafx.h"
#include "svr_car_wall.h"

#include "svr_json_helper.h"

namespace svr_data
{
	bool SvrWallConstructData::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		if (!GetJsonValue(jv["elevatorSizeId"], ElevatorSizeId))
		{
			return false;
		}

		if (!ParseWallElems(jv["elems"]))
		{
			return false;
		}

		return true;
	}

	bool SvrWallConstructData::ParseWallElems(const Json::Value& jv)
	{
		auto size = jv.size();
		Elems.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!Elems[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrWallData::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		if (!basic_info_.Parse(jv["basicInfo"]))
		{
			return false;
		}

		if (!ParseWallWallSizeDataList(jv["wallSizeDataList"]))
		{
			return false;
		}

		if (!ParseWallDatas(jv["data"]))
		{
			return false;
		}

		return true;
	}

	bool SvrWallData::ParseWallWallSizeDataList(const Json::Value& jv)
	{
		auto size = jv.size();
		WallSizeDataList.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!WallSizeDataList[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrWallData::ParseWallDatas(const Json::Value& jv)
	{
		auto size = jv.size();
		Data.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!Data[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

	bool SvrExhibitWall::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		if (!WallInfo.Parse(jv["walls"]))
		{
			return false;
		}

		if (!ParseMaterialInfos(jv["materialInfos"]))
		{
			return false;
		}

		return true;

	}

	bool SvrExhibitWall::ParseMaterialInfos(const Json::Value& jv)
	{
		auto size = jv.size();
		MaterialInfos.resize(size);
		for (auto i = 0; i < size; ++i)
		{
			if (!MaterialInfos[i].Parse(jv[i]))
			{
				return false;
			}
		}

		return true;
	}

}
