#include "stdafx.h"
#include "Sel_Esca_Access_Cover_ExpendType.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		bool Sel_Esca_Access_Cover_ExpendType::Getbool_select(int row)
		{
			return visitor_->GetElementBool(row, 0);
		}

		void Sel_Esca_Access_Cover_ExpendType::Setbool_select(int row, bool val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		rse::string Sel_Esca_Access_Cover_ExpendType::Getstr_Path(int row)
		{
			const char* val = visitor_->GetElementString(row, 1);
			rse::string ret = val;
			fnGetLigMgr()->ReleaseLibBuf(val);
			return ret;
		}

		void Sel_Esca_Access_Cover_ExpendType::Setstr_Path(int row, const tchar* val)
		{
			visitor_->SetElementValue(row, 1, val);
		}
	}


}