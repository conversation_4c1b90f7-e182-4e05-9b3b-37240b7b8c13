#include "stdafx.h"
#include "GlobalInfoDataCommon.h"
#include "PartTypeManager.h"
#include "ElevatorConfigManager.h"
#include "ElevatorPartFactoryManager.h"
#include "download_visitor.h"
#include "db_visitor.h"
#include "VrGlobalInfo.h"
#include "IDGContextEx.h"
#include <mutex>

namespace decoration_vr_interface
{
	GlobalInfoDataCommon::GlobalInfoDataCommon()
		: is_stand_along_version_(false)
		, elevator_type_(kConstTypeElevator)
		, handrail_pos_(-1), cur_part_type_(-1), opposite_door_(-1)
	{

	}

	GlobalInfoDataCommon::~GlobalInfoDataCommon()
	{
	
	}

	static GlobalInfoDataCommon* g_GlobalInfoDataCommon;

	void GlobalInfoDataCommon::Create()
	{
		if (!g_GlobalInfoDataCommon)
		{
			g_GlobalInfoDataCommon = rs_new0(GlobalInfoDataCommon);
		}
	}

	void GlobalInfoDataCommon::Destory()
	{
		if (g_GlobalInfoDataCommon)
		{
			rs_delete(g_GlobalInfoDataCommon);
		}

		g_GlobalInfoDataCommon = nullptr;
	}

	void GlobalInfoDataCommon::Initialize()
	{
		if (!part_type_manager_)
		{
			auto part_type_mgr = RSE_MAKE_SHARED<PartTypeManager>();
			part_type_mgr->Initialize();
			part_type_manager_ = part_type_mgr;
		}
		if (!elevator_config_manager_)
		{
			elevator_config_manager_ = RSE_MAKE_SHARED<ElevatorConfigManager>();
		}
		if (!elevator_part_factory_manager_)
		{
			auto elevator_part_factory_manager = RSE_MAKE_SHARED<ElevatorPartFactoryManager>();
			elevator_part_factory_manager->Initialize();
			elevator_part_factory_manager_ = elevator_part_factory_manager;
		}

		if (!download_visitor_ptr_) download_visitor_ptr_ = RSE_MAKE_SHARED<DownloadVisitor>();
		if (!db_visitor_ptr_) db_visitor_ptr_ = RSE_MAKE_SHARED<DbVisitor>();
		if(!login_info_ptr_) login_info_ptr_ = RSE_MAKE_SHARED<LoginInfo>();

		if (!down_load_assist_)
		{
			down_load_assist_ = RSE_MAKE_SHARED<DownloadAssist>();
		}

		if (!file_pool_)
		{
			file_pool_ = RSE_MAKE_SHARED<FilePool>();
		}

		if (!material_channel_pool_)
		{
			material_channel_pool_ = RSE_MAKE_SHARED<MaterialChannelPool>();
		}

		if (!lru_cache_)
		{
			lru_cache_ = RSE_MAKE_SHARED<util::lru_cache>();
		}

		auto lru_file = Util::CombinePath(VrGlobalInfo::Instance()->get_config_info()->resouseFilePath, TSTR("lru.bin"));
#if defined(RENDER_SKETCHER_HTML)
		lru_cache_.get()->set_capacity(1024 * 1024 * 256);
#elif defined(RENDER_SKETCHER_ANDROID) || defined(RENDER_SKETCHER_APPLE)
		lru_cache_.get()->set_capacity(1024 * 1024 * 1024);
#endif
		lru_cache_.get()->set_config_file(lru_file);
		lru_cache_.get()->load();
		lru_cache_.get()->deleter = [](const rse::string& file, int32_t file_size)
		{
			auto digest_file = file + ".digital";
			if (remove(file.c_str()) == 0)
			{
				LOGI("lru remove(%s)\n", file.c_str());
			}
			if (remove(digest_file.c_str()) == 0)
			{
				LOGI("lru remove(%s)\n", digest_file.c_str());
			}
		};
	}

	GlobalInfoDataCommon* GlobalInfoDataCommon::Instance()
	{
		return g_GlobalInfoDataCommon;
	}

	void GlobalInfoDataCommon::SetWebServiceSite(const tchar *data_site, const tchar *down_site)
	{
		data_site_host_url_ = Util::CombinePath(data_site, TSTR("/"), TSTR("/"));
		
		download_site_host_url_ = Util::CombinePath(down_site, TSTR("/"), TSTR("/"));
	}

	const tstring& GlobalInfoDataCommon::GetDataSiteUrl()
	{
		return data_site_host_url_;
	}
	const tstring& GlobalInfoDataCommon::GetDownloadSiteUrl()
	{
		return download_site_host_url_;
	}

	LoginInfo* GlobalInfoDataCommon::GetLoginInfo()
	{
		if(login_info_ptr_) return login_info_ptr_.get();

		return nullptr;
	}
	void GlobalInfoDataCommon::SetLoginInfo(int64 lib_id, int64 user_id, int lang_id)
	{
		if (login_info_ptr_)
		{
			login_info_ptr_->library_id_ = lib_id;
			login_info_ptr_->user_id_ = user_id;
			login_info_ptr_->lang_id_ = lang_id;
		}
	}

	void GlobalInfoDataCommon::Finalize()
	{
		lru_cache_.get()->save();
		lru_cache_.get()->clear();
		lru_cache_.reset();
		elevator_config_manager_->Finalize();
        login_info_ptr_.reset();
        db_visitor_ptr_.reset();
        download_visitor_ptr_.reset();
        elevator_part_factory_manager_.reset();
		elevator_config_manager_->Finalize();
        elevator_config_manager_.reset();
		part_type_manager_.reset();
		down_load_assist_.reset();
		file_pool_.reset();
		material_channel_pool_.reset();
		lru_cache_.reset();
	}

	int GlobalInfoDataCommon::GetElevatorType()
	{
		return elevator_type_;
	}
	void GlobalInfoDataCommon::SetElevatorType(int elev_type)
	{
		elevator_type_ = elev_type;
	}
	
	void GlobalInfoDataCommon::LogErrorLn(const char* format, ...)
	{
		auto context = GetDGContext();
		if (context)
		{
			va_list valist;
			va_start(valist, format);

			char buf[2048];
			int buf_size = 2048;
#ifdef RENDER_SKETCHER_WINDOWS		 
			int ret = _vsnprintf_s(buf, buf_size, _TRUNCATE, format, valist);
#else
			int ret = vsnprintf(buf, buf_size, format, valist);
#endif
			if (ret < 0 || ret > buf_size - 1) ret = buf_size - 1;

			buf[ret] = '\0';

			va_end(valist);

			context->LogErrorLn(buf);

			//send error to host wnd
			auto vr_call_back = GetVrCallBack();
			if (vr_call_back && vr_call_back->GetReceiveProgress())
			{
				vr_call_back->OnProgress(0, 0, buf);
			}
		}
	}

	void GlobalInfoDataCommon::ReportInfoToHost(const char* format, ...)
	{
		auto vr_call_back = GetVrCallBack();
		if (vr_call_back && vr_call_back->GetReceiveProgress())
		{
			va_list valist;
			va_start(valist, format);

			char buf[2048];
			int buf_size = 2048;
#ifdef RENDER_SKETCHER_WINDOWS		 
			int ret = _vsnprintf_s(buf, buf_size, _TRUNCATE, format, valist);
#else
			int ret = vsnprintf(buf, buf_size, format, valist);
#endif
			if (ret < 0 || ret > buf_size - 1) ret = buf_size - 1;

			buf[ret] = '\0';

			va_end(valist);

			vr_call_back->OnProgress(0, 0, buf);
		}
	}

	void GlobalInfoDataCommon::SetIsStandalongVersion(bool v)
	{
		is_stand_along_version_ = v;
	}

	bool GlobalInfoDataCommon::IsStandalongVersion()
	{
		return is_stand_along_version_; 
	}
	bool GlobalInfoDataCommon::IsOppositeDoor()
	{
		return opposite_door_ == 1;
	}
}
