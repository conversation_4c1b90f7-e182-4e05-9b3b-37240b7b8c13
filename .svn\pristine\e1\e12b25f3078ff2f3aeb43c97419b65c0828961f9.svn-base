#include "stdafx.h"
#include "Global_Step_Parameter.h"


namespace decoration_vr_interface
{
	namespace esca
	{
		float Global_Step_Parameter::GetRung_Size_X1(int row)
		{
			return visitor_->GetElementFloat(row, 0);
		}

		void Global_Step_Parameter::SetRung_Size_X1(int row, float val)
		{
			visitor_->SetElementValue(row, 0, val);
		}

		float Global_Step_Parameter::GetRung_Size_X2(int row)
		{
			return visitor_->GetElementFloat(row, 1);
		}

		void Global_Step_Parameter::SetRung_Size_X2(int row, float val)
		{
			visitor_->SetElementValue(row, 1, val);
		}

		float Global_Step_Parameter::GetRung_Size_Y(int row)
		{
			return visitor_->GetElementFloat(row, 2);
		}

		void Global_Step_Parameter::SetRung_Size_Y(int row, float val)
		{
			visitor_->SetElementValue(row, 2, val);
		}

	}


}