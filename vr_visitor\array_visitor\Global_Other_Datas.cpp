#include "stdafx.h"
#include "Global_Other_Datas.h"


namespace decoration_vr_interface
{

	float Global_Other_Datas::GetFrontSkirtingHeight(int row)
	{
		return visitor_->GetElementFloat(row, 0);
	}

	void Global_Other_Datas::SetFrontSkirtingHeight(int row, float val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	int Global_Other_Datas::GetElevatorModel(int row)
	{
		return visitor_->GetElementFloat(row, 1);
	}

	void Global_Other_Datas::SetElevatorModel(int row, int val)
	{
		visitor_->SetElementValue(row, 1, val);
	}
}