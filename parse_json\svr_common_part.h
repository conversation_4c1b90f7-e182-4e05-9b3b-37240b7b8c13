#ifndef _SVR_COMMON_PART_H_
#define _SVR_COMMON_PART_H_

#include <vector>

#include "svr_base_data.h"

#include "svr_part_basic_info.h"
#include "svr_part_material.h"
#include "svr_part_model_info.h"
#include "svr_file_digital_info.h"
#include "svr_material.h"

namespace svr_data
{
	class SvrCommonPart : public ISvrBaseData
	{
	public:
		bool Parse(const Json::Value& jobj) override;

	public:
		SvrPartBasicInfo basic_info_;
		rse::vector<SvrPartMaterial> material_infos_;
		rse::vector<SvrPartModelInfo> model_infos_;
		rse::vector<SvrFileDigitalInfo> file_digitals_;

	private:
		bool ParseMaterialInfos(const Json::Value& jobj);
		bool ParseModelInfos(const Json::Value& jobj);
		bool ParseFileDigitals(const Json::Value& jobj);
	};

	class SvrExhibitCommonPart : public ISvrBaseData
	{
	public:
		bool Parse(const Json::Value& jobj) override;

	public:
		SvrCommonPart part_info_;
		rse::vector<SvrCommonPart> child_parts_;
		rse::vector<SvrMaterialInfo> material_infos_;
		rse::vector<int> partType_has_reflect_;
	private:
		bool ParseChildParts(const Json::Value& jobj);
		bool ParseMaterialInfos(const Json::Value& jobj);
		bool ParsePartTypeHasReflect(const Json::Value& jv);
	};
}
#endif
