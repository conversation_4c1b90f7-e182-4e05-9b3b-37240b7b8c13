//
//  Global_Setting_Hall.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Global_Setting_Hall : public DGBaseVisitor
{
public:
	Global_Setting_Hall(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	int Getcar_Number(int row);
	void Setcar_Number(int row, int val);

	float Getcar_Size_X(int row);
	void Setcar_Size_X(int row, float val);

	float Getcar_Size_Z(int row);
	void Setcar_Size_Z(int row, float val);

	int Getdoor_Type(int row);
	void Setdoor_Type(int row, int val);

	float Getdistance(int row);
	void Setdistance(int row, float val);

	float Gethalldoor_SIze_X(int row);
	void Sethalldoor_SIze_X(int row, float val);

	float Gethalldoor_SIze_Y(int row);
	void Sethalldoor_SIze_Y(int row, float val);

};
}