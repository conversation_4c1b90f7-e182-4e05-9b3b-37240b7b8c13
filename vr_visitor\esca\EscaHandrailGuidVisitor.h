#ifndef _VR_EscaHandrailGuidVisitor_H_
#define _VR_EscaHandrailGuidVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Handrail_Guid.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaHandrailGuidVisitor : public BaseVisitor
	{
	public:
		VrEscaHandrailGuidVisitor();
		virtual ~VrEscaHandrailGuidVisitor();

		DEFINE_CREATE_FUN(VrEscaHandrailGuidVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Handrail_Guid> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
