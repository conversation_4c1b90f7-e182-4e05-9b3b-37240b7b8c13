#pragma once

#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
	namespace esca
	{
		class Global_Escalators_Rung_Parameter : public DGBaseVisitor
		{
		public:
			Global_Escalators_Rung_Parameter(IDGSceneEx* scene, const tchar* arr_name)
				:DGBaseVisitor(scene, arr_name) {}

			bool GetSelect(int row);
			void SetSelect(int row, bool val);

			int Getstates(int row);
			void Setstates(int row, int val);

		};
	}

}