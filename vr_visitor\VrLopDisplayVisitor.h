#pragma once
#include "BaseVisitor.h"

namespace decoration_vr_interface
{
class Sel_LopLcd;
class MaterialChannel;

class VrLopDisplayVisitor : public BaseVisitor
{
public:
	VrLopDisplayVisitor();
	~VrLopDisplayVisitor();

	DEFINE_CREATE_FUN(VrLopDisplayVisitor);
	virtual void Initialize() override;
	virtual bool UpdateVr(VrChangeArg* vr_change) override;
	virtual void PrintData(const tchar* file_name) override;

protected:

	std::shared_ptr<Sel_LopLcd> model_;
};
}
