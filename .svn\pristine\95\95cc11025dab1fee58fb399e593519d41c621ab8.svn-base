//
//  Sel_Bottom_Accessory.cpp
//  VrVisitor
//
//  Created by <PERSON> on 2019-01-12 13:04:05.//. All rights reserved.
//



#include "stdafx.h"
#include "Sel_Bottom_Accessory.h"


namespace decoration_vr_interface
{
	bool Sel_Bottom_Accessory::GetIsHave(int row)
	{
		return visitor_->GetElementBool(row, 0);
	}

	void Sel_Bottom_Accessory::SetIsHave(int row, bool val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	rse::string Sel_Bottom_Accessory::GetPath(int row)
	{
		const char* val = visitor_->GetElementString(row, 1);
		rse::string ret = val;
		fnGetLigMgr()->ReleaseLibBuf(val);
		return ret;
	}

	void Sel_Bottom_Accessory::SetPath(int row, const tchar* val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	bool Sel_Bottom_Accessory::GetIsBoth(int row)
	{
		return visitor_->GetElementBool(row, 2);
	}

	void Sel_Bottom_Accessory::SetIsBoth(int row, bool val)
	{
		//目前都为false，表示只用一种；为true，代表全都使用
		visitor_->SetElementValue(row, 2, false);
	}
}