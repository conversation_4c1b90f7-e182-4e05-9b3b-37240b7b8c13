#include "stdafx.h"
#include "VrGlobalInfo.h"
#include "esca_elevator_part.h"

#include "EscaSideCladdingLightingVisitor.h"

namespace decoration_vr_interface
{

	VrEscaSideCladdingLightingVisitor::VrEscaSideCladdingLightingVisitor()
	{
		part_type_ = PartTypeEscaSideCladdingLighting;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrEscaSideCladdingLightingVisitor::~VrEscaSideCladdingLightingVisitor()
	{

	}

	void VrEscaSideCladdingLightingVisitor::Initialize()
	{
		auto scene = VrGlobalInfo::Instance()->get_dg_scene();
		auto type_info = GlobalInfoDataCommon::Instance()->GetPartTypeManager()->GetTypeInfo(part_type_);

		if (!model_)
		{
			model_ = RSE_MAKE_SHARED<esca::Sel_Esca_Side_Cladding_Lighting>(scene, type_info->model_array_.c_str());
		}
		else
		{
			model_->init(scene, type_info->model_array_.c_str());
		}
	}

	bool VrEscaSideCladdingLightingVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (!vr_change)
		{
			return false;
		}

		auto part = static_cast<EscaScenes*>(vr_change->config_->GetPart(part_type_));

		if (model_->GetRowCount() < 1)
		{
			model_->AddRow();
		}

		if (part && part->GetPartId() != 0)
		{
			model_->Setbool_Select(0, true);

			auto dir = VrGlobalInfo::Instance()->get_config_info()->get_model_dir();
			auto model_file = part->LoadModel();
			auto model_path = Util::CombinePath(dir, model_file);
			AddFileToModelCache(part->GetPartType(), part->GetPartId(), model_file, model_path);
			model_->Setstr_Path(0, model_path.c_str());
		}
		else
		{
			model_->Setbool_Select(0, false);
		}

		return true;
	}

	void VrEscaSideCladdingLightingVisitor::PrintData(const tchar* file_name)
	{
		model_->PrintData(file_name);
	}

}
