#include "stdafx.h"
#include "VrGlobalInfo.h"
#include "Global_Esca_Setting_Parameter.h"
#include "EscaConfigVisitor.h"

namespace decoration_vr_interface
{

	VrEscalatorConfigVisitor::VrEscalatorConfigVisitor()
	{
		part_type_ = PartTypeEscalatorConfig;
		available_parttypeid_list_.push_back(part_type_);
	}

	VrEscalatorConfigVisitor::~VrEscalatorConfigVisitor()
	{

	}

	void VrEscalatorConfigVisitor::Initialize()
	{

	}

	bool VrEscalatorConfigVisitor::UpdateVr(VrChangeArg* vr_change)
	{
#if defined(ESCALATOR)
		auto size = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentEscalatorSize();
		auto setting_parameters = VrGlobalInfo::Instance()->get_esca_setting_parameter_array_visitor();
		setting_parameters->SetStepWidth(0, size->width_);
		setting_parameters->SetVertHeight(0, size->height_);
		setting_parameters->SetmodelPath(0, VrGlobalInfo::Instance()->get_config_info()->get_model_dir().c_str());
#endif
		return true;
	}

	void VrEscalatorConfigVisitor::PrintData(const tchar* file_name)
	{

	}

}
