﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="netWork">
      <UniqueIdentifier>{cecb5dd0-a618-4986-9e30-2a45f27cee29}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{3942f974-b669-472f-a611-007f895c6fd1}</UniqueIdentifier>
    </Filter>
    <Filter Include="part">
      <UniqueIdentifier>{8b9cb978-65f9-41e0-a1ad-07decdd46152}</UniqueIdentifier>
    </Filter>
    <Filter Include="part_operator">
      <UniqueIdentifier>{840312cd-42bc-485f-b7e0-2a547735121d}</UniqueIdentifier>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{2b78ec60-1444-4279-b0b1-cd8cc76b55fd}</UniqueIdentifier>
    </Filter>
    <Filter Include="源文件">
      <UniqueIdentifier>{a38cafef-b0a2-4788-acad-589a8911cc9c}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor">
      <UniqueIdentifier>{07cabf5b-ef10-4083-96fc-4ded88f1ee25}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor\array_visitor">
      <UniqueIdentifier>{d2c37971-ada8-4c7c-95f4-b204f07d8d25}</UniqueIdentifier>
    </Filter>
    <Filter Include="jsoncpp">
      <UniqueIdentifier>{5eed5eb8-02f3-4d75-bf1a-4211d32ef292}</UniqueIdentifier>
    </Filter>
    <Filter Include="parse_string">
      <UniqueIdentifier>{9961c0ba-2cc8-47b9-bafb-4cb31228f9ca}</UniqueIdentifier>
    </Filter>
    <Filter Include="parss_json">
      <UniqueIdentifier>{3a73d355-a213-43d8-9b15-3ecb21ee8966}</UniqueIdentifier>
    </Filter>
    <Filter Include="util">
      <UniqueIdentifier>{49a3411a-349b-4cda-98e3-9f2bbce8deca}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor\array_visitor\esca">
      <UniqueIdentifier>{b0a1e8c5-89ca-44ac-9c7e-0741d003a5b4}</UniqueIdentifier>
    </Filter>
    <Filter Include="part\esca">
      <UniqueIdentifier>{f7ad7a3f-546d-4449-b6d2-bd028cf53ebf}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr_visitor\esca">
      <UniqueIdentifier>{4f7fcba9-a3ac-441a-bb54-f7659f63110d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="netWork\HttpAsyncManage.h">
      <Filter>netWork</Filter>
    </ClInclude>
    <ClInclude Include="include\decoration_vr_interface_lib.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_array_visitor.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_callback.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_interface.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_decoration_vr_interface_extend.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\MsgParaValue.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\part_type.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="ConfigAnalyzer.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ConstPartInnerParams.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ConstValueMap.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="decoration_vr_interface_include.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ElevatorConfig.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ElevatorConfigManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ElevatorPartFactoryManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="GlobalInfoDataCommon.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="IConfig.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="IElevatorConfig.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="IElevatorPart.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="IElevatorPartFactory.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PartOperatorManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PartTypeManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Util.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="BaseVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\IVrVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\vr_controller.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\vr_visitor_factory.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrAccessoryVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrBottomVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarConfigVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarDoorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarIndicatorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarWallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrChangeArg.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrConfigInfo.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCopDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCopVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrFrontWallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrGlobalInfo.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallConfigVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallDoorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallIndicatorDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallIndicatorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHandrailVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrJambVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLanternDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLanternVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLopDisplayVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrLopVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrMirrorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrSightseeingVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrTopVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\DGBaseVisitor.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Hwndmsg.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Loading_Scence.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Car.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Hall.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Setting_Parameter.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\material_channel.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\ModelCarWallElem.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Accessory.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_CarIndicator.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Ceiling.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Cop.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_CopLcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Hall.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HandRail.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HI.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HILcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HL.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HLLcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Jamb.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Lop.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_LopLcd.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Mirror.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="BasePartOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="ConfigOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="ElectricOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="common_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="electric_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="electric_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="ElevatorSize.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="material_channel_pool.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Select.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="car_top.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_top_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_wall_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="car_wall_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="decoration_vr_interface.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="common_part_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="FrontWallTypeOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="ElevatorPartOperator.h">
      <Filter>part_operator</Filter>
    </ClInclude>
    <ClInclude Include="download_visitor.h">
      <Filter>netWork</Filter>
    </ClInclude>
    <ClInclude Include="IDownloadVisitor.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="IDbVisitor.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="db_visitor.h">
      <Filter>netWork</Filter>
    </ClInclude>
    <ClInclude Include="skirting_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="skirting_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="config_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="file_pool.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\allocator.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\assertions.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\autolink.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\config.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\features.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\forwards.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\json.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\reader.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\value.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\version.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\include\json\writer.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="jsoncpp\src\lib_json\json_tool.h">
      <Filter>jsoncpp</Filter>
    </ClInclude>
    <ClInclude Include="config_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="decoration_vr_array_visitor.h">
      <Filter>源文件</Filter>
    </ClInclude>
    <ClInclude Include="elevator_size_parser.h">
      <Filter>parse_string</Filter>
    </ClInclude>
    <ClInclude Include="arg_cache.h">
      <Filter>parse_string</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\BaseFactory.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_car_wall.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_common_part.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_config.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_material.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_base_data.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_part_basic_info.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_part_material.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_material_special_rule.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_material_channel.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_wall_size_info.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_wall_element.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_part_model_info.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_file_digital_info.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="parse_json\svr_json_helper.h">
      <Filter>parss_json</Filter>
    </ClInclude>
    <ClInclude Include="util\sha256.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="util\lru_cache.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="decoration_vr_callback.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="include\i_html_decoration_vr_callback.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\i_html_decoration_vr_interface.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="html_decoration_vr_interface.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="i_decoration_vr_bind.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="dvi_tinyxml2.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Camera_EffectParameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Camera_Position.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Control_Flag.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_CopyObject.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Esca_PartMark.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Esca_Setting_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Escalators_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Escalators_Rung_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_PartType_Info.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Script_Floor.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Step_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Step0_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_StepLight0_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_TestNote.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Work0_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Global_Work1_Parameter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_ExpendType.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_Flag.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Background.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Balustrade.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb_Lighting.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking_Ctrl_Box.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Enter.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Guid.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Lighting.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Photoelectric.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Scene.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding_Lighting.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Brush.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Lighting.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Step.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Step_Lighting.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Traffic_Light.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_Esca_Truss_Lighting.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\esca\Sel_RGB_Share.h">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\esca_elevator_part.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\esca_elevator_part_factory.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaAcessOverFactory.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaBalustradeFactory.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaHandrailEnterFactory.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaSideCladdingFactory.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="esca\EscaSkirtLightingFactory.h">
      <Filter>part\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaAccessOverVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaAcessOverExpendTypeVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaAcessOverFlagVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaBalustradeVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaCombLightingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaCombVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaConfigVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaDeckingCtrlBoxVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaDeckingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaHandrailEnterVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaHandrailGuidVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaHandrailLightingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaHandrailVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaPhotoelectricVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaScenesVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaSideCladdingLightingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaSideCladdingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaSkirtBrushVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaSkirtLightingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaSkirtVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaStepLightingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaStepVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaTrafficLightVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\esca\EscaTrussLightingVisitor.h">
      <Filter>vr_visitor\esca</Filter>
    </ClInclude>
    <ClInclude Include="EscalatorSize.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="vr_visitor\array_visitor\Sel_Door_Imported.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Car_Shell.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Shaft.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrCarShellVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallShaftVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="hall_room_part.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="hall_room_part_factory.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="download_assist.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="EscalatorSize.h">
      <Filter>part</Filter>
    </ClInclude>
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="EscalatorSize.h" />
    <ClInclude Include="vr_visitor\VrHallFloorVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallWallVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Global_Other_Datas.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_FireBox.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrHallFireBoxVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\array_visitor\Sel_Emids.h">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClInclude>
    <ClInclude Include="vr_visitor\VrEmidsVisitor.h">
      <Filter>vr_visitor</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="netWork\HttpAsyncManage.cpp">
      <Filter>netWork</Filter>
    </ClCompile>
    <ClCompile Include="ConfigAnalyzer.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ConstValueMap.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ElevatorConfig.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ElevatorConfigManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ElevatorPartFactoryManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="GlobalInfoDataCommon.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PartOperatorManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PartTypeManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Util.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="BaseVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\vr_controller.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\vr_visitor_factory.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrAccessoryVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrBottomVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarConfigVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarDoorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarIndicatorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarWallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrChangeArg.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrConfigInfo.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCopDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCopVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrFrontWallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrGlobalInfo.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallConfigVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallDoorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallIndicatorDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallIndicatorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHandrailVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrJambVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLanternDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLanternVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLopDisplayVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrLopVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrMirrorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrSightseeingVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrTopVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\DGBaseVisitor.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Hwndmsg.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Loading_Scence.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Car.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Hall.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Setting_Parameter.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\material_channel.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\ModelCarWallElem.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Accessory.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_CarIndicator.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Ceiling.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Cop.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_CopLcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Hall.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HandRail.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HI.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HILcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HL.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HLLcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Jamb.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Lop.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_LopLcd.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Mirror.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="BasePartOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="ConfigOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="ElectricOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="ElevatorPartOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="common_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="electric_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="electric_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="ElevatorSize.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="material_channel_pool.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Select.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="car_top.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_top_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_wall_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="car_wall_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="decoration_vr_interface.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="common_part_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="FrontWallTypeOperator.cpp">
      <Filter>part_operator</Filter>
    </ClCompile>
    <ClCompile Include="download_visitor.cpp">
      <Filter>netWork</Filter>
    </ClCompile>
    <ClCompile Include="db_visitor.cpp">
      <Filter>netWork</Filter>
    </ClCompile>
    <ClCompile Include="skirting_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="skirting_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="config_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="file_pool.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="decoration_vr_interface_lib.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_reader.cpp">
      <Filter>jsoncpp</Filter>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_value.cpp">
      <Filter>jsoncpp</Filter>
    </ClCompile>
    <ClCompile Include="jsoncpp\src\lib_json\json_writer.cpp">
      <Filter>jsoncpp</Filter>
    </ClCompile>
    <ClCompile Include="config_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="decoration_vr_array_visitor.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="elevator_size_parser.cpp">
      <Filter>parse_string</Filter>
    </ClCompile>
    <ClCompile Include="arg_cache.cpp">
      <Filter>parse_string</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_car_wall.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_common_part.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_config.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_material.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_part_basic_info.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_part_material.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_material_special_rule.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_material_channel.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_wall_size_info.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_wall_element.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_part_model_info.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_file_digital_info.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="parse_json\svr_json_helper.cpp">
      <Filter>parss_json</Filter>
    </ClCompile>
    <ClCompile Include="util\lru_cache.cpp">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="util\sha256.cpp">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="decoration_vr_callback.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="html_decoration_vr_interface.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="dvi_tinyxml2.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Camera_EffectParameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Camera_Position.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Control_Flag.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_CopyObject.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Esca_PartMark.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Esca_Setting_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Escalators_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Escalators_Rung_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_PartType_Info.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Script_Floor.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Step_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Step0_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_StepLight0_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_TestNote.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Work0_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Global_Work1_Parameter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_ExpendType.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Access_Cover_Flag.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Background.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Balustrade.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Comb_Lighting.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Decking_Ctrl_Box.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Enter.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Guid.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Handrail_Lighting.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Photoelectric.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Scene.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Side_Cladding_Lighting.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Brush.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Skirt_Lighting.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Step.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Step_Lighting.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Traffic_Light.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_Esca_Truss_Lighting.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\esca\Sel_RGB_Share.cpp">
      <Filter>vr_visitor\array_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\esca_elevator_part.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\esca_elevator_part_factory.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaAcessOverFactory.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaBalustradeFactory.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaHandrailEnterFactory.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaSideCladdingFactory.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="esca\EscaSkirtLightingFactory.cpp">
      <Filter>part\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaAccessOverVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaAcessOverExpendTypeVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaAcessOverFlagVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaBalustradeVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaCombLightingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaCombVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaConfigVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaDeckingCtrlBoxVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaDeckingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaHandrailEnterVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaHandrailGuidVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaHandrailLightingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaHandrailVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaPhotoelectricVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaScenesVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaSideCladdingLightingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaSideCladdingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaSkirtBrushVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaSkirtLightingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaSkirtVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaStepLightingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaStepVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaTrafficLightVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\esca\EscaTrussLightingVisitor.cpp">
      <Filter>vr_visitor\esca</Filter>
    </ClCompile>
    <ClCompile Include="EscalatorSize.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Bottom_Accessory.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="vr_visitor\array_visitor\Sel_Door_Imported.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_HallDoor_Imported.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Car_Shell.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Shaft.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrCarShellVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallShaftVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="hall_room_part.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="hall_room_part_factory.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="download_assist.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="EscalatorSize.cpp">
      <Filter>part</Filter>
    </ClCompile>
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="EscalatorSize.cpp" />
    <ClCompile Include="vr_visitor\VrHallFloorVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallWallVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Global_Other_Datas.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_FireBox.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrHallFireBoxVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\array_visitor\Sel_Emids.cpp">
      <Filter>vr_visitor\array_visitor</Filter>
    </ClCompile>
    <ClCompile Include="vr_visitor\VrEmidsVisitor.cpp">
      <Filter>vr_visitor</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="jsoncpp\src\lib_json\json_valueiterator.inl">
      <Filter>jsoncpp</Filter>
    </None>
    <None Include="jsoncpp\src\lib_json\version.h.in">
      <Filter>jsoncpp</Filter>
    </None>
  </ItemGroup>
</Project>