#ifndef _VR_EscaHandrailVisitor_H_
#define _VR_EscaHandrailVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Handrail.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaHandrailVisitor : public BaseVisitor
	{
	public:
		VrEscaHandrailVisitor();
		virtual ~VrEscaHandrailVisitor();

		DEFINE_CREATE_FUN(VrEscaHandrailVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Handrail> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif
