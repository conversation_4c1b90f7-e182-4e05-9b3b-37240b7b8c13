//
//  Global_Hwndmsg.h
//  VrVisitor
//
//  Created by vrprg on 11:32:56.//  Copyright (c) 2015??? vrprg. All rights reserved.
//



#include "stdafx.h"
#include "Global_Hwndmsg.h"


namespace decoration_vr_interface
{

	int Global_Hwndmsg::Gethwnd(int row)
	{
		return visitor_->GetElementInt(row, 0);
	}

	void Global_Hwndmsg::Sethwnd(int row, int val)
	{
		visitor_->SetElementValue(row, 0, val);
	}

	int Global_Hwndmsg::Getmsg(int row)
	{
		return visitor_->GetElementInt(row, 1);
	}

	void Global_Hwndmsg::Setmsg(int row, int val)
	{
		visitor_->SetElementValue(row, 1, val);
	}

	int Global_Hwndmsg::Getcampos(int row)
	{
		return visitor_->GetElementInt(row, 2);
	}

	void Global_Hwndmsg::Setcampos(int row, int val)
	{
		visitor_->SetElementValue(row, 2, val);
	}

	int Global_Hwndmsg::GetPartChange(int row)
	{
		return visitor_->GetElementInt(row, 3);
	}

	void Global_Hwndmsg::SetPartChange(int row, int val)
	{
		visitor_->SetElementValue(row, 3, val);
	}

	int Global_Hwndmsg::GetCam(int row)
	{
		return visitor_->GetElementInt(row, 4);
	}

	void Global_Hwndmsg::SetCam(int row, int val)
	{
		visitor_->SetElementValue(row, 4, val);
	}

	int Global_Hwndmsg::Geterror(int row)
	{
		return visitor_->GetElementInt(row, 5);
	}

	void Global_Hwndmsg::Seterror(int row, int val)
	{
		visitor_->SetElementValue(row, 5, val);
	}

	int Global_Hwndmsg::Getcol6(int row)
	{
		return visitor_->GetElementInt(row, 6);
	}

	void Global_Hwndmsg::Setcol6(int row, int val)
	{
		visitor_->SetElementValue(row, 6, val);
	}

}