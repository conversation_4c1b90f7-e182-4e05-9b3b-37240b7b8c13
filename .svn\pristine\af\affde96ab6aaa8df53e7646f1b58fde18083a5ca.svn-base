#pragma once

#include "IElevatorPartFactory.h"

#include "parse_json/svr_config.h"

namespace decoration_vr_interface
{
	class CarTopFactory : public IElevatorPartFactory
	{
	public:
		CarTopFactory();
		~CarTopFactory();

		virtual int GetPartType() override;

		virtual IElevatorPartPtr CreatePart(FactoryArgs* args) override;

	protected:
		IElevatorPartPtr GetTopFromJsonConfig(FactoryArgs* args);
		IElevatorPartPtr GetTopFromJsonObject(FactoryArgs* args);

		void FillTopBasicInfo(IElevatorPart* top, const svr_data::SvrPartBasicInfo& info);
		void FillTopMaterial(IElevatorPart* top, const  rse::vector<svr_data::SvrPartMaterial>& mats);
	};
}
