﻿//
//  Global_Setting_Parameter.h
//  VrVisitor
//
//  Created by vrprg on 15:40:34.//  Copyright (c) 2015ๅนด vrprg. All rights reserved.
//



#pragma once


#include "DGBaseVisitor.h"

namespace decoration_vr_interface
{
class Global_Setting_Parameter : public DGBaseVisitor
{
public:
	Global_Setting_Parameter(IDGSceneEx* scene, const tchar* arr_name)
		:DGBaseVisitor(scene, arr_name) {}
	bool Getplay_Type(int row);
	void Setplay_Type(int row, bool val);

	int GettextureManager(int row);
	void SettextureManager(int row, int val);

	int Getscreen_X(int row);
	void Setscreen_X(int row, int val);

	int Getscreen_Y(int row);
	void Setscreen_Y(int row, int val);

	int Getcam_Pos(int row);
	void Setcam_Pos(int row, int val);

	int Getcam_Speed(int row);
	void Setcam_Speed(int row, int val);

	float Getcam_High(int row);
	void Setcam_High(int row, float val);

	float Getcam_SavePosition_X(int row);
	void Setcam_SavePosition_X(int row, float val);

	float Getcam_SavePosition_Y(int row);
	void Setcam_SavePosition_Y(int row, float val);

	bool GetkeyControl(int row);
	void SetkeyControl(int row, bool val);

	rse::string GetmodelPath(int row);
	void SetmodelPath(int row, const tchar* val);

	rse::string GettexturePath(int row);
	void SettexturePath(int row, const tchar* val);

	int GetrecordFramerate(int row);
	void SetrecordFramerate(int row, int val);

	int GetrecodCamPosition(int row);
	void SetrecodCamPosition(int row, int val);

	bool GetDisplayMode(int row);
	void SetDisplayMode(int row, bool val);

	bool GetPause(int row);
	void SetPause(int row, bool val);

	bool GetReachedFloorNotify(int row);
	void SetReachedFloorNotify(int row, bool val);

	int GetSetCurFloor(int row);
	void SetSetCurFloor(int row, int val);

	float Getdoorgap(int row);
	void Setdoorgap(int row, float val);

	int Getis_network(int row);
	void Setis_network(int row, int val);

	int GetBuild_Level(int row);
	void SetBuild_Level(int row, int val);

	const tchar* GetPanronamaPath(int row);
	void SetPanronamaPath(int row, const tchar* val);

	int GetProjectType(int row);
	void SetProjectType(int row, int val);

	float GetCameraFov(int row);
	void SetCameraFov(int row, float val);
};
}