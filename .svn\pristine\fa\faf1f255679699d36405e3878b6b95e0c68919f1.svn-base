#include "stdafx.h"
#include "VrCarConfigVisitor.h"
#include "VrGlobalInfo.h"
#include "config_part.h"
#include "electric_part.h"
#include "common_part.h"

namespace decoration_vr_interface
{
	//VrEscalatorConfigVisitor
	VrCarConfigVisitor::VrCarConfigVisitor()
	{
		part_type_ = PartTypeCarConfig;

		available_parttypeid_list_.push_back(PartTypeCarConfig);

	}


	VrCarConfigVisitor::~VrCarConfigVisitor()
	{
	}

	void VrCarConfigVisitor::Initialize()
	{

	}

	bool VrCarConfigVisitor::UpdateVr(VrChangeArg* vr_change)
	{
		if (!vr_change)
		{
			return false;
		}

		ElevatorSize* car_size = vr_change->config_->GetElevatorConfig()->GetElevatorSize();
		int row = 0;

		Global_Setting_Car* car = VrGlobalInfo::Instance()->get_car_array_visitor();
		car->SetCar_Size_X(row, car_size->width_);
		car->SetCar_Size_Z(row, car_size->depth_);
		car->SetCar_Size_Y(row, car_size->height_);

		auto mode = VrGlobalInfo::Instance()->GetRunningMode();
		float w = VrGlobalInfo::Instance()->GetDoorWidth();
		float h = VrGlobalInfo::Instance()->GetDoorHeight();
		if (mode == AR_RUNNING_MODE && w > 1.0f && h > 1.0f)
		{
			car->SetDoor_Size_X(row, w);
			car->SetDoor_Size_Y(row, h);
		}
		else
		{
			car->SetDoor_Size_X(row, car_size->door_width_);
			car->SetDoor_Size_Y(row, car_size->door_height_);
		}
		

		Global_Select* select = VrGlobalInfo::Instance()->get_select_array_visitor();

		//auto cardoor_type_prop = vr_change->config_->GetExtendProperty(EPK_CarDoorType);
		//int cardoor_type = cardoor_type_prop == nullptr ? kCarDoorTypeDefaultValue : cardoor_type_prop->i_;
		//auto cardoor_type = vr_change->config_->GetElevatorConfig()->GetElevatorSetting()->door_type_;
        
        //when setting value of DoorOpenType, the value is stored in PartId of CarDoor Part,
        //we need read this value from it
		//2019.12.10
        //CommonPart* part = static_cast<CommonPart*>(vr_change->config_->GetPart(PartTypeCarDoor));
        //int door_type = part == nullptr ? -1 : (int)part->GetPartId();
        //VrGlobalInfo::Instance()->UpdateDoorType(door_type);
		int door_type = VrGlobalInfo::Instance()->GetDoorType();
		VrGlobalInfo::Instance()->UpdateDoorType(door_type);
        
		//select->SetDoorType(row, cardoor_type);
		select->SetCurrentElevtor(row, GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorId());
// 		auto frontwall_type_prop = vr_change->config_->GetExtendProperty(EPK_FrontWallType);
// 		int frontwall_type = frontwall_type_prop == nullptr ? kFrontWallTypeDefaultValue : frontwall_type_prop->i_;
		auto cop_part = static_cast<ElectricPart*>(vr_change->config_->GetPart(PartTypeCop));
		int frontwall_type = cop_part == nullptr ? kFrontWallTypeDefaultValue : cop_part->GetPanelType();
		select->SetFrontWallType(row, frontwall_type);
		if (GlobalInfoDataCommon::Instance()->IsOppositeDoor())
		{
			select->SetThroughBackWallType(row, frontwall_type);
		}
		else
		{
			select->SetThroughBackDoorType(0, -0);
		}
		//select->SetcarType(row, static_cast<Config*>(vr_change->config_)->GetPartId());//22
		select->SetcarType(row, 100028);//22

		Global_Setting_Parameter* setting_parameters = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		setting_parameters->SetmodelPath(row, VrGlobalInfo::Instance()->get_config_info()->get_model_dir().c_str());

		return true;
	}

	void VrCarConfigVisitor::PrintData(const tchar* file_name)
	{
		Global_Setting_Car* car = VrGlobalInfo::Instance()->get_car_array_visitor();
		car->PrintData(file_name);
		Global_Select* select = VrGlobalInfo::Instance()->get_select_array_visitor();
		select->PrintData(file_name);
		Global_Setting_Parameter* setting_parameter = VrGlobalInfo::Instance()->get_setting_parameter_array_visitor();
		setting_parameter->PrintData(file_name);
	}

}
