#include "stdafx.h"
#include "material_channel_pool.h"

namespace decoration_vr_interface
{
	MaterialChannelPool::MaterialChannelPool()
	{
	}


	MaterialChannelPool::~MaterialChannelPool()
	{
	}

// 	decoration_vr_interface::MaterialChannelPool* MaterialChannelPool::Instance()
// 	{
// 		static MaterialChannelPool ins;
// 		return &ins;
// 	}

	bool MaterialChannelPool::GetMaterialChannel(rse::vector<svr_data::SvrMaterialChannel>& rt, int64 mat_id, int channel_type /*= 0*/)
	{
		auto it = svr_materials_.find(mat_id);
		if (it != svr_materials_.end())
		{
			auto& cs = it->second.channels_;
			auto& rules = it->second.rules_;

			auto baseMaterialId = it->second.basic_info_.IntVal1;
			auto specialType = baseMaterialId > 0 ? baseMaterialId >> 16 : 0;
			bool isSpecialMaterial = specialType != 0;
			if (isSpecialMaterial)
			{
				rse::vector<svr_data::SvrMaterialSpecialRule> special_rules;

				switch (specialType)
				{
				case 1:
				{
					auto es = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize();
					for (auto ir = rules.begin(), ire = rules.end(); ir != ire; ++ir)
					{
						if (Util::Equals(es->width_, ir->ReservedInt2) && Util::Equals(ir->ReservedInt3, es->depth_))
						{
							special_rules.push_back(*ir);
						}
					}
					break;
				}
				case 2:
				{
					//2019.04.24
					auto doorRuleType = it->second.basic_info_.IntVal5;
					if (doorRuleType == 1)
					{
						for (auto ir = rules.begin(), ire = rules.end(); ir != ire; ++ir)
						{
							if (ir->RuleType == 1)
							{
								special_rules.push_back(*ir);
							}
						}
					}
					else
					{
						auto door_type = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->
							GetCurrentElevatorConfig()->GetElevatorSetting()->door_type_;
						int dt = 1;
						switch (door_type)
						{
						case 100:
							dt = 1;
							break;
						case 201:
							dt = 2;
							break;
						case 202:
							dt = 3;
							break;
						case 401:
							dt = 4;
							break;
						}

						auto es = GlobalInfoDataCommon::Instance()->GetElevatorConfigManager()->GetCurrentElevatorSize();
						for (auto ir = rules.begin(), ire = rules.end(); ir != ire; ++ir)
						{
							if (Util::Equals(ir->ReservedInt2, es->door_width_) && Util::Equals(ir->ReservedInt3, es->door_height_)
								&& ir->ReservedInt4 == dt)
							{
								special_rules.push_back(*ir);
							}
						}
						if (special_rules.empty() && (door_type == 201 || door_type == 202))
						{
							for (auto ir = rules.begin(), ire = rules.end(); ir != ire; ++ir)
							{
								if (Util::Equals(ir->ReservedInt2, es->door_width_) && Util::Equals(ir->ReservedInt3, es->door_height_)
									&& ir->ReservedInt4 == 1)
								{
									special_rules.push_back(*ir);
								}
							}
						}
					}
					
					break;
				}
				default:
					break;
				}

				for (int32_t i = 0; i < cs.size(); ++i)
				{
					auto chan_id = cs[i].ChannelId;
					auto find_it = std::find_if(special_rules.begin(), special_rules.end(), [this, &chan_id](svr_data::SvrMaterialSpecialRule &rule)
					{
						return rule.ChannelId == chan_id;
					});

					if (special_rules.end() != find_it)
					{
						rt.push_back(cs[i]);
					}
				}

				for (int32_t i = 0; i < rt.size(); ++i)
				{
					rt[i].ChannelId = i;
				}

				if (rt.size() == 0)
				{
					GlobalInfoDataCommon::Instance()->LogErrorLn(TSTR("VrPartType=%d, mat_id=%lld cann't find any material channels in special case!"), channel_type, mat_id);
				}
			}
			else
			{
				for (int32_t i = 0, sz = cs.size(); i < sz; ++i)
				{
					if (cs[i].PartType == channel_type)
					{
						rt.push_back(cs[i]);
					}
				}

				if (rt.size() <= 0)
				{
					for (int32_t i = 0, sz = cs.size(); i < sz; ++i)
					{
						if (cs[i].PartType == 0)
						{
							rt.push_back(cs[i]);
						}
					}
				}

				if (rt.size() == 0)
				{
					GlobalInfoDataCommon::Instance()->LogErrorLn(TSTR("VrPartType=%d, mat_id=%lld cann't find any material channels in common case!"), channel_type, mat_id);
				}
			}
			
			return true;
		}

		return false;		
	}

	void MaterialChannelPool::AddMaterialInfo(int64 mat_id, const svr_data::SvrMaterialInfo& mat_info)
	{
		svr_materials_[mat_id] = mat_info;
	}

	void MaterialChannelPool::RemoveMaterialInfo(int64 mat_id)
	{
		auto it = svr_materials_.find(mat_id);
		if (it != svr_materials_.end())
		{
			svr_materials_.erase(it);
		}
	}

	void MaterialChannelPool::Clear()
	{
		svr_materials_.clear();
	}

}
