#pragma once
struct VrConfigInfo
{
	VrConfigInfo()
	{
		textures_dir_ = TSTR("Textures/");
		model_dir_ = TSTR("Model");
		shader_dir_ = TSTR("shader_buildin");
		sceneFilePath = TSTR("decoration_test0901.zxrm");
	}

	
	tstring sceneFilePath;
	tstring resouseFilePath;
	tstring get_texture_dir();
	tstring get_model_dir();
	tstring get_shader_dir();
private:
	tstring model_dir_;
	tstring textures_dir_;
	tstring shader_dir_;
};

