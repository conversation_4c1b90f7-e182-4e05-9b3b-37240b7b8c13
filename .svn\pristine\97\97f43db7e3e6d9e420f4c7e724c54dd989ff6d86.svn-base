#include "stdafx.h"
#include "svr_wall_element.h"

#include "svr_json_helper.h"

namespace svr_data
{

	bool SvrWallElement::Parse(const Json::Value& jv)
	{
		if (IsNullJsonValue(jv))
		{
			return true;
		}

		IF_ERROR_RETURN(GetJsonValue(jv["id"], Id));
		IF_ERROR_RETURN(GetJsonValue(jv["posX"], PosX));
		IF_ERROR_RETURN(GetJsonValue(jv["posY"], PosY));
		IF_ERROR_RETURN(GetJsonValue(jv["sizeX"], SizeX));
		IF_ERROR_RETURN(GetJsonValue(jv["sizeY"], SizeY));
		IF_ERROR_RETURN(GetJsonValue(jv["posZ"], PosZ));
		IF_ERROR_RETURN(GetJsonValue(jv["leftSideThick"], LeftSideThick));
		IF_ERROR_RETURN(GetJsonValue(jv["rightSideThick"], RightSideThick));
		IF_ERROR_RETURN(GetJsonValue(jv["topSideThick"], TopSideThick));
		IF_ERROR_RETURN(GetJsonValue(jv["bottomSideThick"], BottomSideThick));
		IF_ERROR_RETURN(GetJsonValue(jv["edgeMark"], EdgeMark));
		IF_ERROR_RETURN(GetJsonValue(jv["cornerRadiusX"], CornerRadiusX));
		IF_ERROR_RETURN(GetJsonValue(jv["cornerRadiusY"], CornerRadiusY));
		IF_ERROR_RETURN(GetJsonValue(jv["cornerRadiusZ"], CornerRadiusZ));
		IF_ERROR_RETURN(GetJsonValue(jv["cornerType"], CornerType));
		IF_ERROR_RETURN(GetJsonValue(jv["gap"], Gap));
		IF_ERROR_RETURN(GetJsonValue(jv["marker"], Marker));
		IF_ERROR_RETURN(GetJsonValue(jv["lightMapMark"], LightMapMark));
		IF_ERROR_RETURN(GetJsonValue(jv["materialId"], MaterialId));

		return true;
	}

}
