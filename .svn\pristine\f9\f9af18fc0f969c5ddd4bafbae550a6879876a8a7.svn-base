#ifndef _VR_EscaCombLightingVisitor_H_
#define _VR_EscaCombLightingVisitor_H_

#include "BaseVisitor.h"
#include "Sel_Esca_Comb_Lighting.h"

namespace decoration_vr_interface
{
	class MaterialChannel;
	class VrEscaCombLightingVisitor : public BaseVisitor
	{
	public:
		VrEscaCombLightingVisitor();
		virtual ~VrEscaCombLightingVisitor();

		DEFINE_CREATE_FUN(VrEscaCombLightingVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;

	protected:
		std::shared_ptr<esca::Sel_Esca_Comb_Lighting> model_;
		std::shared_ptr<MaterialChannel> material_;
	};
}

#endif //endif
