#ifndef _VR_EscaDeckingCtrlBoxVisitor_H_
#define _VR_EscaDeckingCtrlBoxVisitor_H_

#include "BaseVisitor.h"
#include "esca/Sel_Esca_Decking_Ctrl_Box.h"

namespace decoration_vr_interface
{
	class VrEscaDeckingCtrlBoxVisitor : public BaseVisitor
	{
	public:
		VrEscaDeckingCtrlBoxVisitor();
		virtual ~VrEscaDeckingCtrlBoxVisitor();

		DEFINE_CREATE_FUN(VrEscaDeckingCtrlBoxVisitor);

		virtual void Initialize() override;

		virtual bool UpdateVr(VrChangeArg* vr_change) override;
		virtual void PrintData(const tchar* file_name) override;
	protected:
		std::shared_ptr<esca::Sel_Esca_Decking_Ctrl_Box> model_;
	};
}

#endif
